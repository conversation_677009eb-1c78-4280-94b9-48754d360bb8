<template>
	<view>
		<view class="common-header education-header pb16">
			<view class="header-top">
				<view class="common-header-title">{{ partJobVo.title }}</view>
				<view class="commission-info" v-if="partJobVo.commission_detail && partJobVo.commission_detail.calculation" :style="{background: t('color1'), opacity: 0.8}">
					<text class="commission-text" :style="{color: '#fff'}">{{ partJobVo.commission_detail.calculation }}
						<!-- <text :style="{color: '#fff'}" class="commission-label">预计佣金</text> -->
					</text>
				</view>
			</view>
			<view class="rpo-salary">
				<view class="rpo-salary-num">{{ partJobVo.salary }}</view>
			</view>
			<view class="common-list-tag list-tag-box">
				<template v-if="partJobVo.formatted_options">
					<view class="list-tag" v-for="(tag, index) in Object.values(partJobVo.formatted_options).flat()" :key="'format_' + index">
						<view class="ellipsis">{{ tag }}</view>
					</view>
				</template>
				<view class="list-tag" v-if="partJobVo.work_mode">
					<view class="ellipsis">{{ partJobVo.work_mode }}</view>
				</view>
				<view class="list-tag" v-if="partJobVo.work_intensity">
					<view class="ellipsis">{{ partJobVo.work_intensity }}</view>
				</view>
				<view class="list-tag" v-if="partJobVo.work_time_type">
					<view class="ellipsis">{{ partJobVo.work_time_type }}</view>
				</view>
				<view class="list-tag" v-if="partJobVo.payment">
					<view class="ellipsis">{{ partJobVo.payment }}</view>
				</view>
				<view class="list-tag" v-if="partJobVo.education">
					<view class="ellipsis">{{ partJobVo.education }}</view>
				</view>
				<view class="list-tag" v-if="partJobVo.experience">
					<view class="ellipsis">{{ partJobVo.experience }}</view>
				</view>
			</view>
			
			<!-- <view class="anchor-new-company" v-if="partJobVo.company">
				<image :src="partJobVo.company.logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png'" mode="aspectFill"></image>
				<view class="anchor-new-info">
					<view class="name ellipsis">{{ partJobVo.company.name }}</view>
					<view class="desc ellipsis">{{ partJobVo.company.industry }} | {{ partJobVo.company.scale }}</view>
				</view>
			</view> -->
<!-- 
			<view class="anchor-new-place">
				<view class="place-bold">{{ partJobVo.address }}</view>
				<view class="place-normal">{{ partJobVo.province }} {{ partJobVo.city }} {{ partJobVo.district }}</view>
			</view> -->

			<view @tap="skiptoWebview" class="common-safe ptp_exposure_static" data-ptpid="69a4-1137-8c63-fcc7"
				id="pid=69a4-1137-8c63-fcc7" :style="{background: t('color1'), opacity: 0.8}">
				<view class="common-safe-left">
					<view class="iconfont iconsafeguard" :style="{color: t('color2')}"></view>
					
					<view :style="{color: '#fff', fontWeight: 'bold'}">此职位经过官方认证，放心投递+职位无忧</view>
				</view>
				<view class="iconfont iconarrow" :style="{color: t('color2')}"></view>
			</view>
		</view>
	
		<!-- 基本信息 -->
		<view class="common-title" style="margin-top: 48rpx;">基本信息</view>
		<view class="common-box">
			<view class="info-item" v-if="partJobVo.work_address">
				<view class="info-label">工作地点</view>
				<view class="info-content">{{ partJobVo.work_address }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.education">
				<view class="info-label">学历要求</view>
				<view class="info-content">{{ partJobVo.education }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.experience">
				<view class="info-label">经验要求</view>
				<view class="info-content">{{ partJobVo.experience }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.age_requirement">
				<view class="info-label">年龄要求</view>
				<view class="info-content">{{ partJobVo.age_requirement }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.gender_requirement !== undefined">
				<view class="info-label">性别要求</view>
				<view class="info-content">{{ getGenderText(partJobVo.gender_requirement) }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.numbers">
				<view class="info-label">招聘人数</view>
				<view class="info-content">{{ partJobVo.numbers }}人</view>
			</view>
		</view>

		<!-- 工作安排 -->
		<view class="common-title" style="margin-top: 48rpx;">工作安排</view>
		<view class="common-box">
			<view class="info-item" v-if="partJobVo.work_mode">
				<view class="info-label">工作形式</view>
				<view class="info-content">{{ partJobVo.work_mode }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.payment">
				<view class="info-label">结算方式</view>
				<view class="info-content">{{ partJobVo.payment }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.work_time_start || partJobVo.work_time_end">
				<view class="info-label">工作时间</view>
				<view class="info-content">{{ partJobVo.work_time_start }} - {{ partJobVo.work_time_end }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.work_time_type">
				<view class="info-label">班次类型</view>
				<view class="info-content">{{ partJobVo.work_time_type }}</view>
			</view>
			<!-- <view class="info-item" v-if="partJobVo.work_intensity">
				<view class="info-label">工作强度</view>
				<view class="info-content">{{ partJobVo.work_intensity }}</view>
			</view> -->
			<view class="info-item" v-if="partJobVo.rest_time">
				<view class="info-label">休息时间</view>
				<view class="info-content">{{ partJobVo.rest_time }}</view>
			</view>
		</view>

		<!-- 福利待遇 -->
		<view class="common-title" style="margin-top: 48rpx;">福利待遇</view>
		<view class="common-box">
			<view class="info-item" v-if="partJobVo.meal_provided !== undefined">
				<view class="info-label">工作餐</view>
				<view class="info-content">{{ partJobVo.meal_provided === 1 ? '提供' : '不提供' }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.meal_provided === 0 && partJobVo.meal_allowance">
				<view class="info-label">餐费补贴</view>
				<view class="info-content">{{ partJobVo.meal_allowance }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.housing_provided !== undefined">
				<view class="info-label">住宿</view>
				<view class="info-content">{{ getHousingProvidedText(partJobVo.housing_provided) }}</view>
			</view>
			<view class="info-item" v-if="partJobVo.housing_provided === 2 && partJobVo.housing_fee">
				<view class="info-label">住宿费用</view>
				<view class="info-content">{{ partJobVo.housing_fee }}元/月</view>
			</view>
		</view>

		<!-- 其他信息 -->
		<view class="common-title" style="margin-top: 48rpx;">其他福利</view>
		<view class="common-box">
			<view :class="'detail-info-box ' + (isBenefitsShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.benefits || '暂无其他福利')"></rich-text>
			</view>
			<view @tap="benefitsBtnTap" class="detail-info-btn" v-if="isBenefitsShowBtn && isComputedBenefits">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>

		<view class="common-title" style="margin-top: 48rpx;">职位描述</view>
		<view class="common-box">
			<view :class="'detail-info-box ' + (isDescShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.description || '暂无描述')"></rich-text>
			</view>
			<view @tap="descBtnTap" class="detail-info-btn" v-if="isDescShowBtn && isComputedDesc">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<!-- 视频展示区域 -->
		<view class="common-title" style="margin-top: 48rpx;" v-if="partJobVo.video_info && partJobVo.video_info.url">视频介绍</view>
		<view class="common-box video-box" v-if="partJobVo.video_info && partJobVo.video_info.url">
			<video 
				:src="partJobVo.video_info.url"
				:poster="partJobVo.video_info.poster"
				:controls="true"
				:show-center-play-btn="true"
				:enable-progress-gesture="true"
				:show-fullscreen-btn="true"
				:show-play-btn="true"
				:show-progress="true"
				:object-fit="'contain'"
				class="job-video"
			></video>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;" v-if="partJobVo.requirement">任职要求</view>
		<view class="common-box" v-if="partJobVo.requirement">
			<view :class="'detail-info-box ' + (isReqShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.requirement)"></rich-text>
			</view>
			<view @tap="reqBtnTap" class="detail-info-btn" v-if="isReqShowBtn && isComputedReq">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<!-- <view class="common-title" style="margin-top: 48rpx;">培训详情</view>
		<view class="common-box">
			<view :class="'detail-info-box ' + (isInfoShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.jobDesc || '1. 岗前培训：我们将提供专业的岗前培训，包括工作流程、服务标准、操作规范等内容。\n2. 技能培训：定期组织专业技能培训，提升员工的专业水平。\n3. 职业发展：提供清晰的职业发展路径，有机会晋升为店长或区域经理。\n4. 在岗指导：配备经验丰富的导师，提供一对一指导。')"></rich-text>
			</view>
			<view @tap="infoBtnTap" class="detail-info-btn" v-if="isInfoShowBtn && isComputedInfo">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view> -->
		
		<!-- <view class="common-title" style="margin-top: 48rpx;">入选专区</view>
		<view class="chosen-inner-item single">
			<view class="single-box">
				<image class="logo" src="https://qiniu-image.qtshe.com/entrance/bd_small.png"></image>
				<view>
					<view class="title ellipsis">高薪急招</view>
					<view class="desc">入选高薪急招专区</view>
				</view>
			</view>
			<view class="btn orange">进入榜单</view>
		</view> -->
		
		<view class="common-title" style="margin-top: 48rpx;" v-if="partJobVo.company_introduction && partJobVo.company_show === 1">公司介绍</view>
		<view class="common-box" v-if="partJobVo.company_introduction && partJobVo.company_show === 1">
			<view :class="'detail-info-box ' + (isCompanyShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.company_introduction)"></rich-text>
			</view>
			<view @tap="companyBtnTap" class="detail-info-btn" v-if="isCompanyShowBtn && isComputedCompany">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;" v-if="partJobVo.company_show === 1">{{ partJobVo.company.companyType === 2 ? '发布者' : '发布企业' }}</view>
		<view class="common-box" v-if="partJobVo.company_show === 1" :class="(partJobVo.company.isOfficialAccount || partJobVo.company.companyType === 2 || (partJobVo.company.companyType === 1 && hasEyeAuth) ? 'pb0' : '')">
			<view @tap="$emit('clickCompany')" class="common-company ptp_exposure_static" data-ptpid="c4e6-18fa-96fc-d96d" id="pid=c4e6-18fa-96fc-d96d">
				<view class="common-company-box">
					<image lazyLoad class="common-company-logo" :src="partJobVo.company.logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png'"></image>
					<view class="common-company-main">
						<view class="common-company-name ellipsis">{{ partJobVo.company.name }}</view>
						<view class="common-company-auth ignoreT2">
							<view class="iconfont iconverified company-icon-blue"></view>
							<view>企业认证</view>
							<image class="common-company-auth-icon" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon.png"></image>
						</view>
					</view>
				</view>
				<view class="iconfont iconarrow" v-if="hasEyeAuth"></view>
			</view>
			<view class="official-box">
				<view class="official-item">
					<view class="iconfont iconconfirm_round"></view>
					已通过天眼查认证
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			partJobVo: {
				type: Object,
				default: () => ({
					title: '',
					salary: '',
					description: '',
					requirement: '',
					address: '',
					province: '',
					city: '',
					district: '',
					jobDesc: '',
					company: {
						name: '',
						logo: '',
						industry: '',
						scale: '',
						introduction: ''
					},
					labelList: {
						descLabels: []
					},
					requireList: [],
					work_mode: '',
					work_intensity: '',
					work_time_type: '',
					payment: '',
					education: '',
					experience: '',
					formatted_options: {},
					company_show: 0
				})
			},
			chosenList: {
				type: Array,
				default: () => []
			},
			tabCurrent: {
				type: Number,
				default: 0
			},
			hasEyeAuth: {
				type: Boolean,
				default: false
			},
			isShowAll: {
				type: Boolean,
				default: false
			},
			agreementVo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				isInfoShowBtn: true,
				isComputedInfo: false,
				isDescShowBtn: true,
				isComputedDesc: false,
				isReqShowBtn: true,
				isComputedReq: false,
				isCompanyShowBtn: true,
				isComputedCompany: false,
				healthVisible: false,
				isBenefitsShowBtn: true,
				isComputedBenefits: false
			}
		},
		created() {
			console.log('education组件初始化', this.partJobVo);
			this.$nextTick(() => {
				this.getInfoHeight();
				this.getDescHeight();
				this.getReqHeight();
				this.getCompanyHeight();
				this.getBenefitsHeight();
			});
		},
		methods: {
			// 获取性别要求文本
			getGenderText(gender) {
				switch(gender) {
					case 0:
						return '不限';
					case 1:
						return '男';
					case 2:
						return '女';
					default:
						return '不限';
				}
			},
			// 获取住宿类型文本
			getHousingProvidedText(type) {
				if(type === 1) return '包住';
				if(type === 2) return '有偿住宿';
				if(type === 0) return '不包住';
				return '不包住';
			},
			// 跳转到公司
			jumpToCompany() {
				if (this.hasEyeAuth && this.partJobVo.company && this.partJobVo.company.id) {
					uni.navigateTo({
						url: "/pagesExa/zhaopin/company?id=" + this.partJobVo.company.id
					});
				}
			},
			// 格式化富文本
			formatRichText(text) {
				if (!text) return '';
				let content = text;
				try {
					// 替换常见的HTML实体
					content = content.replace(/&nbsp;/g, ' ')
								   .replace(/&lt;/g, '<')
								   .replace(/&gt;/g, '>')
								   .replace(/&amp;/g, '&')
								   .replace(/&quot;/g, '"')
								   .replace(/&#39;/g, "'")
								   .replace(/&middot;/g, '·')
								   .replace(/&ldquo;/g, '"')
								   .replace(/&rdquo;/g, '"')
								   .replace(/&hellip;/g, '...');
					
					// 处理换行
					content = content.replace(/\n/g, '<br>');
					
					// 处理段落
					content = content.replace(/<p[^>]*>/g, '<p>')
								   .replace(/<\/p>/g, '</p><br>');
					
					// 处理列表
					content = content.replace(/<ul[^>]*>/g, '<ul>')
								   .replace(/<ol[^>]*>/g, '<ol>')
								   .replace(/<li[^>]*>/g, '<li>');
					
					// 处理加粗和斜体
					content = content.replace(/<strong[^>]*>/g, '<b>')
								   .replace(/<\/strong>/g, '</b>')
								   .replace(/<em[^>]*>/g, '<i>')
								   .replace(/<\/em>/g, '</i>');
					
					// 处理链接
					content = content.replace(/<a[^>]*href="([^"]*)"[^>]*>/g, '<a href="$1">')
								   .replace(/<\/a>/g, '</a>');
					
					// 移除其他HTML标签的属性
					content = content.replace(/<([^>]+)>/g, (match, tag) => {
						const tagName = tag.split(' ')[0];
						return `<${tagName}>`;
					});
					
					// 确保内容是一个有效的字符串
					content = String(content).trim();
					
					// 移除多余的换行
					content = content.replace(/(<br>){3,}/g, '<br><br>');
					
				} catch (e) {
					console.error('格式化富文本出错:', e);
					// 发生错误时返回纯文本
					return text.replace(/<[^>]*>/g, '');
				}
				return content;
			},
			// 判断职位描述是否显示更多
			getDescHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(4) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isDescShowBtn = res1.height > (44 * 4);
							this.isComputedDesc = true;
						} else {
							this.isDescShowBtn = false;
							this.isComputedDesc = true;
						}
					}).exec();
				}, 500);
			},
			// 判断任职要求是否显示更多
			getReqHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(6) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isReqShowBtn = res1.height > (44 * 4);
							this.isComputedReq = true;
						} else {
							this.isReqShowBtn = false;
							this.isComputedReq = true;
						}
					}).exec();
				}, 500);
			},
			// 判断培训详情是否显示更多
			getInfoHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(8) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isInfoShowBtn = res1.height > (44 * 4);
							this.isComputedInfo = true;
						} else {
							this.isInfoShowBtn = false;
							this.isComputedInfo = true;
						}
					}).exec();
				}, 500);
			},
			// 判断公司介绍是否显示更多
			getCompanyHeight() {
				if (this.partJobVo.company && this.partJobVo.company.introduction) {
					setTimeout(() => {
						const query = uni.createSelectorQuery().in(this);
						query.select('.common-box:nth-child(12) .detail-info-text').boundingClientRect(res1 => {
							if (res1 && res1.height) {
								this.isCompanyShowBtn = res1.height > (44 * 4);
								this.isComputedCompany = true;
							} else {
								this.isCompanyShowBtn = false;
								this.isComputedCompany = true;
							}
						}).exec();
					}, 500);
				}
			},
			// 判断其他福利是否显示更多
			getBenefitsHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(10) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isBenefitsShowBtn = res1.height > (44 * 4);
							this.isComputedBenefits = true;
						} else {
							this.isBenefitsShowBtn = false;
							this.isComputedBenefits = true;
						}
					}).exec();
				}, 500);
			},
			// 点击展开职位描述
			descBtnTap() {
				this.isDescShowBtn = false;
			},
			// 点击展开任职要求
			reqBtnTap() {
				this.isReqShowBtn = false;
			},
			// 点击展开培训详情
			infoBtnTap() {
				this.isInfoShowBtn = false;
			},
			// 点击展开公司介绍
			companyBtnTap() {
				this.isCompanyShowBtn = false;
			},
			// 点击展开其他福利
			benefitsBtnTap() {
				this.isBenefitsShowBtn = false;
			}
		}
	}
</script>

<style lang="scss">
	@import './education.scss';	
	
	.header-top {
		position: relative;
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}
	
	.common-header-title {
		color: #111e38;
		font-size: 44rpx;
		line-height: 60rpx;
		font-weight: 700;
		flex: 1;
		margin-right: 120rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}
	
	.commission-info {
		position: fixed;
		right: 32rpx;
		top: 240rpx;
		background: rgba(255, 255, 255, 0.7);
		backdrop-filter: blur(20px);
		border-radius: 16rpx;
		padding: 26rpx 24rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
		animation: float 3s ease-in-out infinite;
		z-index: 999;
		transition: all 0.3s ease;
		text-align: center;
		width: 180rpx;
		
		&::before {
			content: '';
			position: absolute;
			inset: 0;
			border-radius: 16rpx;
			border: 1px solid rgba(255, 107, 0, 0.1);
			z-index: -1;
		}
		
		.commission-text {
			font-size: 20rpx;
			font-weight: bold;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 8rpx;
			
			&::before {
				content: '预计佣金';
				font-size: 24rpx;
				font-weight: bold;
				display: block;
				letter-spacing: 1rpx;
				margin: 0;
			}
		}
	}
	
	@keyframes float {
		0% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-8rpx);
		}
		100% {
			transform: translateY(0);
		}
	}
	
	.detail-info-box {
		position: relative;
		overflow: hidden;
		transition: all 0.3s;
		
		&:not(.detail-info-show) {
			.detail-info-text {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 4;
				overflow: hidden;
				padding-bottom: 40rpx;
			}
		}
		
		&.detail-info-show {
			.detail-info-text {
				display: block;
				padding-bottom: 24rpx;
			}
		}
		
		.detail-info-text {
			font-size: 28rpx;
			line-height: 44rpx;
			color: #111E38;
			
			::v-deep {
				p {
					margin: 0;
					padding: 0;
					line-height: 44rpx;
				}
				
				ul {
					margin: 16rpx 0;
					padding-left: 40rpx;
					list-style: disc;
					
					li {
						margin: 16rpx 0;
						line-height: 44rpx;
						
						p {
							margin: 0;
							line-height: 44rpx;
						}
					}
				}
				
				a {
					color: #3370ff;
					text-decoration: none;
				}
			}
		}
		
		&:not(.detail-info-show)::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 120rpx;
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.9) 50%, #FFFFFF 100%);
			pointer-events: none;
		}
	}
	
	.detail-info-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		color: #3370ff;
		font-size: 28rpx;
		margin-top: -40rpx;
		position: relative;
		z-index: 1;
		
		.iconarrow_down {
			margin-left: 8rpx;
			font-size: 24rpx;
		}
	}
	
	.chosen-inner-item.single {
		width: 686rpx;
		height: 132rpx;
		padding: 24rpx 32rpx 24rpx 24rpx;
		background: #fff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 24rpx;
		margin: 0 auto;
		
		.single-box {
			display: flex;
			align-items: center;
			
			.logo {
				width: 64rpx;
				height: 64rpx;
				margin-right: 16rpx;
			}
			
			.title {
				font-size: 32rpx;
				color: #111e38;
				font-weight: 700;
				line-height: 44rpx;
				max-width: 406rpx;
			}
			
			.desc {
				font-size: 24rpx;
				color: #808999;
				line-height: 32rpx;
				margin-top: 4rpx;
			}
		}
		
		.btn {
			width: 140rpx;
			height: 56rpx;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			
			&.orange {
				color: #ff8000;
				background: #fff2e5;
			}
			
			&.green {
				color: #00ca88;
				background: #e5fcf4;
			}
		}
	}
	
	.common-list-tag {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
		max-height: 180rpx;
		overflow: hidden;
		
		.list-tag {
			height: 48rpx;
			padding: 0 16rpx;
			background: #F5F7FA;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			
			.ellipsis {
				font-size: 24rpx;
				color: #485670;
				line-height: 32rpx;
			}
		}
	}
	
	.info-item {
		display: flex;
		padding: 24rpx 0;
		border-bottom: 1px solid #F5F7FA;
		
		&:last-child {
			border-bottom: none;
		}
		
		.info-label {
			width: 160rpx;
			color: #808999;
			font-size: 28rpx;
			line-height: 40rpx;
		}
		
		.info-content {
			flex: 1;
			color: #111E38;
			font-size: 28rpx;
			line-height: 40rpx;
		}
	}
	
	.video-box {
		padding: 24rpx;
		
		.job-video {
			width: 100%;
			height: 400rpx;
			border-radius: 16rpx;
			background: #000;
		}
	}
</style>
