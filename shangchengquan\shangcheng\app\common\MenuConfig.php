<?php
/**
 * 菜单配置类
 * 将所有菜单配置集中管理，便于维护
 */

namespace app\common;

class MenuConfig
{
    /**
     * 获取商城菜单配置
     */
    public static function getShopMenuConfig()
    {
        return [
            'name' => '商城',
            'fullname' => '商城系统',
            'icon' => 'my-icon my-icon-shop',
            'items' => [
                [
                    'name' => '商品管理',
                    'path' => 'ShopProduct/index',
                    'authdata' => 'ShopProduct/*,ShopCode/*'
                ],
                [
                    'name' => '条形码录入',
                    'path' => 'BarcodeProduct/index',
                    'authdata' => 'BarcodeProduct/*'
                ],
                [
                    'name' => '订单管理',
                    'path' => 'ShopOrder/index',
                    'authdata' => 'ShopOrder/*'
                ],
                [
                    'name' => '打印订单列表',
                    'path' => 'ShopOrder/printList',
                    'authdata' => 'ShopOrder/*'
                ],
                [
                    'name' => '退款申请',
                    'path' => 'ShopRefundOrder/index',
                    'authdata' => 'ShopRefundOrder/*'
                ],
                [
                    'name' => '评价管理',
                    'path' => 'ShopComment/index',
                    'authdata' => 'ShopComment/*'
                ],
                [
                    'name' => '商品分类',
                    'path' => 'ShopCategory/index',
                    'authdata' => 'ShopCategory/*',
                    'admin_only' => true
                ],
                [
                    'name' => '商品分类',
                    'path' => 'ShopCategory2/index',
                    'authdata' => 'ShopCategory2/*',
                    'admin_only' => false
                ],
                [
                    'name' => '商品分组',
                    'path' => 'ShopGroup/index',
                    'authdata' => 'ShopGroup/*',
                    'admin_only' => true
                ],
                [
                    'name' => '商品参数',
                    'path' => 'ShopParam/index',
                    'authdata' => 'ShopParam/*'
                ],
                [
                    'name' => '商品服务',
                    'path' => 'ShopFuwu/index',
                    'authdata' => 'ShopFuwu/*'
                ],
                [
                    'name' => '商品海报',
                    'path' => 'ShopPoster/index',
                    'authdata' => 'ShopPoster/*',
                    'admin_only' => true
                ],
                [
                    'name' => '录入订单',
                    'path' => 'ShopOrderlr/index',
                    'authdata' => 'ShopOrderlr/*,ShopProduct/chooseproduct,ShopProduct/index,ShopProduct/getproduct,Member/index',
                    'admin_only' => true
                ],
                [
                    'name' => '商品采集',
                    'path' => 'ShopTaobao/index',
                    'authdata' => 'ShopTaobao/*'
                ],
                [
                    'name' => '销售统计',
                    'path' => 'ShopOrder/tongji',
                    'authdata' => 'ShopOrder/*'
                ],
                [
                    'name' => '系统设置',
                    'path' => 'ShopSet/index',
                    'authdata' => 'ShopSet/*',
                    'admin_only' => true
                ]
            ]
        ];
    }

    /**
     * 获取会员菜单配置
     */
    public static function getMemberMenuConfig()
    {
        return [
            'name' => t('会员'),
            'fullname' => t('会员') . '管理',
            'icon' => 'my-icon my-icon-member',
            'items' => [
                [
                    'name' => t('会员') . '列表',
                    'path' => 'Member/index',
                    'authdata' => 'Member/index,Member/excel,Member/excel,Member/importexcel,Member/getplatform,Member/edit,Member/save,Member/del,Member/getcarddetail,Member/charts,Member/setst,Member/*,MemberStatistics/*'
                ],
                [
                    'name' => '充值',
                    'path' => 'Member/recharge',
                    'authdata' => 'Member/recharge',
                    'hide' => true
                ],
                [
                    'name' => '加配资',
                    'path' => 'Member/addAllocation',
                    'authdata' => 'Member/addAllocation',
                    'hide' => true
                ],
                [
                    'name' => '加积分',
                    'path' => 'Member/addscore',
                    'authdata' => 'Member/addscore',
                    'hide' => true
                ],
                [
                    'name' => '加佣金',
                    'path' => 'Member/addcommission',
                    'authdata' => 'Member/addcommission',
                    'hide' => true
                ],
                [
                    'name' => '加' . t('贡献值'),
                    'path' => 'Member/addgongxianzhi',
                    'authdata' => 'Member/addgongxianzhi',
                    'hide' => true
                ],
                [
                    'name' => '加' . t('现金券'),
                    'path' => 'Member/addheijifen',
                    'authdata' => 'Member/addheijifen',
                    'hide' => true
                ],
                [
                    'name' => '等级及分销',
                    'path' => 'MemberLevel/index',
                    'authdata' => 'MemberLevel/*'
                ],
                [
                    'name' => '升级申请记录',
                    'path' => 'MemberLevel/applyorder',
                    'authdata' => 'MemberLevel/*'
                ],
                [
                    'name' => t('会员') . '关系图',
                    'path' => 'Member/charts',
                    'authdata' => 'Member/charts'
                ],
                [
                    'name' => t('会员') . '标签',
                    'path' => 'MemberTag/index',
                    'authdata' => 'MemberTag/*'
                ],
                [
                    'name' => '注册自定义',
                    'path' => 'RegisterForm/index',
                    'authdata' => 'RegisterForm/*'
                ],
                [
                    'name' => '分享海报',
                    'path' => 'MemberPoster/index',
                    'authdata' => 'MemberPoster/*'
                ]
            ]
        ];
    }

    /**
     * 获取管理员财务菜单配置
     */
    public static function getAdminFinanceMenuConfig()
    {
        return [
            'name' => '财务',
            'fullname' => '财务管理',
            'icon' => 'my-icon my-icon-finance',
            'items' => [
                [
                    'name' => t('会员') . '列表',
                    'path' => 'Membercaiwu/index',
                    'authdata' => 'Membecaiwu/*'
                ],
                [
                    'name' => '全局明细',
                    'path' => 'Payorder/tongjiyue',
                    'authdata' => 'Payorder/*'
                ],
                [
                    'name' => '每日收款明细',
                    'path' => 'Payorder/caiwu',
                    'authdata' => 'Payorder/*'
                ],
                [
                    'name' => '每日货款明细',
                    'path' => 'Payorder/fahuomingxi',
                    'authdata' => 'Payorder/*'
                ],
                [
                    'name' => '消费明细',
                    'path' => 'Payorder/index',
                    'authdata' => 'Payorder/*'
                ],
                [
                    'name' => '用户明细',
                    'path' => 'BusinessMoney/memberindex',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => t('余额') . '明细',
                    'path' => 'Money/moneylog',
                    'authdata' => 'Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'
                ],
                [
                    'name' => t('现金券') . '明细',
                    'path' => 'Money/heijifenlog',
                    'authdata' => 'Money/heijifenlog,Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'
                ],
                [
                    'name' => '充值记录',
                    'path' => 'Money/rechargelog',
                    'authdata' => 'Money/rechargelog,Money/rechargelogexcel,Money/rechargelogdel'
                ],
                [
                    'name' => t('余额') . '提现',
                    'path' => 'Money/withdrawlog',
                    'authdata' => 'Money/*'
                ],
                [
                    'name' => t('佣金') . '记录',
                    'path' => 'Commission/record',
                    'authdata' => 'Commission/record'
                ],
                [
                    'name' => t('佣金') . '明细',
                    'path' => 'Commission/commissionlog',
                    'authdata' => 'Commission/commissionlog,Commission/commissionlogexcel,Commission/commissionlogdel'
                ],
                [
                    'name' => t('佣金') . '提现',
                    'path' => 'Commission/withdrawlog',
                    'authdata' => 'Commission/*'
                ],
                [
                    'name' => t('积分') . '明细',
                    'path' => 'Score/scorelog',
                    'authdata' => 'Score/*'
                ],
                [
                    'name' => t('积分') . '转账明细',
                    'path' => 'Score/zhuanscorelog',
                    'authdata' => 'Score/*'
                ],
                [
                    'name' => t('余额') . t('转账') . '明细',
                    'path' => 'Money/zhuangzhang',
                    'authdata' => 'Money/*'
                ],
                [
                    'name' => '买单记录',
                    'path' => 'Maidan/index',
                    'authdata' => 'Maidan/*'
                ],
                [
                    'name' => '分红记录',
                    'path' => 'Commission/fenhonglog',
                    'authdata' => 'Commission/*'
                ],
                [
                    'name' => '核销记录',
                    'path' => 'Hexiao/index',
                    'authdata' => 'Hexiao/*'
                ],
                [
                    'name' => '发票管理',
                    'path' => 'Invoice/index',
                    'authdata' => 'Invoice/*'
                ],
                [
                    'name' => '买单收款',
                    'children' => [
                        [
                            'name' => '买单扣费',
                            'path' => 'Maidan/add',
                            'authdata' => 'Maidan/*'
                        ],
                        [
                            'name' => '买单记录',
                            'path' => 'Maidan/index',
                            'authdata' => 'Maidan/*'
                        ],
                        [
                            'name' => '聚合收款码',
                            'path' => 'Maidan/set',
                            'authdata' => 'Maidan/set'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取商家财务菜单配置
     */
    public static function getBusinessFinanceMenuConfig($userType)
    {
        $config = [
            'name' => '财务',
            'fullname' => '财务管理',
            'icon' => 'my-icon my-icon-finance',
            'items' => []
        ];

        if ($userType == 'business') {
            $config['items'] = [
                [
                    'name' => '余额明细',
                    'path' => 'BusinessMoney/moneylog',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => '用户明细',
                    'path' => 'BusinessMoney/memberindex',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => '充值申请',
                    'path' => 'BusinessMoney/recharge',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => '充值记录',
                    'path' => 'BusinessMoney/rechargelog',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => '余额提现',
                    'path' => 'BusinessMoney/withdraw',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => '提现记录',
                    'path' => 'BusinessMoney/withdrawlog',
                    'authdata' => 'BusinessMoney/*'
                ],
                [
                    'name' => '买单收款',
                    'children' => [
                        [
                            'name' => '买单扣费',
                            'path' => 'BusinessMaidan/add',
                            'authdata' => 'BusinessMaidan/*'
                        ],
                        [
                            'name' => '买单记录',
                            'path' => 'BusinessMaidan/index',
                            'authdata' => 'BusinessMaidan/*'
                        ],
                        [
                            'name' => '收款码',
                            'path' => 'BusinessMaidan/set',
                            'authdata' => 'BusinessMaidan/*'
                        ]
                    ]
                ]
            ];
        } elseif ($userType == 'tuanzhang') {
            $config['items'] = [
                [
                    'name' => '团长收益',
                    'path' => 'TuanzhangMoney/moneylog',
                    'authdata' => 'TuanzhangMoney/*'
                ],
                [
                    'name' => '提现申请',
                    'path' => 'TuanzhangMoney/withdraw',
                    'authdata' => 'TuanzhangMoney/*'
                ],
                [
                    'name' => '提现记录',
                    'path' => 'TuanzhangMoney/withdrawlog',
                    'authdata' => 'TuanzhangMoney/*'
                ],
                [
                    'name' => '订单记录',
                    'path' => 'TuanzhangOrder/index',
                    'authdata' => 'TuanzhangOrder/*'
                ]
            ];
        }

        return $config;
    }

    /**
     * 获取营销菜单配置
     */
    public static function getMarketingMenuConfig()
    {
        return [
            'name' => '营销',
            'fullname' => '营销活动',
            'icon' => 'my-icon my-icon-yingxiao',
            'items' => [
                [
                    'name' => t('优惠券'),
                    'path' => 'Coupon/index',
                    'authdata' => 'Coupon/*,ShopCategory/index,ShopCategory/choosecategory'
                ],
                [
                    'name' => '注册赠送',
                    'path' => 'Member/registerGive',
                    'authdata' => 'Member/registerGive',
                    'admin_only' => true
                ],
                [
                    'name' => '充值赠送',
                    'path' => 'Money/giveset',
                    'authdata' => 'Money/giveset',
                    'admin_only' => true
                ],
                [
                    'name' => '购物满减',
                    'path' => 'Manjian/set',
                    'authdata' => 'Manjian/set',
                    'admin_only' => true
                ],
                [
                    'name' => '商品促销',
                    'path' => 'Cuxiao/index',
                    'authdata' => 'Cuxiao/*'
                ],
                [
                    'name' => '购物返现',
                    'path' => 'Cashback/index',
                    'authdata' => 'Cashback/*',
                    'admin_only' => true
                ],
                [
                    'name' => '多人拼团',
                    'children' => [
                        [
                            'name' => '商品管理',
                            'path' => 'CollageProduct/index',
                            'authdata' => 'CollageProduct/*,CollageCode/*'
                        ],
                        [
                            'name' => '订单管理',
                            'path' => 'CollageOrder/index',
                            'authdata' => 'CollageOrder/*'
                        ],
                        [
                            'name' => '拼团管理',
                            'path' => 'CollageTeam/index',
                            'authdata' => 'CollageTeam/*'
                        ],
                        [
                            'name' => '评价管理',
                            'path' => 'CollageComment/index',
                            'authdata' => 'CollageComment/*'
                        ],
                        [
                            'name' => '商品分类',
                            'path' => 'CollageCategory/index',
                            'authdata' => 'CollageCategory/*',
                            'admin_only' => true
                        ],
                        [
                            'name' => '分享海报',
                            'path' => 'CollagePoster/index',
                            'authdata' => 'CollagePoster/*',
                            'admin_only' => true
                        ],
                        [
                            'name' => '系统设置',
                            'path' => 'CollageSet/index',
                            'authdata' => 'CollageSet/*',
                            'admin_only' => true
                        ]
                    ]
                ],
                [
                    'name' => '砍价活动',
                    'children' => [
                        [
                            'name' => '商品管理',
                            'path' => 'KanjiaProduct/index',
                            'authdata' => 'KanjiaProduct/*,KanjiaCode/*'
                        ],
                        [
                            'name' => '订单管理',
                            'path' => 'KanjiaOrder/index',
                            'authdata' => 'KanjiaOrder/*'
                        ],
                        [
                            'name' => '分享海报',
                            'path' => 'KanjiaPoster/index',
                            'authdata' => 'KanjiaPoster/*',
                            'admin_only' => true
                        ],
                        [
                            'name' => '系统设置',
                            'path' => 'KanjiaSet/index',
                            'authdata' => 'KanjiaSet/*',
                            'admin_only' => true
                        ]
                    ]
                ],
                [
                    'name' => '整点秒杀',
                    'children' => [
                        [
                            'name' => '商品列表',
                            'path' => 'SeckillProduct/index',
                            'authdata' => 'SeckillProduct/*,SeckillCode/*'
                        ],
                        [
                            'name' => '订单列表',
                            'path' => 'SeckillOrder/index',
                            'authdata' => 'SeckillOrder/*'
                        ],
                        [
                            'name' => '用户评价',
                            'path' => 'SeckillComment/index',
                            'authdata' => 'SeckillComment/*'
                        ],
                        [
                            'name' => '秒杀设置',
                            'path' => 'SeckillSet/index',
                            'authdata' => 'SeckillSet/*',
                            'admin_only' => true
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取拓展功能菜单配置
     */
    public static function getExtensionMenuConfigs()
    {
        return [
            // 这里可以添加各种拓展功能菜单配置
            // 由于原文件太大，这里只展示结构，具体配置可以根据需要添加
        ];
    }

    /**
     * 获取系统菜单配置
     */
    public static function getSystemMenuConfig()
    {
        return [
            'name' => '系统',
            'fullname' => '系统设置',
            'icon' => 'my-icon my-icon-sysset',
            'items' => [
                [
                    'name' => '基础设置',
                    'path' => 'Backstage/sysset',
                    'authdata' => 'Backstage/sysset'
                ],
                [
                    'name' => '微信设置',
                    'path' => 'Wxset/index',
                    'authdata' => 'Wxset/*'
                ],
                [
                    'name' => '支付设置',
                    'path' => 'Wxpay/index',
                    'authdata' => 'Wxpay/*'
                ],
                [
                    'name' => '短信设置',
                    'path' => 'Sms/index',
                    'authdata' => 'Sms/*'
                ],
                [
                    'name' => '运费模板',
                    'path' => 'Freight/index',
                    'authdata' => 'Freight/*'
                ],
                [
                    'name' => '操作日志',
                    'path' => 'Backstage/plog',
                    'authdata' => 'Backstage/plog'
                ]
            ]
        ];
    }

    /**
     * 获取权限白名单
     */
    public static function getBlacklist()
    {
        return [
            'Backstage/index',
            'Backstage/welcome',
            'Backstage/setpwd',
            'Backstage/about',
            'Help/*',
            'Upload/*',
            'DesignerPage/chooseurl',
            'Peisong/getpeisonguser',
            'Peisong/peisong',
            'Miandan/addorder',
            'Wxset/*',
            'Notice/*',
            'notice/*',
            'SxpayIncome/*',
            'Member/inputlockpwd',
            'MemberLevel/inputlockpwd',
            'ShopProduct/inputlockpwd',
            'Member/dolock',
            'MemberLevel/dolock',
            'ShopProduct/dolock'
        ];
    }
}
