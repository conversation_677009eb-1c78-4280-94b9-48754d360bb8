<?php /*a:3:{s:76:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\web_user\edit.html";i:1745486434;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>编辑用户</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
	<script src="/static/admin/js/address3.js?v=1"></script>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<?php if(!$ainfo['id']): ?><i class="fa fa-plus"></i> 添加用户<?php else: ?><i class="fa fa-pencil"></i> 编辑用户<?php endif; if(input('param.isopen')==1): ?><i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i><?php endif; ?>
				</div>
				<div class="layui-card-body" pad15>
					<div class="layui-form" lay-filter="">
						<input type="hidden" name="ainfo[id]" value="<?php echo $ainfo['id']; ?>">
						<input type="hidden" name="uinfo[id]" value="<?php echo $uinfo['id']; ?>">
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">登录账号：</label>
							<div class="layui-input-inline">
								<input type="text" name="uinfo[un]" value="<?php echo $uinfo['un']; ?>" lay-verify="required" lay-verType="tips" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">登录密码：</label>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" autocomplete="off" name="uinfo[pwd]" value="" <?php if($uinfo['id']): ?> placeholder="留空表示不修改密码"<?php else: ?> lay-verify="required" lay-verType="tips"<?php endif; ?>>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">联系人：</label>
							<div class="layui-input-inline">
								<input type="text" name="ainfo[linkman]" value="<?php echo $ainfo['linkman']; ?>" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">联系电话：</label>
							<div class="layui-input-inline">
								<input type="text" name="ainfo[tel]" value="<?php echo $ainfo['tel']; ?>" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">人脸识别次数：</label>
							<div class="layui-input-inline">
								<input type="text" name="ainfo[face_times]" value="<?php echo $ainfo['face_times']; ?>" autocomplete="off" class="layui-input">
							</div>
						</div>
						<!-- <div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">地区：</label>
							<div class="layui-input-inline">
								<select name="ainfo[province]" id="province" lay-filter="province"></select>
							</div>
							<div class="layui-input-inline">
								<select name="ainfo[city]" id="city" lay-filter="city"></select>
							</div>
							<div class="layui-input-inline">
								<select name="ainfo[area]" id="area" ></select>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">地址：</label>
							<div class="layui-input-inline">
								<input type="text" name="ainfo[address]" value="<?php echo $ainfo['address']; ?>" class="layui-input">
							</div>
						</div> -->
						<div class="layui-form-item" id="endtimediv">
							<label class="layui-form-label" style="width:150px">到期时间：</label>
							<div class="layui-input-inline">
								<input type="text" id="endtime" name="ainfo[endtime]" value="<?php echo date('Y-m-d H:i:s',$ainfo['endtime']); ?>" class="layui-input">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">前端独立域名：</label>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important">https://</div>
							<div class="layui-input-inline">
								<input type="text" name="ainfo[domain]" value="<?php echo $ainfo['domain']; ?>" autocomplete="off" class="layui-input">
							</div>
							<div class="layui-form-mid layui-word-aux">不需要设置手机端独立访问域名无须填写</div>
							<div class="layui-form-mid layui-word-aux" style="clear:left;margin-left:180px">设置后手机端可以用该域名进行访问，注意：后台还是使用统一域名进行访问<br>设置方法：在宝塔添加站点域名填写独立域名，根目录选择系统的原安装目录，配置ssl证书</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">版权信息：</label>
							<div class="layui-input-inline" style="width:300px;">
								<textarea type="text" name="ainfo[copyright]" class="layui-textarea" style="min-height:60px"><?php echo $ainfo['copyright']; ?></textarea>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">类型：</label>
							<div class="layui-input-inline" style="width:500px">
								<?php $platform = getplatformname(); foreach($platform as $k=>$v): ?>
								<input type="checkbox" name="ainfo[platform][]" value="<?php echo $k; ?>" title="<?php echo $v; ?>" lay-skin="primary" <?php if(in_array($k,explode(',',$ainfo['platform']))): ?>checked<?php endif; ?>>
								<?php endforeach; ?>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">微支付抽成：</label>
							<div class="layui-input-inline" style="width:500px">
								<input type="radio" name="ainfo[chouchengset]" value="0" <?php if(!$ainfo['chouchengset'] || $ainfo['chouchengset']==0): ?>checked<?php endif; ?> title="按默认抽成" lay-filter="achouchengset">
								<input type="radio" name="ainfo[chouchengset]" value="1" <?php if($ainfo['chouchengset']==1): ?>checked<?php endif; ?> title="按百分百抽成" lay-filter="achouchengset">
								<input type="radio" name="ainfo[chouchengset]" value="2" <?php if($ainfo['chouchengset']==2): ?>checked<?php endif; ?> title="固定抽成" lay-filter="achouchengset">
								<input type="radio" name="ainfo[chouchengset]" value="3" <?php if($ainfo['chouchengset']==3): ?>checked<?php endif; ?> title="不抽成" lay-filter="achouchengset">
							</div>
							<div class="layui-form-mid layui-word-aux">设置抽成需开通服务商分账功能</div>
						</div>
						<div class="layui-form-item" id="achouchengset1" <?php if($ainfo['chouchengset']!=1): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label" style="width:150px"></label>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 抽成比例(%) </div>
							<div class="layui-input-inline" style="width: 100px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[chouchengrate]" class="layui-input" value="<?php echo $ainfo['chouchengrate']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 最低抽成金额(元) </div>
							<div class="layui-input-inline" style="width: 100px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[chouchengmin]" class="layui-input" value="<?php echo $ainfo['chouchengmin']; ?>">
							</div>
						</div>
						<div class="layui-form-item" id="achouchengset2" <?php if($ainfo['chouchengset']!=2): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label" style="width:150px"></label>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 抽成金额(元) </div>
							<div class="layui-input-inline" style="width: 100px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[chouchengmoney]" class="layui-input" value="<?php echo $ainfo['chouchengmoney']; ?>">
							</div>
						</div>
						<?php if(getcustom('choucheng_receiver')): ?>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">分账接收方：</label>
							<div class="layui-input-inline" style="width:600px">
								<input type="radio" name="ainfo[choucheng_receivertype]" value="0" title="服务商" <?php if(!$ainfo || $ainfo['choucheng_receivertype']==0): ?>checked<?php endif; ?> lay-filter="choucheng_receivertype">
								<input type="radio" name="ainfo[choucheng_receivertype]" value="1" title="商户号" <?php if($ainfo['choucheng_receivertype']==1): ?>checked<?php endif; ?> lay-filter="choucheng_receivertype">
								<input type="radio" name="ainfo[choucheng_receivertype]" value="2" title="个人openid" <?php if($ainfo['choucheng_receivertype']==2): ?>checked<?php endif; ?> lay-filter="choucheng_receivertype">
							</div>
						</div>

						<div class="layui-form-item" id="choucheng_receivertype1" <?php if($ainfo['choucheng_receivertype']!=1): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label" style="width:150px"></label>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 商户号(必填) </div>
							<div class="layui-input-inline" style="width: 150px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[choucheng_receivertype1_account]" class="layui-input" value="<?php echo $ainfo['choucheng_receivertype1_account']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 商户全称(必填)</div>
							<div class="layui-input-inline" style="width: 200px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[choucheng_receivertype1_name]" class="layui-input" value="<?php echo $ainfo['choucheng_receivertype1_name']; ?>">
							</div>
						</div>
						<div class="layui-form-item" id="choucheng_receivertype2" <?php if($ainfo['choucheng_receivertype']!=2): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label" style="width:150px"></label>
							<div class="layui-input-inline" style="width:300px">
								<input type="radio" name="ainfo[choucheng_receivertype2_openidtype]" value="0" title="服务商openid" <?php if($ainfo['choucheng_receivertype2_openidtype']==0): ?>checked<?php endif; ?> lay-filter="choucheng_receivertype2_openidtype">
								<input type="radio" name="ainfo[choucheng_receivertype2_openidtype]" value="1" title="子商户openid" <?php if($ainfo['choucheng_receivertype2_openidtype']==1): ?>checked<?php endif; ?> lay-filter="choucheng_receivertype2_openidtype">
							</div>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 公众号openid </div>
							<div class="layui-input-inline" style="width:150px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[choucheng_receivertype2_account]" class="layui-input" value="<?php echo $ainfo['choucheng_receivertype2_account']; ?>">
							</div>
							<div id="choucheng_receivertype2_accountwx" <?php if($ainfo['choucheng_receivertype2_openidtype']!=1): ?>style="display:none"<?php endif; ?>>
								<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 小程序openid </div>
								<div class="layui-input-inline" style="width:150px;">
									<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[choucheng_receivertype2_accountwx]" class="layui-input" value="<?php echo $ainfo['choucheng_receivertype2_accountwx']; ?>">
								</div>
							</div>
							<div class="layui-form-mid" style="margin-right:0;background:#e6e6e6;padding: 9px 9px !important"> 姓名(选填) </div>
							<div class="layui-input-inline" style="width: 100px;">
								<input style="border-radius:0 2px 2px 0" type="text" name="ainfo[choucheng_receivertype2_name]" class="layui-input" value="<?php echo $ainfo['choucheng_receivertype2_name']; ?>">
							</div>
							<div class="layui-form-mid layui-word-aux" style="clear:both;margin-left:180px">服务商openid由服务商的APPID转换得到，子商户openid由子商户APPID转换得到</div>
						</div>
						<?php endif; if(getcustom('agent_card')): ?>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">代理卡片：</label>
							<div class="layui-input-inline">
								<input type="radio" name="ainfo[agent_card]" value="1" title="开启" <?php if($ainfo['agent_card']==1): ?>checked<?php endif; ?>>
								<input type="radio" name="ainfo[agent_card]" value="0" title="关闭" <?php if($ainfo['agent_card']==0): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php endif; if(getcustom('image_search')): ?>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">图搜：</label>
							<div class="layui-input-inline">
								<input type="radio" name="ainfo[image_search]" value="1" title="开启" <?php if($ainfo['image_search']==1): ?>checked<?php endif; ?>>
								<input type="radio" name="ainfo[image_search]" value="0" title="关闭" <?php if($ainfo['image_search']==0): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php endif; if(getcustom('order_show_onlychildren')): ?>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">管理员仅可查看下级订单和会员：</label>
							<div class="layui-input-inline">
								<input type="radio" name="ainfo[order_show_onlychildren]" value="1" title="开启" <?php if($ainfo['order_show_onlychildren']==1): ?>checked<?php endif; ?>>
								<input type="radio" name="ainfo[order_show_onlychildren]" value="0" title="关闭" <?php if($ainfo['order_show_onlychildren']==0): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php endif; if(getcustom('other_money')): ?>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">多账户：</label>
							<div class="layui-input-inline">
								<input type="radio" name="ainfo[othermoney_status]" value="1" title="开启" <?php if($ainfo['othermoney_status']==1): ?>checked<?php endif; ?>>
								<input type="radio" name="ainfo[othermoney_status]" value="0" title="关闭" <?php if(!$ainfo['othermoney_status'] || $ainfo['othermoney_status']==0): ?>checked<?php endif; ?>>
							</div>
						</div>
						<?php endif; ?>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">状态：</label>
							<div class="layui-input-inline">
								<input type="radio" name="ainfo[status]" value="1" title="开启" <?php if($ainfo['status']==1): ?>checked<?php endif; ?>>
								<input type="radio" name="ainfo[status]" value="0" title="关闭" <?php if($ainfo['status']==0): ?>checked<?php endif; ?>>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">模块权限：</label>
							<div class="layui-input-inline">
								<input type="radio" name="uinfo[auth_type]" value="1" title="所有" <?php if(!$uinfo['aid'] || $uinfo['auth_type']==1): ?>checked<?php endif; ?> lay-filter="auth_type">
								<input type="radio" name="uinfo[auth_type]" value="0" title="选择" <?php if($uinfo['aid'] && $uinfo['auth_type']==0): ?>checked<?php endif; ?> lay-filter="auth_type">
							</div>
						</div>

						<div class="layui-form-item" id="authdataset" style="<?php if(!$uinfo['aid'] || $uinfo['auth_type']==1): ?>display:none<?php endif; ?>">
							<label class="layui-form-label" style="width:150px">权限设置：</label>
							<div class="layui-input-block" style="margin-left:180px;">
								<div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
									<input type="checkbox" title="全部选择" lay-skin="primary" lay-filter="checkall_all"/>
								</div>
								<?php $i=0; foreach($menudata as $k=>$v): $i++; ?>
								<div>
									<div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
										 <input type="checkbox" title="<?php echo $v['name']; ?>" lay-skin="primary" lay-filter="checkall"/>
									</div>
									<div style="margin-left:20px">
									<?php foreach($v['child'] as $k1=>$v1): 
									if(!$v1['authdata'] && $v1['child']){
										$path = array();
										echo '<div style="width: 100px;float:left;margin-top:8px;clear:left">'.$v1['name'].'</div>';
										foreach($v1['child'] as $v2){
											echo '<div style="min-width: 120px;float: left;">';
											echo '	<input type="checkbox" value="'.$v2['path'].','.str_replace('/*','^_^',$v2['authdata']).'" name="auth_data[]" '.(in_array($v2['path'].','.$v2['authdata'],$auth_data)?'checked':'').' title="'.$v2['name'].'" lay-skin="primary"/>';
											echo '</div>';
										}
										echo '<div style="clear:both;float: left;"></div>';
									}
									 if(!$v1['child'] || $v1['authdata']): ?>
									<div style="min-width: 120px;float: left;">
										<input type="checkbox" value="<?php echo $v1['path']; ?>,<?php echo str_replace('/*','^_^',$v1['authdata']); ?>" name="auth_data[]" <?php if(in_array($v1['path'].','.$v1['authdata'],$auth_data)): ?>checked<?php endif; ?> title="<?php echo $v1['name']; ?>" lay-skin="primary"/>
									</div>
									<?php endif; ?>
									<?php endforeach; ?>
									</div>
									<div style="clear:both;float: left;margin-bottom:10px"></div>
								</div>
								<?php endforeach; ?>
							</div>
						</div>
  <!-- 需同步修改 public\user_auth.html -->
  <div class="layui-form-item">
	  <label class="layui-form-label" style="width:150px">手机端权限：</label>
	  <div class="layui-input-block" style="margin-left:180px;">
		  <div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
			  <input type="checkbox" title="全部选择" lay-skin="primary" lay-filter="checkall_all"/>
		  </div>
		  <div>
			  <div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
				  <input type="checkbox" title="查看权限" lay-skin="primary" lay-filter="checkall"/>
			  </div>
			  <div style="margin-left:20px">
				  <div style="min-width: 120px;float: left;">
					  <input type="checkbox" value="member" name="wxauth_data[]" <?php if(in_array('member',$wxauth_data)): ?>checked<?php endif; ?> title="<?php echo t('会员'); ?>" lay-skin="primary"/>
				  </div>
				  <div style="min-width: 120px;float: left;">
					  <input type="checkbox" value="product" name="wxauth_data[]" <?php if(in_array('product',$wxauth_data)): ?>checked<?php endif; ?> title="商品" lay-skin="primary"/>
				  </div>
				  <div style="min-width: 120px;float: left;">
					  <input type="checkbox" value="order" name="wxauth_data[]" <?php if(in_array('order',$wxauth_data)): ?>checked<?php endif; ?> title="订单" lay-skin="primary"/>
				  </div>
				  <div style="min-width: 120px;float: left;">
					  <input type="checkbox" value="finance" name="wxauth_data[]" <?php if(in_array('finance',$wxauth_data)): ?>checked<?php endif; ?> title="财务" lay-skin="primary"/>
				  </div>
				  <div style="min-width: 120px;float: left;">
					  <input type="checkbox" value="zixun" name="wxauth_data[]" <?php if(in_array('zixun',$wxauth_data)): ?>checked<?php endif; ?> title="咨询" lay-skin="primary"/>
				  </div>
				  <div style="clear:both;float: left;margin-bottom:10px"></div>
			  </div>
		  </div>
		  <div>
			  <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
				  <input type="checkbox" title="接收通知权限" lay-skin="primary" lay-filter="checkall"/>
			  </div>
			  <div style="margin-left:20px">
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="订单提交通知" name="notice_auth_data[]" <?php if(in_array('tmpl_orderconfirm',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_orderconfirm" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="订单支付通知" name="notice_auth_data[]" <?php if(in_array('tmpl_orderpay',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_orderpay" lay-skin="primary"/>
				  </div>

				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="订单收货通知" name="notice_auth_data[]" <?php if(in_array('tmpl_ordershouhuo',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_ordershouhuo" lay-skin="primary"/>
				  </div>

				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="退款申请通知" name="notice_auth_data[]" <?php if(in_array('tmpl_ordertui',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_ordertui" lay-skin="primary"/>
				  </div>

				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="提现申请通知" name="notice_auth_data[]" <?php if(in_array('tmpl_withdraw',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_withdraw" lay-skin="primary"/>
				  </div>

				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="升级申请通知" name="notice_auth_data[]" <?php if(in_array('tmpl_uplv',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_uplv" lay-skin="primary"/>
				  </div>

				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="表单提交通知" name="notice_auth_data[]" <?php if(in_array('tmpl_formsub',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_formsub" lay-skin="primary"/>
				  </div>

				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="用户咨询通知" name="notice_auth_data[]" <?php if(in_array('tmpl_kehuzixun',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_kehuzixun" lay-skin="primary"/>
				  </div>

				  <div style="clear:both;float: left;margin-bottom:10px"></div>
			  </div>
		  </div>

		  <div>
			  <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
				  <input type="checkbox" title="核销权限" lay-skin="primary" lay-filter="checkall"/>
			  </div>
			  <div style="margin-left:20px">
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="商城订单" name="hexiao_auth_data[]" <?php if(in_array('shop',$hexiao_auth_data)): ?>checked<?php endif; ?> value="shop" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="拼团订单" name="hexiao_auth_data[]" <?php if(in_array('collage',$hexiao_auth_data)): ?>checked<?php endif; ?> value="collage" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="幸运拼团订单" name="hexiao_auth_data[]" <?php if(in_array('lucky_collage',$hexiao_auth_data)): ?>checked<?php endif; ?> value="lucky_collage" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="周期购" name="hexiao_auth_data[]" <?php if(in_array('cycle',$hexiao_auth_data)): ?>checked<?php endif; ?> value="cycle" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="砍价订单" name="hexiao_auth_data[]" <?php if(in_array('kanjia',$hexiao_auth_data)): ?>checked<?php endif; ?> value="kanjia" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="秒杀订单" name="hexiao_auth_data[]" <?php if(in_array('seckill',$hexiao_auth_data)): ?>checked<?php endif; ?> value="seckill" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="预约订单" name="hexiao_auth_data[]" <?php if(in_array('yuyue',$hexiao_auth_data)): ?>checked<?php endif; ?> value="yuyue" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="<?php echo t('积分'); ?>兑换" name="hexiao_auth_data[]" <?php if(in_array('scoreshop',$hexiao_auth_data)): ?>checked<?php endif; ?> value="scoreshop" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="<?php echo t('优惠券'); ?>" name="hexiao_auth_data[]" <?php if(in_array('coupon',$hexiao_auth_data)): ?>checked<?php endif; ?> value="coupon" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="抽奖活动" name="hexiao_auth_data[]" <?php if(in_array('choujiang',$hexiao_auth_data)): ?>checked<?php endif; ?> value="choujiang" lay-skin="primary"/>
				  </div>
				  <div style="clear:both;float: left;margin-bottom:10px"></div>
			  </div>
		  </div>
		  <div>
			  <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
				  <input type="checkbox" title="餐饮权限" lay-skin="primary" lay-filter="checkall"/>
			  </div>
			  <div style="margin-left:20px">
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="菜品管理" name="wxauth_data[]" <?php if(in_array('restaurant_product',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_product" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="餐桌设置" name="wxauth_data[]" <?php if(in_array('restaurant_table',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_table" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="餐桌管理" name="wxauth_data[]" <?php if(in_array('restaurant_tableWaiter',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_tableWaiter" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="点餐订单" name="wxauth_data[]" <?php if(in_array('restaurant_shop',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_shop" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="外卖订单" name="wxauth_data[]" <?php if(in_array('restaurant_takeaway',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_takeaway" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="预定" name="wxauth_data[]" <?php if(in_array('restaurant_booking',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_booking" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="寄存" name="wxauth_data[]" <?php if(in_array('restaurant_deposit',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_deposit" lay-skin="primary"/>
				  </div>
				  <div style="width: 120px;float: left;">
					  <input type="checkbox" title="排队" name="wxauth_data[]" <?php if(in_array('restaurant_queue',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_queue" lay-skin="primary"/>
				  </div>
				  <div style="clear:both;float: left;margin-bottom:10px"></div>
			  </div>
		  </div>

	  </div>
  </div>

						<div class="layui-form-item">
							<label class="layui-form-label" style="width:150px">附件存储类型：</label>
							<div class="layui-input-inline" style="width:300px">
								<select name="rinfo[type]" lay-filter="changetype">
									<option value="0" <?php if($rinfo['type']==0): ?>selected<?php endif; ?>>跟随平台附件设置</option>
									<option value="1" <?php if($rinfo['type']==1): ?>selected<?php endif; ?>>本地存储</option>
									<option value="2" <?php if($rinfo['type']==2): ?>selected<?php endif; ?>>阿里云</option>
									<option value="3" <?php if($rinfo['type']==3): ?>selected<?php endif; ?>>七牛云</option>
									<option value="4" <?php if($rinfo['type']==4): ?>selected<?php endif; ?>>腾讯云</option>
								</select>
							</div>
						</div>
						<div id="aliossset" <?php if($rinfo['type']!=2): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Access Key ID：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[alioss][key]" value="<?php echo $rinfo['alioss']['key']; ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Access Key Secret：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[alioss][secret]" value="<?php echo $rinfo['alioss']['secret']; ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Bucket名称：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[alioss][bucket]" value="<?php echo $rinfo['alioss']['bucket']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">空间名称</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">EndPoint（地域节点）</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[alioss][ossurl]" value="<?php echo $rinfo['alioss']['ossurl']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">如：oss-cn-qingdao.aliyuncs.com</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Bucket域名：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[alioss][url]" value="<?php echo $rinfo['alioss']['url']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">开头须加https://</div>
							</div>
						</div>
						<div id="qiniuset" <?php if($rinfo['type']!=3): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Accesskey：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[qiniu][accesskey]" value="<?php echo $rinfo['qiniu']['accesskey']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">在密钥管理中查找</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Secretkey：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[qiniu][secretkey]" value="<?php echo $rinfo['qiniu']['secretkey']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">在密钥管理中查找</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Bucket：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[qiniu][bucket]" value="<?php echo $rinfo['qiniu']['bucket']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">空间名称</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Url：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[qiniu][url]" value="<?php echo $rinfo['qiniu']['url']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">开头须加https://</div>
							</div>
						</div>
						<div id="cosset" <?php if($rinfo['type']!=4): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">APPID：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[cos][appid]" value="<?php echo $rinfo['cos']['appid']; ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">SecretID：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[cos][secretid]" value="<?php echo $rinfo['cos']['secretid']; ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">SecretKEY：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[cos][secretkey]" value="<?php echo $rinfo['cos']['secretkey']; ?>" class="layui-input">
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Bucket：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[cos][bucket]" value="<?php echo $rinfo['cos']['bucket']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">存储桶名称</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">bucket所属地域：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[cos][local]" value="<?php echo $rinfo['cos']['local']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">地域代码，如：ap-beijing</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:150px">Url：</label>
								<div class="layui-input-inline" style="width:300px">
									<input type="text" name="rinfo[cos][url]" value="<?php echo $rinfo['cos']['url']; ?>" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux">开头须加https://</div>
							</div>
						</div>
					  <div class="layui-form-item">
						  <label class="layui-form-label" style="width:150px">备注：</label>
						  <div class="layui-input-inline" style="width:300px;">
							  <textarea type="text" name="ainfo[remark]" class="layui-textarea" style="min-height:60px"><?php echo $ainfo['remark']; ?></textarea>
						  </div>
					  </div>

						<div class="layui-form-item">
							<div class="layui-input-block" style="margin-left:180px;">
								<button class="layui-btn" lay-submit lay-filter="formsubmit">提 交</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script>
	//address3('province','city','area',"<?php echo $ainfo['province']; ?>","<?php echo $ainfo['city']; ?>","<?php echo $ainfo['area']; ?>");
	layui.form.on('checkbox(checkall_all)',function(data){
		if(data.elem.checked){
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',true);
		}else{
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',false);
		}
		layui.form.render('checkbox');
	})
	layui.laydate.render({
		elem: '#endtime',
		type: 'datetime'
	});
	layui.form.on('radio(auth_type)', function(data){
		if(data.value == '0'){
			$('#authdataset').show();
		}else{
			$('#authdataset').hide();
		}
	})
	layui.form.on('radio(achouchengset)', function(data){
		if(data.value == '1'){
			$('#achouchengset1').show();
			$('#achouchengset2').hide();
		}else if(data.value == '2'){
			$('#achouchengset1').hide();
			$('#achouchengset2').show();
		}else{
			$('#achouchengset1').hide();
			$('#achouchengset2').hide();
		}
	})
	layui.form.on('checkbox(checkall)',function(data){
		if(data.elem.checked){
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',true);
		}else{
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',false);
		}
		layui.form.render('checkbox');
	})
	layui.form.on('select(changetype)',function(data){
		$('#aliossset').hide()
		$('#qiniuset').hide()
		$('#cosset').hide()
		if(data.value==2){
			$('#aliossset').show()
		}
		if(data.value==3){
			$('#qiniuset').show()
		}
		if(data.value==4){
			$('#cosset').show()
		}
	})

	layui.form.on('radio(choucheng_receivertype)', function(data){
		$('#choucheng_receivertype1').hide();
		$('#choucheng_receivertype2').hide();
		if(data.value == '1'){
			$('#choucheng_receivertype1').show();
		}
		if(data.value == '2'){
			$('#choucheng_receivertype2').show();
		}
	});
	layui.form.on('radio(choucheng_receivertype2_openidtype)', function(data){
		$('#choucheng_receivertype2_accountwx').hide();
		if(data.value == '1'){
			$('#choucheng_receivertype2_accountwx').show();
		}
	});



	layui.form.on('submit(formsubmit)', function(obj){
		var field = obj.field
		var index = layer.load();
		$.post("<?php echo url('save'); ?>",field,function(data){
			layer.close(index);
			<?php if(input('param.isopen')==1): ?>
				dialog(data.msg,data.status);
				if(data.status == 1){
					setTimeout(function(){
						parent.layer.closeAll();
						parent.tableIns.reload()
					},1000)
				}
			<?php else: ?>
				dialog(data.msg,data.status,data.url);
			<?php endif; ?>
		})
	})
  </script>
</body>
</html>
