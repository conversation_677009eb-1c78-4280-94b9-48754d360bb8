# 云喇叭故障排查指南

## 常见问题及解决方案

### 1. 播报失败：未知错误（返回RequestId）

**现象：**
```json
{
  "status": 0, 
  "msg": "播报失败：未知错误", 
  "data": {
    "RequestId": "04aec398-1559-4c46-94d7-bec64e392c37"
  }
}
```

**可能原因：**
1. 设备编号不正确或设备离线
2. 接口返回格式与预期不符
3. 设备未正确绑定到账户
4. 网络连接问题

**解决步骤：**

#### 步骤1：验证设备编号
- 确认设备编号格式正确（如：3MS9910006952）
- 检查设备是否在线（设备指示灯状态）
- 确认设备已正确绑定到您的账户

#### 步骤2：使用调试测试
1. 在设置页面点击"调试测试"按钮
2. 查看详细的请求和响应信息
3. 检查原始响应内容

#### 步骤3：检查网络连接
```bash
# 测试是否能访问云喇叭接口
curl -X POST http://cs.mqlinks.com/txmsgpush/ \
  -H "Content-Type: application/json" \
  -d '{"sbx_id":"您的设备号","agent_id":"{\"cmd\":\"voice\",\"msg\":\"测试\",\"msgid\":\"123456\"}"}'
```

### 2. 设备编号格式要求

**正确格式：**
- 通常为字母+数字组合
- 例如：3MS9910006952
- 长度一般为10-15位

**获取方式：**
- 从设备包装盒上的标签获取
- 从供应商提供的设备清单获取
- 联系设备供应商确认

### 3. 接口响应格式分析

云喇叭接口可能返回以下几种格式：

#### 格式1：标准成功响应
```json
{
  "code": 200,
  "msg": "success",
  "data": {...}
}
```

#### 格式2：状态字段响应
```json
{
  "status": 1,
  "msg": "播报成功"
}
```

#### 格式3：异步处理响应
```json
{
  "RequestId": "04aec398-1559-4c46-94d7-bec64e392c37"
}
```

### 4. 调试步骤

#### 4.1 启用详细日志
在 `app/model/CloudSpeaker.php` 中已经添加了详细的日志记录，可以查看系统日志获取更多信息。

#### 4.2 使用调试模式
1. 进入买单设置页面
2. 输入设备编号
3. 点击"调试测试"按钮
4. 查看弹出的详细调试信息

#### 4.3 手动测试接口
可以使用以下PHP代码手动测试：

```php
<?php
$data = [
    'cmd' => 'voice',
    'msg' => '测试消息',
    'msgid' => time().rand(1,999999)
];

$map = [
    'sbx_id' => '您的设备编号',
    'agent_id' => json_encode($data, true),
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://cs.mqlinks.com/txmsgpush/");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($map, true));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$error = curl_error($ch);
curl_close($ch);

echo "响应: " . $response . "\n";
echo "错误: " . $error . "\n";
?>
```

### 5. 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 设备离线 | 设备未连接网络 | 检查设备网络连接 |
| 设备编号错误 | 设备编号不存在 | 确认设备编号正确性 |
| 账户未绑定 | 设备未绑定到账户 | 联系供应商绑定设备 |
| 余额不足 | 账户余额不足 | 充值账户余额 |

### 6. 联系技术支持

如果以上步骤都无法解决问题，请收集以下信息联系技术支持：

1. **设备信息：**
   - 设备编号
   - 设备型号
   - 购买时间

2. **错误信息：**
   - 完整的错误响应
   - 调试测试的详细信息
   - 系统日志相关内容

3. **环境信息：**
   - 服务器IP地址
   - 网络环境描述
   - 防火墙设置

### 7. 临时解决方案

如果云喇叭暂时无法正常工作，可以：

1. **关闭云喇叭功能：**
   - 在设置页面将"云喇叭播报"设置为"关闭"
   - 这样不会影响正常的买单功能

2. **使用其他通知方式：**
   - 短信通知
   - 微信通知
   - 邮件通知

### 8. 预防措施

1. **定期测试：**
   - 每周进行一次连接测试
   - 确保设备正常工作

2. **备用方案：**
   - 准备备用设备
   - 配置多种通知方式

3. **监控日志：**
   - 定期查看系统日志
   - 及时发现问题

## 更新日志

- 2024-01-XX：增加调试模式和详细错误处理
- 2024-01-XX：优化接口响应解析逻辑
- 2024-01-XX：添加故障排查指南
