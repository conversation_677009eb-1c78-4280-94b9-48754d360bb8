<?php /*a:3:{s:77:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\business\index.html";i:1745486434;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>商户管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header">
            商户管理
            <span style="float: right; margin-right: 20px;">
              商家保证金总额：<span style="color: #1E9FFF; font-weight: bold;" id="totalDeposit"><?php echo $totalDeposit; ?></span> 元
            </span>
          </div>
          <div class="layui-card-body" pad15>
						<div class="layui-col-md4" style="padding-bottom:10px">
							<a class="layui-btn layuiadmin-btn-list" href="javascript:void(0)" onclick="openmax('<?php echo url('edit'); ?>')">添加</a>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<!-- <button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,1)">开启</button>
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="setst(0,0)">关闭</button> -->
							<!-- <button class="layui-btn layuiadmin-btn-list" onclick="location.href='?op=excel'+urlEncode(datawhere)">导出</button> -->
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="showHexiaoLoginQr()">登录地址</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline layuiadmin-input-useradmin">
								<label class="layui-form-label" style="width:60px">名称</label>
								<div class="layui-input-block" style="width:120px;margin-left:90px">
									<input type="text" name="name" autocomplete="off" class="layui-input">
								</div>
							</div>
							<?php if($clist): ?>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">分类</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="cid">
										<option value="">全部</option>
										<?php foreach($clist as $cv): ?>
										<option value="<?php echo $cv['id']; ?>"><?php echo $cv['name']; ?></option>
										<?php endforeach; ?>
									</select>
								</div>
							</div>
							<?php endif; ?>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部</option>
										<option value="0">待审核</option>
										<option value="1">已通过</option>
										<option value="2">已驳回</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit="" lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
          </div>
        </div>
    </div>
  </div>
	<div id="rechargeModel" style="width:500px;display:none;margin-top:30px">
		<div class="layui-form" lay-filter="">
			<input type="hidden" name="rechargemid" id="rechargemid"/>
			<div class="layui-form-item">
				<label class="layui-form-label">充值金额</label>
				<div class="layui-input-inline">
					<input type="text" name="rechargemoney" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
				</div>
				<div class="layui-form-mid layui-word-aux">输入负值表示扣除金额</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formRecharge">确定充值</button>
				</div>
			</div>
		</div>
	</div>
	
	<div id="rechargeBalanceModel" style="width:500px;display:none;margin-top:30px">
		<div class="layui-form" lay-filter="">
			<input type="hidden" name="rechargemid" id="rechargebalancemid"/>
			<div class="layui-form-item">
				<label class="layui-form-label">充值进货款</label>
				<div class="layui-input-inline">
					<input type="text" name="rechargemoney" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
				</div>
				<div class="layui-form-mid layui-word-aux">输入负值表示扣除金额</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formRechargeBalance">确定充值</button>
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script>
  var table = layui.table;
	var datawhere = {};
  //数据表
  var tableIns = table.render({
    elem: '#tabledata'
    ,url: "<?php echo app('request')->url(); ?>" //数据接口
    ,page: true //开启分页
    ,cols: [[ //表头
			{type:"checkbox"},
      {field: 'id', title: 'ID',  sort: true,width:80},
      {field: 'cname', title: '商家分类'},
      {field: 'name', title: '商家名称'},
      {field: 'logo', title: '商家图片',templet:function(d){return '<img src="'+d.logo+'" style="width:100px;"/>';}},
      {field: 'address', title: '商家地址'},
      {field: 'nickname', title: '头像昵称',templet:function(d){
				if(!d.nickname) return '';
				return '<img src="'+d.headimg+'" style="width:40px;height:40px"/> '+d.nickname;
      }},
      {field: 'linkman', title: '联系人',templet:function(d){ return d.linkman + '('+d.tel+')' }},
       {title: '拓展员信息', width: 200, templet: function(d){
        // 返回姓名和手机号组合的格式
        return d.tuozhan_name + '（' + d.tuozhan_phone + '）';
      }},
        {field: 'rate', title: '费率名称'}, // 添加费率名称列
		{field: 'balance', title: '进货款'}, // 添加进货款列
	  {field: 'money', title: "<?php echo t('余额'); ?>(点击余额可查看日志)",templet:function(d){
	      
				return '<a sytle="color:blue;" href="javascript:void(0)" onclick="openmax(\'<?php echo url('Business/moneylog'); ?>&isopen=1&bid='+d.id+'\')">'+d.money+'</a>';
				
      }},
      {field: 'money', title: "<?php echo t('余额'); ?>(点击可查看用户情况)",templet:function(d){
          
				return '<a sytle="color:blue;" href="javascript:void(0)" onclick="openmax(\'<?php echo url('Business/memberindex'); ?>&isopen=1&bid='+d.id+'\')">'+"查看用户"+'</a>';
				
	
      }},

      {field: 'sort', title: '创建时间',templet:function(d){ return date('Y-m-d H:i',d.createtime)}},
      {field: 'status', title: '状态',templet:function(d){ 
				if(d.status==0){
					return '<span style="color:red">待审核</span>';
				}else if(d.status==1){
					return '<span style="color:green">已通过</span>';
				}else if(d.status==2){
					return '<span style="color:red">已驳回</span>';
				}
			}},
      {field: 'operation', title: '操作', templet: function(d){
  var html = '';
  html += '<button class="table-btn" onclick="openmax(\'<?php echo url('edit'); ?>/id/' + d.id + '\')">编辑</button>';
  html += '<button class="table-btn" onclick="datadel(' + d.id + ')">删除</button>';
  html += '<button class="table-btn" onclick="recharge(' + d.id + ')">充值</button>';
  html += '<button class="table-btn" onclick="rechargeBalance(' + d.id + ')">充值进货款</button>';
  html += '<button class="table-btn" onclick="blogin(' + d.id + ')">登录</button>';
  if (d.status === 0 || d.status === 2) {
    html += '<button class="table-btn" onclick="setcheckst(\'' + d.id + '\', 1)">通过</button>';
  }
  if (d.status === 0 || d.status === 1) {
    html += '<button class="table-btn" onclick="setcheckst(\'' + d.id + '\', 2)">驳回</button>';
  }
  html += '<button class="table-btn" onclick="copydata(\'' + d.id + '\')">复制数据</button>';

  // 新增一键提现按钮
  html += '<button class="table-btn" onclick="withdraw(' + d.id + ')">一键提现</button>';

  return html;
}, width: 350}
    ]]
  ,done: function(res){ 
        if(res.totalDeposit){
            $('#totalDeposit').text(res.totalDeposit);
        }
    }
  });
	var rechargelayer
	function recharge(id){
		$('#rechargemid').val(id);
		rechargelayer = layer.open({type:1,area: ['500px', '200px'],title:"<?php echo t('余额'); ?>充值",content:$('#rechargeModel'),shadeClose:true})
	}
	
	// 进货款充值方法
	var rechargeBalancelayer
	function rechargeBalance(id){
		$('#rechargebalancemid').val(id);
		rechargeBalancelayer = layer.open({type:1,area: ['500px', '200px'],title:"进货款充值",content:$('#rechargeBalanceModel'),shadeClose:true})
	}
	
	function blogin(id){
		window.open("<?php echo url('blogin'); ?>/id/"+id);
	}
	//充值提交
  layui.form.on('submit(formRecharge)', function(obj){
		var index= layer.load();
    $.post("<?php echo url('recharge'); ?>",obj.field,function(data){
			layer.close(index);
			dialog(data.msg,data.status,data.url);
			if(data.status==1){
				tableIns.reload({
					where: datawhere
				});
			}
			layer.close(rechargelayer);
		})
  });
  
  //进货款充值提交
  layui.form.on('submit(formRechargeBalance)', function(obj){
		var index= layer.load();
    $.post("<?php echo url('rechargeBalance'); ?>",obj.field,function(data){
			layer.close(index);
			dialog(data.msg,data.status,data.url);
			if(data.status==1){
				tableIns.reload({
					where: datawhere
				});
			}
			layer.close(rechargeBalancelayer);
		})
  });
	//排序
	table.on('sort(tabledata)', function(obj){
		datawhere.field = obj.field;
		datawhere.order = obj.type;
		tableIns.reload({
			initSort: obj,
			where: datawhere
		});
	});
	//检索
	layui.form.on('submit(LAY-app-forumreply-search)', function(obj){
		var field = obj.field
		var olddatawhere = datawhere
		datawhere = field
		datawhere.field = olddatawhere.field
		datawhere.order = olddatawhere.order
		tableIns.reload({
			where: datawhere,
			page: {curr: 1}
		});
	})
	
	//审核
	function setcheckst(id,st){
		if(st == 2){
			var html = '';
			html+='	<div class="layui-form-item" style="margin-top:40px;margin-right:20px;">';
			html+='		<label class="layui-form-label" style="width:80px">驳回原因</label>';
			html+='		<div class="layui-input-inline" style="width:350px">';
			html+='			<textarea type="text" id="check_reason" class="layui-textarea"></textarea>';
			html+='		</div>';
			html+='	</div>';
			var checkLayer = layer.open({type:1,area:['500px','250px'],title:false,content:html,shadeClose:true,btn: ['确定', '取消'],
				yes:function(){
					var index = layer.load();
					$.post("<?php echo url('setcheckst'); ?>",{id:id,st:st,reason:$('#check_reason').val()},function(res){
						layer.close(index);
						dialog(res.msg,res.status);
						layer.close(checkLayer);
						tableIns.reload()
					})
				}
			})
		}else{
			layer.confirm('确定要审核通过吗?',{icon: 7, title:'操作确认'}, function(index){
				layer.close(index);
				var index = layer.load();
				$.post("<?php echo url('setcheckst'); ?>",{id:id,st:st},function(data){
					layer.close(index);
					dialog(data.msg,data.status);
					tableIns.reload()
				})
			});
		}
	}
	function withdraw(bid){
    var html = '';
    html += '<div class="layui-form-item" style="margin-top:40px;margin-right:20px;">';
    html += '    <label class="layui-form-label" style="width:80px">提现金额</label>';
    html += '    <div class="layui-input-inline" style="width:150px">';
    html += '        <input type="number" id="withdraw_amount" class="layui-input" placeholder="请输入金额">';
    html += '    </div>';
    html += '</div>';
    html += '<div class="layui-form-item" style="margin-top:20px;">';
    html += '    <label class="layui-form-label" style="width:80px">提现方式</label>';
    html += '    <div class="layui-input-inline" style="width:150px">';
    html += '        <select id="withdraw_method" class="layui-input">';
    html += '            <option value="银行卡">银行卡</option>';
    html += '            <option value="支付宝">支付宝</option>';
    html += '            <option value="微信">微信</option>';
    html += '        </select>';
    html += '    </div>';
    html += '</div>';
    
    var checkLayer = layer.open({
        type: 1,
        area: ['500px', '300px'],
        title: false,
        content: html,
        shadeClose: true,
        btn: ['确定', '取消'],
        yes: function(){
            var index = layer.load();
            var amount = $('#withdraw_amount').val();
            var paytype = $('#withdraw_method').val();
            $.post("<?php echo url('withdraw'); ?>", {id: bid, paytype: paytype, amount: amount}, function(res){
                layer.close(index);
                dialog(res.msg, res.status);
                layer.close(checkLayer);
                tableIns.reload();
            });
        }
    });
}

	//删除
	function datadel(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id);
		}
		layer.confirm('删除商家后，该商家下的商品、订单等会一并删除，确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('del'); ?>",{ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	//上下架
	function setst(id,st){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id);
		}
		layer.confirm('确定要'+(st==0?'关闭':'开启')+'吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('setst'); ?>",{ids:ids,st:st},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
	}
	function showHexiaoLoginQr(){
		layer.open({type:1,area:['500px','450px'],content:'<div style="margin-top:40px;text-align:center"><div style="margin:20px 110px;text-align:center" id="hexiaologinqr"></div><div style="font-size:30px;color:#e94745"><span style="font-size:24px"></span></div><div style="font-size:14px;text-align:center;margin:10px 20px">手机端：<?php echo m_url('admin/index/index'); ?></div><div style="font-size:14px;text-align:center;margin:10px 20px">电脑端：<?php echo PRE_URL; ?></div></div>',title:false,shadeClose:true});
		var qrcode = new QRCode('hexiaologinqr', {
			text: 'your content',
			width: 280,
			height: 280,
			colorDark : '#000000',
			colorLight : '#ffffff',
			correctLevel : QRCode.CorrectLevel.H
		});
		qrcode.clear();
		qrcode.makeCode("<?php echo m_url('admin/index/index'); ?>");
	}

	//复制数据
	function copydata(id){
		var html = '';
		html ='<div style="margin:20px auto;">';
		html+='	<blockquote class="layui-elem-quote" style="margin:10px">复制操作会对复制到的商户的数据产生较大影响，请提前做好备份</blockquote>';
		html+='	<div class="layui-form" lay-filter="">';
		html+='		<div class="layui-form-item">';
		html+='			<label class="layui-form-label" style="width:130px">复制到的商户ID：</label>';
		html+='			<div class="layui-input-inline">';
		html+='				<input type="text" name="info[toid]" value="'+id+'" required lay-verify="required" placeholder="" autocomplete="off" class="layui-input">';
		html+='			</div>';
		html+='		</div>';
		html+='		<div class="layui-form-item">';
		html+='			<label class="layui-form-label" style="width:130px">要复制的数据：</label>';
		html+='			<div class="layui-input-block" style="margin-left:150px;">';
		html+='				<div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">';
		html+='					<input type="checkbox" title="全部选择" lay-skin="primary" lay-filter="checkall_all"/>';
		html+='				</div>';
		html+='				<div style="float:left;margin-left:0">';
		html+='					<div style="margin-left:10px">';
		<?php if($auth_data=='all' || in_array('ShopProduct/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="商城商品" name="module_data[]"  title="商城商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('CollageProduct/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="拼团商品" name="module_data[]"  title="拼团商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('KanjiaProduct/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="砍价商品" name="module_data[]"  title="砍价商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('SeckillProduct/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="秒杀商品" name="module_data[]"  title="秒杀商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('TuangouProduct/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="团购商品" name="module_data[]"  title="团购商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('LuckyCollageProduct/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="幸运拼团商品" name="module_data[]"  title="幸运拼团商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('Shortvideo/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="短视频" name="module_data[]"  title="短视频" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('Article/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="文章列表" name="module_data[]"  title="文章列表" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('Yuyue/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="预约服务商品" name="module_data[]"  title="预约服务商品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('KechengList/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="知识付费课程" name="module_data[]"  title="知识付费课程" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('Restaurant/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="餐饮菜品" name="module_data[]"  title="餐饮菜品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('Coupon/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="优惠券" name="module_data[]"  title="优惠券" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; if($auth_data=='all' || in_array('KechengList/*',$auth_data)): ?>
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="租机产品" name="module_data[]"  title="租机产品" lay-skin="primary"/>';
		html+='						</div>';
		<?php endif; ?>
		
		html+='						<div style="min-width:100px;float: left;">';
		html+='							<input type="checkbox" value="设计页面" name="module_data[]"  title="设计页面" lay-skin="primary"/>';
		html+='						</div>';
		html+='					</div>';
		html+='				</div>';
		html+='			</div>';
		html+='		</div>';
		html+='		<div class="layui-form-item">';
		html+='			<label class="layui-form-label" style="width:130px">删除原有数据：</label>';
		html+='			<div class="layui-input-inline">';
		html+='				<input type="radio" name="info[delold]" value="0" title="否" checked>';
		html+='				<input type="radio" name="info[delold]" value="1" title="是">';
		html+='			</div>';
		html+='			<div class="layui-form-mid layui-word-aux">是否删除掉要复制到的商户的原来的数据</div>';
		html+='		</div>';
		html+='		<div class="layui-form-item" style="margin-top:40px">';
		html+='			<label class="layui-form-label" style="width:130px"></label>';
		html+='			<div class="layui-input-block">';
		html+='				<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formCopydata">确定复制</button>';
		html+='			</div>';
		html+='		</div>';
		html+='	</div>';
		html+='</div>';
		var copydatalayer = layer.open({type:1,area:['800px','600px'],content:html,title:'平台数据复制到商户',shadeClose:true});
		layui.form.render();
		layui.form.on('submit(formCopydata)', function(obj){
			var field = obj.field;
			console.log(field);
			var index= layer.load();
			$.post("<?php echo url('copydata'); ?>",field,function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				if(data.status == 1){
					layer.close(copydatalayer);
				}
			})
		})
		layui.form.on('checkbox(checkall_all)',function(data){
			if(data.elem.checked){
				$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',true);
			}else{
				$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',false);
			}
			layui.form.render('checkbox'); 
		});
		layui.form.on('checkbox(checkall)',function(data){
			if(data.elem.checked){
				$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',true);
			}else{
				$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',false);
			}
			layui.form.render('checkbox'); 
		})
	}

			
	</script>
</body>
</html>