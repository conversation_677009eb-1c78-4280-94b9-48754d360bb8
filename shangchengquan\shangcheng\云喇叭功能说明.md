# 云喇叭功能说明

## 功能概述
云喇叭功能为买单支付成功后自动播报收款信息，提升商户收款体验。

## 功能特点
- 支付成功后自动播报收款金额
- 可配置开启/关闭云喇叭功能
- 支持自定义设备编号
- 提供连接测试功能
- 播报失败不影响主要业务流程

## 文件修改清单

### 1. 新增文件
- `app/model/CloudSpeaker.php` - 云喇叭通知封装类
- `cloud_speaker.sql` - 数据库更新SQL
- `云喇叭功能说明.md` - 功能说明文档

### 2. 修改文件
- `app/controller/Maidan.php` - 买单控制器
  - 增加云喇叭配置字段
  - 增加配置保存功能
  - 增加测试连接方法
  
- `app/home/<USER>/set.html` - 买单设置页面
  - 增加云喇叭开关配置
  - 增加设备号输入框
  - 增加测试连接按钮
  
- `app/model/Payorder.php` - 支付订单模型
  - 在买单支付成功后调用云喇叭播报

## 数据库变更
需要执行 `cloud_speaker.sql` 文件中的SQL语句：

```sql
-- 添加云喇叭开关字段
ALTER TABLE `ddwx_admin_set` ADD COLUMN `cloud_speaker_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '云喇叭开关：0关闭，1开启';

-- 添加云喇叭设备号字段
ALTER TABLE `ddwx_admin_set` ADD COLUMN `cloud_speaker_device_id` varchar(50) NOT NULL DEFAULT '' COMMENT '云喇叭设备编号';
```

## 使用说明

### 1. 配置云喇叭
1. 进入后台管理 -> 买单管理 -> 设置
2. 找到"云喇叭播报"选项，选择"开启"
3. 输入云喇叭设备编号（如：3MS9910006952）
4. 点击"测试连接"验证设备是否正常
5. 点击"保存"完成配置

### 2. 播报效果
- 买单支付成功后，云喇叭会自动播报："微信收款XX元"
- 播报金额为实际支付金额（paymoney）
- 播报失败不会影响正常的支付流程

### 3. 接口说明
云喇叭使用的是第三方接口：
- 接口地址：`http://cs.mqlinks.com/txmsgpush/`
- 请求方式：POST
- 数据格式：JSON

## 核心类方法说明

### CloudSpeaker类主要方法：

#### sendNotification($aid, $message, $device_id = '')
发送云喇叭通知
- `$aid`: 应用ID
- `$message`: 播报消息
- `$device_id`: 设备编号（可选）

#### sendMaidanPayNotification($order)
发送买单支付成功播报
- `$order`: 订单信息数组

#### testConnection($aid, $device_id = '')
测试云喇叭连接
- `$aid`: 应用ID  
- `$device_id`: 设备编号（可选）

#### getConfig($aid)
获取云喇叭配置
- `$aid`: 应用ID

#### saveConfig($aid, $enabled, $device_id)
保存云喇叭配置
- `$aid`: 应用ID
- `$enabled`: 是否开启
- `$device_id`: 设备编号

## 注意事项
1. 云喇叭播报功能依赖网络连接，请确保服务器能正常访问外网
2. 设备编号需要从云喇叭供应商处获取
3. 播报失败会记录到日志中，不影响正常业务流程
4. 建议在配置完成后先进行测试连接，确保设备正常工作

## 故障排查
1. 如果播报不成功，请检查：
   - 网络连接是否正常
   - 设备编号是否正确
   - 云喇叭设备是否在线
   
2. 查看日志：
   - 系统日志中会记录云喇叭相关的操作和错误信息
   - 可通过日志排查具体问题

## 扩展说明
如需要支持其他类型的订单播报，可以参考买单播报的实现方式：
1. 在对应的支付成功方法中调用 `CloudSpeaker::sendNotification()`
2. 根据订单类型构建相应的播报消息
3. 确保在事务提交成功后再进行播报
