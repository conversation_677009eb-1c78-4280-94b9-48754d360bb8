<template>
	<view class="container ranking_content" :style="'background-image:url('+bg + ')'">


		<view class="ranking_content_nav" v-if="rankConfig.rank_switch">

			<view class="ranking_content_nav_subsection tn-padding">
				<view class="u-subsection u-subsection--button" style="border: 1px solid rgb(255, 255, 255); 
						background: rgba(255, 255, 255, 0.31); 
						box-shadow: rgba(255, 255, 255, 0.31) 5px 3px 7px inset;
						 backdrop-filter: blur(84.2667px); border-radius: 17px;
						  height:100rpx; padding: 0px;">
					<view class="navs">
						<view v-for="(item,idd) in list" :key="idd" :class="{
		                  'active' : tabsIndex=== idd}" class="item"
							@click="tabsChange(idd)">
							{{item.name}}
						</view>
					</view>
				</view>

			</view>

		</view>

		<view v-if="!rankConfig.rank_switch" class="no-rank-tip">
			<text>排行榜功能未开启</text>
		</view>

		<template v-else>
			<!-- top three -->
			<view class="ranking_content_top" v-if="rankList.length > 0">



				<!-- left -->
				<view class="ranking_content_top_left" v-if="rankList[1]">
					<image style="width: 240rpx;height: 300rpx;" :src="left" mode="as"></image>
					<image
						style="width: 125rpx;height: 125rpx;border: 3px solid rgb(230, 208, 255);border-radius: 50%;position: relative;bottom: 410rpx;left: 20rpx;"
						:src="rankList[1].avatar" mode="aspectFit"></image>
					<image style="width: 100rpx;height: 90rpx;position: relative;bottom: 480rpx;left: -82rpx;" :src="ding"
						mode="aspectFit"></image>
					<view style="position: relative;bottom: 350rpx;left: 60rpx;">
						<view style="color:#956AAA; font-size: 28rpx;display: inline-block;transform:rotate(-10deg);">
							{{rankList[1].nickname}}
						</view>
						<view>
							<image style="width: 120rpx;height: 120rpx;" :src="er" mode="aspectFit"></image>
						</view>
					</view>
				</view>

				<!-- center -->
				<view class="ranking_content_top_center" v-if="rankList[0]">
					<image style="width: 330rpx;height: 470rpx;" :src="zhong" mode="as"></image>
					<image
						style="width: 150rpx;height: 150rpx;border: 3px solid rgb(230, 208, 255);border-radius: 50%;position: relative;bottom: 600rpx;left: 80rpx;"
						:src="rankList[0].avatar" mode="aspectFit"></image>
					<image style="width: 100rpx;height: 80rpx;position: relative;bottom: 695rpx;left: -15rpx;" :src="dingz"
						mode="aspectFit"></image>
					<view style="position: relative;bottom: 500rpx;left: 75rpx;">
						<view style="color:#437C84; position: relative;font-size: 32rpx;display: inline-block;">
							{{rankList[0].nickname}}
						</view>
						<view>
							<image style="width: 140rpx;height: 140rpx;position: relative;" :src="yi" mode="aspectFit">
							</image>
						</view>
					</view>
				</view>
				<!-- right -->
				<view class="ranking_content_top_right" v-if="rankList[2]">
					<image style="width: 240rpx;height: 300rpx;" :src="you" mode="as"></image>
					<image
						style="width: 120rpx;height: 120rpx;border: 3px solid rgb(230, 208, 255);border-radius: 50%;position: relative;bottom: 400rpx;left: 110rpx;"
						:src="rankList[2].avatar" mode="aspectFit"></image>
					<image style="width: 100rpx;height: 80rpx;position: relative;bottom: 465rpx;left: 30rpx;" :src="dingy"
						mode="aspectFit"></image>
					<view style="position: relative;bottom: 400rpx;left: 90rpx;">
						<view
							style="color:#3F7AA0; position: relative;font-size: 28rpx;display: inline-block;transform:rotate(10deg);">
							{{rankList[2].nickname}}
						</view>
						<view>
							<image style="width: 120rpx;height: 120rpx;left:0rpx;" :src="san" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
			<!-- top list -->
			<view class="ranking_content_list">
				<view class="ranking_content_list_top">
					<text>排名</text>
					<text>获得积分</text>
				</view>
				<view v-if="rankList.length === 0" class="no-data">
					<text>暂无排行数据</text>
				</view>
				<view v-else v-for="(item,i) in rankList" :key="i" class="ranking_content_list_item" v-if="i>2">
					<view style="color: #99A7AD;font-size: 50rpx;margin-right: 20rpx;width: 50rpx;">{{i+1}}</view>
					<image style="width: 90rpx;height: 90rpx;display: inline-block;border-radius: 50%;" :src="item.avatar">
					</image>
					<view class="ranking_content_list_item_name">
						<text style="color:#333330;font-size: 35rpx;margin-bottom: 10rpx;">{{item.nickname}}</text>
					</view>
					<text>{{item.rank_score}}积分</text>
				</view>
			</view>
		</template>



	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
			      san: app.globalData.pre_url + '/static/img/scoreshop/rank/3.png',
			      bg: app.globalData.pre_url + '/static/img/scoreshop/rank/bg.png',
			      yi: app.globalData.pre_url + '/static/img/scoreshop/rank/1.png',
			      er: app.globalData.pre_url + '/static/img/scoreshop/rank/2.png',
			      dingy: app.globalData.pre_url + '/static/img/scoreshop/rank/dingy.png',
			      dingz: app.globalData.pre_url + '/static/img/scoreshop/rank/dingz.png',
			      ding: app.globalData.pre_url + '/static/img/scoreshop/rank/ding.png',
			      left: app.globalData.pre_url + '/static/img/scoreshop/rank/left.png',
			      zhong: app.globalData.pre_url + '/static/img/scoreshop/rank/zhong.png',
			      you: app.globalData.pre_url + '/static/img/scoreshop/rank/you.png',


				activeItemStyle: {
					fontSize: `36rpx`
				},
				list: [],
				rankConfig: {
					rank_switch: 0,
					rank_types: [],
					rank_names: {}
				},
				tabsIndex: 0,
				swiperIndex: 0,
				swiperTop: 0,
				swiperHeight: 0,
				rankList: []
			};
		},
		onLoad: function() {
			this.getRankConfig();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			getRankConfig() {
				var that = this;
				app.post('ApiScoreshop/getRankConfig', {}, function(res) {
					if(res.status === 1) {
						that.rankConfig = res.data;
						// 根据配置生成榜单列表
						that.list = that.rankConfig.rank_types.map(type => ({
							name: that.rankConfig.rank_names[type],
							type: type
						}));
						// 获取第一个榜单数据
						if(that.list.length > 0) {
							that.getdata();
						}
					}
				});
			},
			tabsChange(index) {
				this.tabsIndex = index;
				this.getdata();
			},
			getdata: function() {
				var that = this;
				if(!that.list.length) return;
				
				that.loading = true;
				app.post('ApiScoreshop/rank', {
					type: that.list[that.tabsIndex].type
				}, function(res) {
					that.loading = false;
					if(res.status === 1) {
						that.rankList = res.data.list || [];
					}
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.ranking_content {
		position: relative;
		height: 100%;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		/* #ifdef MP-WEIXIN  || APP-PLUS */
		overflow: auto;
		/* #endif */

		.no-rank-tip,
		.no-data {
			text-align: center;
			padding: 40rpx;
			color: #666;
			font-size: 28rpx;
		}

		.ranking_content_nav {
			position: relative;
			/* #ifndef MP-WEIXIN */
			padding-top: 30rpx;
			/* #endif */
			/* #ifdef MP-WEIXIN */
			padding-top: 20rpx;
			/* #endif */
			.ranking_content_nav_subsection {
				width: 100%;
				margin: 0 auto;
			}
		}

		.ranking_content_top {
			// border: solid 1px red;
			width: 60%;
			height: 600rpx;
			margin: 40rpx auto 0;
			display: flex;
			justify-content: center;
			padding-top: 120rpx;

			.ranking_content_top_center {
				z-index: 3;
			}

			.ranking_content_top_left {
				position: relative;
				left: 120rpx;
				top: 140rpx;
			}

			.ranking_content_top_right {
				position: relative;
				right: 140rpx;
				top: 170rpx;
			}
		}

		.ranking_content_list {
			background: rgba(255, 255, 255, 0.59);
			border: 1px solid rgb(255, 255, 255);
			backdrop-filter: blur(32.619361877441406px);
			border-radius: 40rpx 40rpx 0px 0px;
			opacity: 0.8;
			// border: solid 1px red;
			position: relative;
			bottom: 110rpx;
			z-index: 99999999;
			width: 90%;
			margin: 0 auto;
			// padding-bottom: 100rpx;
			// height: 500rpx;
			// overflow: scroll;


			.ranking_content_list_top {
				display: flex;
				justify-content: space-between;
				color: #333330;
				padding: 40rpx 60rpx;
				font-size: 30rpx;
			}

			.ranking_content_list_item {
				display: flex;
				// justify-content: space-around;
				flex-direction: row;
				align-items: center;
				height: 160rpx;
				padding: 30rpx 60rpx 0 50rpx;
				border-bottom: 1px solid rgba(214, 229, 228, 0.57);
				// border: solid 1px red;

				.ranking_content_list_item_name {
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					margin: 0 20rpx 0 20rpx;
					// border: solid 1px red;
					width: 260rpx;
				}
			}
		}
	}

	::v-deep .u-subsection--button {
		background: rgba(255, 255, 255, 0.31) !important;
		border: 1px solid rgb(255, 255, 255) !important;
		box-shadow: inset 5px 3px 7px rgba(255, 255, 255, 0.31) !important;
		backdrop-filter: blur(84.26667785644531px) !important;
		border-radius: 35rpx !important;
		height: 50rpx;
		padding: 0;
	}

	::v-deep .u-subsection--button__bar {
		background: linear-gradient(rgba(255, 255, 255, 0.4196078431372549) 0%, rgba(255, 255, 255, 0.4196078431372549) 100%);
		border: 1px solid rgb(255, 255, 255);
		box-shadow: inset 5px 3px 7px rgba(255, 255, 255, 0.31);
		;
		backdrop-filter: blur(84.26667785644531px);
		border-radius: 40px 0px 0px 40px;
		padding: 0 !important;
	}


	.u-subsection {
		
		.navs{
			display: flex;
			justify-content: space-around;
			align-items: center;
			height: 40px;
			.item{
				padding:6px 20px;
			}
			
			.active{color: azure;border-radius: 5px;background-image: repeating-linear-gradient(-45deg,#da84c3,#ffc1d5);}
		}

		position: relative;
		overflow: hidden;
		/* #ifndef APP-NVUE */
		width: 100%;
		box-sizing: border-box;
		/* #endif */

		&--button {
			height: 32px;
			background-color: rgb(238, 238, 239);
			padding: 3px;
			border-radius: 3px;
			align-items: stretch;

			&__bar {
				background-color: #ffffff;
				border-radius: 3px !important;
			}
		}

		&--subsection {
			height: 30px;
		}

		&__bar {
			position: absolute;
			/* #ifndef APP-NVUE */
			transition-property: transform, color;
			transition-duration: 0.3s;
			transition-timing-function: ease-in-out;
			/* #endif */

			&--first {
				border-top-left-radius: 3px;
				border-bottom-left-radius: 3px;
				border-top-right-radius: 0px;
				border-bottom-right-radius: 0px;
			}

			&--center {
				border-top-left-radius: 0px;
				border-bottom-left-radius: 0px;
				border-top-right-radius: 0px;
				border-bottom-right-radius: 0px;
			}

			&--last {
				border-top-left-radius: 0px;
				border-bottom-left-radius: 0px;
				border-top-right-radius: 3px;
				border-bottom-right-radius: 3px;
			}
		}

		&__item {

			flex: 1;
			justify-content: center;
			align-items: center;
			// vue环境下，需要设置相对定位，因为滑块为绝对定位，item需要在滑块的上面
			position: relative;

			&--no-border-right {
				border-right-width: 0 !important;
			}

			&--first {
				border-top-left-radius: 3px;
				border-bottom-left-radius: 3px;
			}

			&--last {
				border-top-right-radius: 3px;
				border-bottom-right-radius: 3px;
			}

			&__text {
				font-size: 12px;
				line-height: 12px;

				align-items: center;
				transition-property: color;
				transition-duration: 0.3s;
			}
		}
	}
</style>