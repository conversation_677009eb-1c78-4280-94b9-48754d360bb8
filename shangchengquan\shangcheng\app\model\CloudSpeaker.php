<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
 *     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

// +----------------------------------------------------------------------
// | 云喇叭通知封装类
// +----------------------------------------------------------------------
namespace app\model;
use think\facade\Db;
use think\facade\Log;

class CloudSpeaker
{
    /**
     * 发送云喇叭通知
     * @param int $aid 应用ID
     * @param string $message 播报消息
     * @param string $device_id 设备编号（可选，如果不传则使用系统配置的设备号）
     * @return array 返回结果
     */
    public static function sendNotification($aid, $message, $device_id = '')
    {
        try {
            // 获取云喇叭配置
            $config = self::getConfig($aid);
            
            // 检查是否开启云喇叭功能
            if (!$config['enabled']) {
                return ['status' => 0, 'msg' => '云喇叭功能未开启'];
            }
            
            // 使用传入的设备号或系统配置的设备号
            $sbx_id = $device_id ?: $config['device_id'];
            
            if (empty($sbx_id)) {
                return ['status' => 0, 'msg' => '设备编号未配置'];
            }
            
            // 构建请求数据
            $data = [
                'cmd' => 'voice',
                'msg' => $message,
                'msgid' => time() . rand(1, 999999)
            ];
            
            $map = [
                'sbx_id' => $sbx_id,
                'agent_id' => json_encode($data, true),
            ];
            
            // 发送请求
            $url = "http://cs.mqlinks.com/txmsgpush/";
            $response = self::curlRequest($url, json_encode($map, true));
            
            // 记录日志
            Log::write('云喇叭通知发送：设备号=' . $sbx_id . '，消息=' . $message . '，响应=' . $response);
            
            // 解析响应
            $result = json_decode($response, true);
            if ($result && isset($result['code']) && $result['code'] == 200) {
                return ['status' => 1, 'msg' => '播报成功', 'data' => $result];
            } else {
                return ['status' => 0, 'msg' => '播报失败：' . ($result['msg'] ?? '未知错误'), 'data' => $result];
            }
            
        } catch (\Exception $e) {
            Log::write('云喇叭通知异常：' . $e->getMessage());
            return ['status' => 0, 'msg' => '播报异常：' . $e->getMessage()];
        }
    }
    
    /**
     * 获取云喇叭配置
     * @param int $aid 应用ID
     * @return array 配置信息
     */
    public static function getConfig($aid)
    {
        $admin_set = Db::name('admin_set')->where('aid', $aid)->find();
        
        return [
            'enabled' => $admin_set['cloud_speaker_enabled'] ?? 0, // 是否开启云喇叭
            'device_id' => $admin_set['cloud_speaker_device_id'] ?? '', // 设备编号
        ];
    }
    
    /**
     * 保存云喇叭配置
     * @param int $aid 应用ID
     * @param int $enabled 是否开启
     * @param string $device_id 设备编号
     * @return bool 保存结果
     */
    public static function saveConfig($aid, $enabled, $device_id)
    {
        $data = [
            'cloud_speaker_enabled' => $enabled ? 1 : 0,
            'cloud_speaker_device_id' => trim($device_id),
        ];
        
        return Db::name('admin_set')->where('aid', $aid)->update($data);
    }
    
    /**
     * 发送买单成功播报
     * @param array $order 订单信息
     * @return array 播报结果
     */
    public static function sendMaidanPayNotification($order)
    {
        if (!$order) {
            return ['status' => 0, 'msg' => '订单信息不存在'];
        }
        
        // 构建播报消息
        $message = self::buildMaidanPayMessage($order);
        
        // 发送播报
        return self::sendNotification($order['aid'], $message);
    }
    
    /**
     * 构建买单支付成功的播报消息
     * @param array $order 订单信息
     * @return string 播报消息
     */
    private static function buildMaidanPayMessage($order)
    {
        $money = $order['paymoney'] ?? $order['money'] ?? 0;
        
        // 格式化金额，去掉小数点后的0
        $moneyStr = rtrim(rtrim(number_format($money, 2), '0'), '.');
        
        // 构建基础消息
        $message = "微信收款{$moneyStr}元";
        
        // 如果有会员信息，可以添加会员昵称
        if (!empty($order['mid'])) {
            $member = Db::name('member')->where('id', $order['mid'])->find();
            if ($member && !empty($member['nickname'])) {
                // 可以选择是否包含会员信息，这里先注释掉
                // $message = "会员{$member['nickname']}，" . $message;
            }
        }
        
        return $message;
    }
    
    /**
     * CURL请求封装
     * @param string $url 请求地址
     * @param string $data 请求数据
     * @param int $timeout 超时时间
     * @return string 响应结果
     */
    private static function curlRequest($url, $data = '', $timeout = 30)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SAFE_UPLOAD, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception('CURL请求错误：' . $error);
        }
        
        return $response;
    }
    
    /**
     * 测试云喇叭连接
     * @param int $aid 应用ID
     * @param string $device_id 设备编号（可选）
     * @return array 测试结果
     */
    public static function testConnection($aid, $device_id = '')
    {
        $testMessage = "云喇叭连接测试，当前时间：" . date('H点i分');
        return self::sendNotification($aid, $testMessage, $device_id);
    }
}
