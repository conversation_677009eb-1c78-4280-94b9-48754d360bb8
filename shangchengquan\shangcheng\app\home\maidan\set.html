<!DOCTYPE html><!--仅平台可用-->
<html>
<head>
  <meta charset="utf-8">
  <title>聚合收款码</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  {include file="public/css"/}
	<style>
		.layui-imgbox .layui-imgbox-close{z-index: 6;}
	</style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
		<div class="layui-card layui-col-md12">
			<div class="layui-card-header"><i class="fa fa-cog"></i> 聚合收款码</div>
			<div class="layui-card-body" pad15>
				<div class="layui-form form-label-w8" lay-filter="">
					{if in_array('wx',$platform_arr)}
					<div class="layui-form-item">
						<label class="layui-form-label">微信设置：</label>
						<div class="layui-form-mid" style="width: 500px;">
							开发管理->开发设置->扫普通链接二维码打开小程序
							<a href="https://developers.weixin.qq.com/miniprogram/introduction/qrcode.html" target="_blank">点击查看配置规则</a>
							<br/>
							协议类型：<span class="layui-default-link">https</span> <br/>
							选择大小写：<span class="layui-default-link">小写</span> <br/>
							二维码规则：<span class="layui-default-link">{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay
							<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText('{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay')">复制</button></span><br/>
							{if $auth_data=='all' || in_array('Business/index',$auth_data)}
							前缀占用规则：<span class="layui-default-link">开启多商户使用选择 “不占用”</span> 平台开启此功能，多商户的收款码即支持聚合码<br/>
							{/if}
							小程序功能页面：<span class="layui-default-link">pagesB/maidan/pay
							<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText('pagesB/maidan/pay')">复制</button></span><br/>
						</div>
					</div>
					{/if}
					{if in_array('alipay',$platform_arr)}
					<div class="layui-form-item">
						<label class="layui-form-label">支付宝设置：</label>
						<div class="layui-form-mid" style="width: 500px;">
							选择应用->审核&发布->小程序码->关联普通链接二维码
							<a href="https://opendocs.alipay.com/b/04ne6i" target="_blank">点击查看配置规则</a>
							<br/>
							{if $auth_data=='all' || in_array('Business/index',$auth_data)}
							关联模式：<span class="layui-default-link">开启多商户使用选择 “模糊匹配”</span>平台开启此功能，多商户的收款码即支持聚合码 <br/>
							{/if}
							二维码地址 ：<span class="layui-default-link">{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay
							<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText('{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay')">复制</button></span><br/>
							小程序功能页：<span class="layui-default-link">pagesB/maidan/pay
							<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText('pagesB/maidan/pay')">复制</button></span><br/>
						</div>
					</div>
					{/if}
					<div class="layui-form-item">
						<label class="layui-form-label">二维码：</label>
						<div class="layui-input-inline">
							<div style="width:100%;margin:10px 0" id="urlqr"></div>
							<div style="width:100%;text-align:center"><button class="layui-btn layui-btn-sm layui-btn-primary" onclick="showwxqrcode('pagesB/maidan/pay')">查看小程序码</button></div>
						</div>
						<div class="layui-form-mid"></div>
					</div>
					

					<div class="layui-form-item">
						<label class="layui-form-label">链接地址：</label>
						<div class="layui-form-mid" style="width: 500px;">
							{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay
							<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText('{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay')">复制</button>
						</div>
						<div class="layui-form-mid"></div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">页面路径：</label>
						<div class="layui-form-mid" style="width: 500px;">
							pagesB/maidan/pay
							<button class="layui-btn layui-btn-xs layui-btn-primary" onclick="copyText('pagesB/maidan/pay')">复制</button>
						</div>
						<div class="layui-form-mid"></div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">收款强制登录：</label>
						<div class="layui-input-inline" style="width: 500px;">
							<input type="radio"  name="maidan_login" {if $admin['maidan_login']==1}checked{/if} value="1" title="开启" lay-filter="maidan_login" />
							<input type="radio"  name="maidan_login" {if $admin['maidan_login']==0}checked{/if} value="0" title="关闭" lay-filter="maidan_login" />
						</div>
						<div class="layui-form-mid layui-word-aux layui-clear">选择开启：扫码进入买单页面后跳转登录页，会员登录后才能买单付款</div>
					</div>
					
					<div class="layui-form-item">
						<label class="layui-form-label">支付后跳转：</label>
						<div class="layui-input-inline" style="width:300px">
							<input type="text" name="maidan_payaftertourl" class="layui-input" value="{$admin.maidan_payaftertourl}" id="maidan_payaftertourl">
						</div>
						<div class="layui-btn layui-btn-primary" style="float:left" onclick="chooseUrl2('maidan_payaftertourl')">选择链接</div>
						<div class="layui-form-mid layui-word-aux"></div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">开启定位：</label>
						<div class="layui-input-inline" style="width: 500px;">
							<input type="radio"  name="maidan_getlocation" {if $admin['maidan_getlocation']==1}checked{/if} value="1" title="开启" />
							<input type="radio"  name="maidan_getlocation" {if $admin['maidan_getlocation']==0}checked{/if} value="0" title="关闭" />
						</div>
						<div class="layui-form-mid"></div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">余额组合支付：</label>
						<div class="layui-input-inline" style="width: 500px;">
							<input type="radio"  name="maidan_balance_pay" {if $admin['maidan_balance_pay']==1}checked{/if} value="1" title="开启" />
							<input type="radio"  name="maidan_balance_pay" {if $admin['maidan_balance_pay']==0}checked{/if} value="0" title="关闭" />
						</div>
						<div class="layui-form-mid layui-word-aux layui-clear">开启后，用户可以在买单页面使用余额+微信支付的组合支付方式</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">自动绑定上级：</label>
						<div class="layui-input-inline" style="width: 500px;">
							<input type="radio"  name="maidan_auto_bind_parent" {if $admin['maidan_auto_bind_parent']==1}checked{/if} value="1" title="开启" />
							<input type="radio"  name="maidan_auto_bind_parent" {if $admin['maidan_auto_bind_parent']==0}checked{/if} value="0" title="关闭" />
						</div>
						<div class="layui-form-mid layui-word-aux layui-clear">开启后，第一次付款的用户如果没有上级，将自动绑定付款商家绑定的用户作为上级</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">云喇叭播报：</label>
						<div class="layui-input-inline" style="width: 500px;">
							<input type="radio"  name="cloud_speaker_enabled" {if $admin['cloud_speaker_enabled']==1}checked{/if} value="1" title="开启" />
							<input type="radio"  name="cloud_speaker_enabled" {if $admin['cloud_speaker_enabled']==0}checked{/if} value="0" title="关闭" />
						</div>
						<div class="layui-form-mid layui-word-aux layui-clear">开启后，买单支付成功时会自动播报收款信息</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">云喇叭设备号：</label>
						<div class="layui-input-inline" style="width:300px">
							<input type="text" name="cloud_speaker_device_id" class="layui-input" value="{$admin.cloud_speaker_device_id}" placeholder="请输入云喇叭设备编号">
						</div>
						<button type="button" class="layui-btn layui-btn-primary" id="testCloudSpeaker">测试连接</button>
						<button type="button" class="layui-btn layui-btn-warm" id="debugCloudSpeaker">调试测试</button>
						<div class="layui-form-mid layui-word-aux layui-clear">请输入云喇叭设备编号，如：3MS9910006952。调试测试会返回详细的请求和响应信息</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">
							<button class="layui-btn" lay-submit lay-filter="maidanedit">保存</button>
							<button class="layui-btn layui-btn-primary" type="button" id="uploadjstxt">上传域名校验文件</button>
						</div>
					</div>
				</div>
			</div>
		</div>
    </div>
  </div>
	{include file="public/js"/}
	<script>
		url = '{$Think.const.PRE_URL2}/h5/{$aid}.html#/pagesB/maidan/pay';
		var qrcode = new QRCode('urlqr', {
			text: 'your content',
			width: 200,
			height: 200,
			colorDark : '#000000',
			colorLight : '#ffffff',
			correctLevel : QRCode.CorrectLevel.L
		});
		qrcode.clear();
		qrcode.makeCode(url);

		layui.form.on('submit(maidanedit)', function(obj){
			var field = obj.field
			$.post("{:url('save')}",obj.field,function(data){
				dialog(data.msg,data.status);

			})
		})
		layui.upload.render({
			elem: '#uploadjstxt', //绑定元素
			url: "{:url('uploadjstxt')}", //上传接口
			exts:'txt|html',
			accept:'file',
			done: function(res){
				//上传完毕回调
				console.log(res)
				dialog(res)
			},
			error: function(){
				//请求异常回调
			}
		});
		layui.form.on('radio(maidan_login)', function(data){
			if(data.value=='1'){
				$('#auto_reg').hide();
			}else{
				$('#auto_reg').show();
			}
		})
		var chooseUrlField = '';
		function chooseUrl2(field){
			chooseUrlField = field;
			layer.open({type:2,shadeClose:true,area:['1200px', '650px'],'title':'选择链接',content:"{:url('DesignerPage/chooseurl')}&callback=chooseLink2"})
		}
		function chooseLink2(urlname,url){
			$("#"+chooseUrlField).val(url);
		}

		// 测试云喇叭连接
		$('#testCloudSpeaker').click(function(){
			var device_id = $('input[name="cloud_speaker_device_id"]').val();
			if(!device_id){
				layer.msg('请先输入设备编号', {icon: 2});
				return;
			}

			var index = layer.load();
			$.post("{:url('testCloudSpeaker')}", {device_id: device_id}, function(data){
				layer.close(index);
				if(data.status == 1){
					layer.msg(data.msg, {icon: 1});
				}else{
					layer.msg(data.msg, {icon: 2});
				}
			}).fail(function(){
				layer.close(index);
				layer.msg('测试失败，请检查网络连接', {icon: 2});
			});
		});

		// 调试测试云喇叭
		$('#debugCloudSpeaker').click(function(){
			var device_id = $('input[name="cloud_speaker_device_id"]').val();
			if(!device_id){
				layer.msg('请先输入设备编号', {icon: 2});
				return;
			}

			var index = layer.load();
			$.post("{:url('debugCloudSpeaker')}", {device_id: device_id, message: '调试测试消息'}, function(data){
				layer.close(index);

				// 显示详细的调试信息
				var content = '<div style="max-height:400px;overflow-y:auto;">';
				content += '<h3>测试结果：' + (data.status == 1 ? '<span style="color:green">成功</span>' : '<span style="color:red">失败</span>') + '</h3>';
				content += '<p><strong>消息：</strong>' + data.msg + '</p>';

				if(data.debug){
					content += '<h4>调试信息：</h4>';
					content += '<p><strong>请求时间：</strong>' + data.debug.request_time + '</p>';
					content += '<p><strong>设备编号：</strong>' + data.debug.final_device_id + '</p>';
					content += '<p><strong>请求URL：</strong>' + data.debug.request_url + '</p>';

					if(data.debug.request_json){
						content += '<p><strong>请求数据：</strong></p>';
						content += '<pre style="background:#f5f5f5;padding:10px;font-size:12px;">' + data.debug.request_json + '</pre>';
					}

					if(data.debug.raw_response){
						content += '<p><strong>原始响应：</strong></p>';
						content += '<pre style="background:#f5f5f5;padding:10px;font-size:12px;">' + data.debug.raw_response + '</pre>';
					}

					if(data.debug.parsed_response){
						content += '<p><strong>解析后响应：</strong></p>';
						content += '<pre style="background:#f5f5f5;padding:10px;font-size:12px;">' + JSON.stringify(data.debug.parsed_response, null, 2) + '</pre>';
					}

					if(data.debug.error){
						content += '<p><strong>错误信息：</strong><span style="color:red;">' + data.debug.error + '</span></p>';
					}
				}

				content += '</div>';

				layer.open({
					type: 1,
					title: '云喇叭调试信息',
					area: ['800px', '600px'],
					content: content,
					btn: ['关闭'],
					yes: function(index){
						layer.close(index);
					}
				});

			}).fail(function(){
				layer.close(index);
				layer.msg('调试测试失败，请检查网络连接', {icon: 2});
			});
		});
  </script>
	{include file="public/copyright"/}
</body>
</html>