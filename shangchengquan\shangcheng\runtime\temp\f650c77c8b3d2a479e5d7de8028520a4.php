<?php /*a:4:{s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\wxpay\set.html";i:1745814019;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>支付设置</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header"><i class="fa fa-cog"></i> 支付设置</div>
				<div class="layui-card-body" pad15>
					<div class="layui-form" lay-filter="">
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">微支付状态：</label>
							<div class="layui-input-inline">
								<input type="radio" name="info[wxpay]" title="开启" value="1" <?php if($info['wxpay']==1): ?>checked<?php endif; ?>/>
								<input type="radio" name="info[wxpay]" title="关闭" value="0" <?php if($info['wxpay']==0): ?>checked<?php endif; ?>/>
							</div>
							<!-- <div class="layui-form-mid">JSAPI支付授权目录：<b><?php echo app('request')->domain(); ?>/</b></div> -->
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">微支付模式：</label>
							<div class="layui-input-inline" style="width:500px">
								<input type="radio" name="info[wxpay_type]" title="普通模式" value="0" <?php if($info['wxpay_type']==0): ?>checked<?php endif; ?> lay-filter="wxpay_type"/>
								<input type="radio" name="info[wxpay_type]" title="服务商模式" value="1" <?php if($info['wxpay_type']==1): ?>checked<?php endif; ?> lay-filter="wxpay_type"/>
								<input type="radio" name="info[wxpay_type]" title="二级商户模式" value="2" <?php if($info['wxpay_type']==2): ?>checked<?php endif; ?> lay-filter="wxpay_type"/>
								<input type="radio" name="info[wxpay_type]" title="随行付" value="3" <?php if($info['wxpay_type']==3): ?>checked<?php endif; ?> lay-filter="wxpay_type"/>
								<input type="radio" name="info[wxpay_type]" title="付呗商户" value="6" <?php if($info['wxpay_type']==6): ?>checked<?php endif; ?> lay-filter="wxpay_type"/>
								<input type="radio" name="info[wxpay_type]" title="嘉联商户" value="5" <?php if($info['wxpay_type']==5): ?>checked<?php endif; ?> lay-filter="wxpay_type"/>
							</div>
						</div>
						<div class="layui-form-item" id="wxpay_type1set" <?php if($info['wxpay_type']!=1): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label" style="width:130px">支付商户号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[wxpay_sub_mchid]" value="<?php echo $info['wxpay_sub_mchid']; ?>" class="layui-input"/>
							</div>
						</div>
						<div class="layui-form-item" id="wxpay_type2set" <?php if($info['wxpay_type']!=2): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label" style="width:130px">支付商户号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[wxpay_sub_mchid2]" value="<?php echo $info['wxpay_sub_mchid2']; ?>" class="layui-input"/>
							</div>
						</div>
						<div id="wxpay_type3set" <?php if($info['wxpay_type']!=3): ?>style="display:none"<?php endif; ?>>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">商户编号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[sxpay_mno]" value="<?php echo $info['sxpay_mno']; ?>" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux"> <a href="javascript:void(0)" onclick="openmax('<?php echo url('SxpayIncome/index'); ?>&isopen=1')">点击申请入驻</a></div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">支付密钥：</label>
							<div class="layui-input-inline" style="width:280px">
								<input type="text" name="info[sxpay_mchkey]" value="<?php echo $info['sxpay_mchkey']; ?>" class="layui-input"/>
								<?php if($info['sxpay_mchkey']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
							</div>
						</div>
						</div>
						
						<div id="wxpay_type6set" <?php if($info['wxpay_type']!=6): ?>style="display:none"<?php endif; ?>>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">商户编号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[fbpay_mno]" value="<?php echo $info['fbpay_mno']; ?>" class="layui-input"/>
							</div>
							<!--<div class="layui-form-mid layui-word-aux"> <a href="javascript:void(0)" onclick="openmax('<?php echo url('FbpayIncome/index'); ?>&isopen=1')">点击申请入驻</a></div>-->
						</div>
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:130px">门店编号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[fbpay_store]" value="<?php echo $info['fbpay_store']; ?>" class="layui-input"/>
							</div>
						</div>    
							<div class="layui-form-item">
							  <label class="layui-form-label">付呗小程序<br>收银台：</label>
							  <div class="layui-input-inline" style="width:200px">
								  <input type="radio" name="info[fbpay_embedded]" title="关闭" value="0" <?php if($info['fbpay_embedded']==0): ?>checked<?php endif; ?> />
								  <input type="radio" name="info[fbpay_embedded]" title="开启" value="1" <?php if($info['fbpay_embedded']==1): ?>checked<?php endif; ?> />
							  </div>
								<div class="layui-form-mid layui-word-aux">调用付呗（半屏）小程序支付，添加后需付呗审核才可使用半屏，未审核通过前以全屏方式调用</div>
							</div>
						</div>
						<div id="wxpay_type5set" <?php if($info['wxpay_type']!=5): ?>style="display:none"<?php endif; ?>>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">商户编号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[jlpay_mno]" value="<?php echo $info['jlpay_mno']; ?>" class="layui-input"/>
							</div>
							<label class="layui-form-label" style="width:130px">终端号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[jlpay_zd]" value="<?php echo $info['jlpay_zd']; ?>" class="layui-input"/>
							</div>
							
							<!--<div class="layui-form-mid layui-word-aux"> <a href="javascript:void(0)" onclick="openmax('<?php echo url('FbpayIncome/index'); ?>&isopen=1')">点击申请入驻</a></div>-->
						</div>
						</div>
						<!--</div>-->
						<div id="wxpay_type0set" <?php if($info['wxpay_type']!=0): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label">支付商户号：</label>
								<div class="layui-input-inline">
									<input type="text" name="info[wxpay_mchid]" value="<?php echo $info['wxpay_mchid']; ?>" class="layui-input"/>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">支付密钥：</label>
								<div class="layui-input-inline" style="width:280px">
									<input type="text" name="info[wxpay_mchkey]" value="<?php echo $info['wxpay_mchkey']; ?>" class="layui-input"/>
									<?php if($info['wxpay_mchkey']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
								</div>
								<div class="layui-form-mid layui-word-aux">请在 <a href="https://pay.weixin.qq.com" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]中设置[APIv2密钥]</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">PEM证书：</label>
								<div class="layui-input-inline" style="width:450px">
									<input style="float:left;" readonly="readonly" class="layui-input" type="text" name="info[wxpay_apiclient_cert]" value="<?php echo $info['wxpay_apiclient_cert']; ?>" id="wxpay_apiclient_cert"/>
								</div>
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary uploadfile" upload-input="wxpay_apiclient_cert" upload-preview="certPreview">上传</button>
								<div id="certPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['wxpay_apiclient_cert']): ?>已上传<?php endif; ?></div>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">apiclient_cert.pem 请在 <a href="https://pay.weixin.qq.com" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]中设置[API证书]，设置完成后上传</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">证书密钥：</label>
								<div class="layui-input-inline" style="width:450px">
									<input style="float:left;" readonly="readonly" class="layui-input" name="info[wxpay_apiclient_key]" value="<?php echo $info['wxpay_apiclient_key']; ?>" id="wxpay_apiclient_key"/>
								</div>
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary uploadfile" upload-input="wxpay_apiclient_key" upload-preview="keyPreview">上传</button>
								<div id="keyPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['wxpay_apiclient_key']): ?>已上传<?php endif; ?></div>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">apiclient_key.pem 请在 <a href="https://pay.weixin.qq.com" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]中设置[API证书]，设置完成后上传</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">证书序列号：</label>
								<div class="layui-input-inline" style="width:360px">
									<input type="text" name="info[wxpay_serial_no]" value="<?php echo $info['wxpay_serial_no']; ?>" class="layui-input"/>
									<?php if($info['wxpay_serial_no']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
								</div>
								<div class="layui-form-mid">使用商家转账到零钱功能时填写此项，否则不需要填写</div>
								<div class="layui-form-mid layui-word-aux">请在 <a href="https://pay.weixin.qq.com" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]-[管理证书]中查看</div>
							</div>
							<fieldset class="layui-elem-field">
								<legend>商家转账配置(APIv3配置)</legend>
								<div class="layui-field-box">
									<div class="layui-form-item">
										<label class="layui-form-label">V3支付密钥：</label>
										<div class="layui-input-inline" style="width:280px">
											<input type="text" name="info[wxpay_mchkey_v3]" value="<?php echo $info['wxpay_mchkey_v3']; ?>" class="layui-input"/>
											<?php if($info['wxpay_mchkey_v3']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
										</div>
										<div class="layui-form-mid layui-word-aux">请在 <a href="https://pay.weixin.qq.com" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]中设置[APIv3密钥]</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">签名验签方式：</label>
										<div class="layui-input-inline" style="width: 300px;">
											<input type="radio" name="info[sign_type]" title="平台证书" value="0" <?php if($info['sign_type']==0): ?>checked<?php endif; ?> lay-filter="diandaSwitchMulti" />
											<input type="radio" name="info[sign_type]" title="微信支付公钥" value="1" <?php if($info['sign_type']==1): ?>checked<?php endif; ?> lay-filter="diandaSwitchMulti" />
										</div>
										<div class="layui-form-mid layui-word-aux layui-clear">
											根据自己 [账户中心]-[API安全]-[验证微信支付身份]的设置选择，不要随意切换：
											<a target="_blank" href="https://pay.weixin.qq.com/doc/v3/merchant/4012154180">微信官方说明</a></div>
									</div>
									<div class="sign_type_0" diandaSwitchMulti="info[sign_type]" data-val="0" <?php if($info && $info['sign_type']!=0): ?>style="display:none"<?php endif; ?>>
										<div class="layui-form-item">
											<label class="layui-form-label">平台证书序列号：</label>
											<div class="layui-input-inline">
												<input type="text" name="info[wxpay_plate_serialno]" value="<?php echo $info['wxpay_plate_serialno']; ?>" class="layui-input"/>
											</div>
											<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">
													获取方式：[账户中心]-[API安全]-[平台证书]-[管理证书]</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label">平台证书：</label>
											<div class="layui-input-inline" style="width:450px">
												<input style="float:left;" readonly="readonly" class="layui-input" type="text" name="info[wxpay_wechatpay_pem]" value="<?php echo $info['wxpay_wechatpay_pem']; ?>" id="wxpay_wechatpay_pem"/>
											</div>
											<button style="float:left;" type="button" class="layui-btn layui-btn-primary uploadfile" upload-input="wxpay_wechatpay_pem" upload-preview="wechatpayPreview">手动上传</button>
											<button style="float:left;margin-left: 10px;" type="button" class="layui-btn layui-btn-primary" onclick="download_wechatkey()">自动下载</button>
											<div id="wechatpayPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['wxpay_wechatpay_pem']): ?>已上传<?php endif; ?></div>
											<div class="layui-form-mid layui-word-aux layui-clear">
												wechatpay.pem 请在 <a href="https://pay.weixin.qq.com/doc/v3/merchant/4012068814" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]-[验证微信支付身份]中下载证书，设置完成后上传
												<br/>注：使用自动下载需开启php的shell_exec函数
											</div>
										</div>
									</div>
									<div class="sign_type_1" diandaSwitchMulti="info[sign_type]" data-val="1" <?php if(!$info || $info['sign_type']!=1): ?>style="display:none"<?php endif; ?>>
										<div class="layui-form-item">
											<label class="layui-form-label">公钥ID：</label>
											<div class="layui-input-inline">
												<input type="text" name="info[public_key_id]" value="<?php echo $info['public_key_id']; ?>" class="layui-input"/>
											</div>
											<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">
												<a target="_blank" href="https://pay.weixin.qq.com/doc/v3/merchant/4013038816#Q:-%E5%A6%82%E4%BD%95%E8%8E%B7%E5%8F%96%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E5%85%AC%E9%92%A5ID?">获取方式</a>
											</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label">公钥文件：</label>
											<div class="layui-input-inline" style="width:450px">
												<input style="float:left;" readonly="readonly" class="layui-input" type="text" name="info[public_key_pem]" value="<?php echo $info['public_key_pem']; ?>" id="public_key_pem"/>
											</div>
											<button style="float:left;" type="button" class="layui-btn layui-btn-primary uploadfile" upload-input="public_key_pem" upload-preview="publickeyPreview">上传</button>
											<div id="publickeyPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['public_key_pem']): ?>已上传<?php endif; ?></div>
											<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">pub_key.pem 请在 <a href="https://pay.weixin.qq.com" target="_blank">微信支付商户平台</a> [账户中心]-[API安全]中设置[微信支付公钥]，设置完成后上传</div>
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">商家转账场景ID：</label>
										<div class="layui-input-inline">
											<input type="text" name="info[transfer_scene_id]" value="<?php echo (isset($info['transfer_scene_id']) && ($info['transfer_scene_id'] !== '')?$info['transfer_scene_id']:'1005'); ?>" readonly class="layui-input"/>
										</div>
										<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">该笔转账使用的转账场景，可前往“商户平台-产品中心-<a target="_blank" href="https://pay.weixin.qq.com/xdc/mchtranstemplate/index.php/xphp/cgi/page/mch_trans/setting">商家转账</a>”中申请。目前仅支持【1005-佣金报酬】 </div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">岗位类型：</label>
										<div class="layui-input-inline">
											<input type="text" name="info[transfer_scene_type]" value="<?php echo $info['transfer_scene_type']; ?>" class="layui-input"/>
										</div>
										<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">
											请在信息内容描述收款用户的岗位类型，如外卖员、专家顾问，不填写默认【员工】
											<a target="_blank" href="https://pay.weixin.qq.com/doc/v3/merchant/4012711988">详情介绍</a>
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">报酬说明：</label>
										<div class="layui-input-inline">
											<input type="text" name="info[transfer_scene_content]" value="<?php echo $info['transfer_scene_content']; ?>" class="layui-input"/>
										</div>
										<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">
											请在信息内容描述用户接收当前这笔报酬的原因，如7月份配送费，高温补贴，不填写默认审核备注
											<a target="_blank" href="https://pay.weixin.qq.com/doc/v3/merchant/4012711988">详情介绍</a>
										</div>
									</div>
								</div>
							</fieldset>
						</div>
						<div class="layui-form-item" diandaSwitchMulti="info[wxpay_type]" data-val="1" <?php if($info['wxpay_type']!=1): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label">支付商户号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[wxpay_sub_mchid]" value="<?php echo $info['wxpay_sub_mchid']; ?>" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">必须填子商户的商户号</div>
						</div>
						<div class="layui-form-item" diandaSwitchMulti="info[wxpay_type]" data-val="2" <?php if($info['wxpay_type']!=2): ?>style="display:none"<?php endif; ?>>
							<label class="layui-form-label">支付商户号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[wxpay_sub_mchid2]" value="<?php echo $info['wxpay_sub_mchid2']; ?>" class="layui-input"/>
							</div>
						</div>
						<div diandaSwitchMulti="info[wxpay_type]" data-val="3" <?php if($info['wxpay_type']!=3): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label" style="width:130px">新商家转账：</label>
								<div class="layui-input-inline">
									<input type="radio" name="info[new_mch_transfer]" title="开启" value="1" <?php if($info['new_mch_transfer']==1): ?>checked<?php endif; ?>/>
									<input type="radio" name="info[new_mch_transfer]" title="关闭" value="0" <?php if($info['new_mch_transfer']==0 || !isset($info['new_mch_transfer'])): ?>checked<?php endif; ?>/>
								</div>
								<div class="layui-form-mid layui-word-aux">开启后将使用新版微信商家转账接口，需要在商户平台完成转账资质审核</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">支付密钥：</label>
								<div class="layui-input-inline" style="width:280px">
									<input type="text" name="info[sxpay_mchkey]" value="<?php echo $info['sxpay_mchkey']; ?>" class="layui-input"/>
									<?php if($info['sxpay_mchkey']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
								</div>
							</div>
							<div class="layui-form-item">
							  <label class="layui-form-label">随行付小程序<br>收银台：</label>
							  <div class="layui-input-inline" style="width:200px">
								  <input type="radio" name="info[sxpay_embedded]" title="关闭" value="0" <?php if($info['sxpay_embedded']==0): ?>checked<?php endif; ?> />
								  <input type="radio" name="info[sxpay_embedded]" title="开启" value="1" <?php if($info['sxpay_embedded']==1): ?>checked<?php endif; ?> />
							  </div>
								<div class="layui-form-mid layui-word-aux">调用随行付（半屏）小程序支付 <a href="javascript:void(0)" onclick="openmax('<?php echo url('Wxembedded/index'); ?>&isopen=1')">点击添加半屏小程序</a>，添加后需随行付审核才可使用半屏，未审核通过前以全屏方式调用</div>
							</div>
							  <?php if(getcustom('sxpay_mendian_info')): ?>
							  <div class="layui-form-item">
								  <label class="layui-form-label">天阙终端编号：</label>
								  <div class="layui-input-inline">
									  <input type="text" name="info[sxpay_deviceNo]" value="<?php echo $info['sxpay_deviceNo']; ?>" class="layui-input"/>
								  </div>
							  </div>
							  <?php endif; ?>
						</div>

						  <?php if(getcustom('pay_huifu')): ?>
							  <div diandaSwitchMulti="info[wxpay_type]" data-val="4" <?php if($info['wxpay_type'] !=4): ?>style="display:none"<?php endif; ?>>
								  <div class="layui-form-item">
									  <label class="layui-form-label">系统号sys_id：</label>
									  <div class="layui-input-inline">
										  <input type="text" name="info[huifu_sys_id]" value="<?php echo $info['huifu_sys_id']; ?>" class="layui-input"/>
									  </div>
									  <div class="layui-form-mid layui-word-aux">渠道商/商户的huifu_id，请在<a href="https://dashboard.huifu.com/customers/login" target="_blank">商户后台</a>[开发设置]-[开发者信息]查找</div>
								  </div>
								  <div class="layui-form-item">
									  <label class="layui-form-label">产品号product_id：</label>
									  <div class="layui-input-inline">
										  <input type="text" name="info[huifu_product_id]" value="<?php echo $info['huifu_product_id']; ?>" class="layui-input"/>
									  </div>
									  <div class="layui-form-mid layui-word-aux">请在<a href="https://dashboard.huifu.com/customers/login" target="_blank">商户后台</a>[开发设置]-[开发者信息]查找</div>
								  </div>

								  <div class="layui-form-item">
									  <label class="layui-form-label">商户公钥：</label>
									  <div class="layui-input-inline" style="width:500px">
										  <input type="text" name="info[huifu_merch_public_key]" value="<?php echo $info['huifu_merch_public_key']; ?>" class="layui-input"/>
										  <?php if($info['huifu_merch_public_key']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
									  </div>
									  <div class="layui-form-mid layui-word-aux">请在<a href="https://dashboard.huifu.com/customers/login" target="_blank">商户后台</a>[开发设置]-[密钥管理]查找</div>
								  </div>
								  <div class="layui-form-item">
									  <label class="layui-form-label">商户私钥：</label>
									  <div class="layui-input-inline" style="width:500px">
										  <input type="text" name="info[huifu_merch_private_key]" value="<?php echo $info['huifu_merch_private_key']; ?>" class="layui-input"/>
										  <?php if($info['huifu_merch_private_key']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
									  </div>
									  <div class="layui-form-mid layui-word-aux">请在<a href="https://dashboard.huifu.com/customers/login" target="_blank">商户后台</a>[开发设置]-[密钥管理]查找</div>
								  </div>
								  <div class="layui-form-item">
									  <label class="layui-form-label">汇付公钥：</label>
									  <div class="layui-input-inline" style="width:500px">
										  <input type="text" name="info[huifu_public_key]" value="<?php echo $info['huifu_public_key']; ?>" class="layui-input"/>
										  <?php if($info['huifu_public_key']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
									  </div>
									  <div class="layui-form-mid layui-word-aux">请在<a href="https://dashboard.huifu.com/customers/login" target="_blank">商户后台</a>[开发设置]-[密钥管理]查找</div>
								  </div>
								</div>
						  	<?php endif; if(getcustom('pay_allinpay')): ?>
								<div diandaSwitchMulti="info[wxpay_type]" data-val="6" <?php if($info['wxpay_type'] !=6): ?>style="display:none"<?php endif; ?>>
									<div class="layui-form-item">
										<label class="layui-form-label">通联支付：</label>
										<div class="layui-input-inline">
											<div class="layui-form-mid layui-word-aux"> <a href="javascript:void(0)" onclick="openmax('<?php echo url('AllinpayYunstSet/index'); ?>&isopen=1')">点击查看设置</a></div>
										</div>
									</div>
								</div>
							  <?php endif; if(getcustom('wxpay_b2b')): ?>
							  <div diandaSwitchMulti="info[wxpay_type]" data-val="8" <?php if($info['wxpay_type']!=8): ?>style="display:none"<?php endif; ?>>
								  <div class="layui-form-item">
									  <label class="layui-form-label">B2b商户号：</label>
									  <div class="layui-input-inline">
										  <input type="text" name="info[wxpay_b2b_mchid]" value="<?php echo $info['wxpay_b2b_mchid']; ?>" class="layui-input"/>
									  </div>
								  </div>
								  <div class="layui-form-item">
									  <label class="layui-form-label">现网AppKey：</label>
									  <div class="layui-input-inline">
										  <input type="text" name="info[wxpay_b2b_appkey]" value="<?php echo $info['wxpay_b2b_appkey']; ?>" class="layui-input"/>
									  </div>
									  <div class="layui-form-mid layui-word-aux">请在 <a href="https://mp.weixin.qq.com" target="_blank">微信小程序后台</a> 行业能力-B2b门店助手-支付管理-查看详情-基本配置中查找</div>
								  </div>
							  </div>
  								<?php endif; if(getcustom('pay_chinaums')): ?>
						  <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
							  <legend>云闪付</legend>
						  </fieldset>
						  <div class="layui-form-item">
							  <label class="layui-form-label">云闪付支付：</label>
							  <div class="layui-input-inline">
								  <input type="radio" name="info[ysf_pay]" title="开启" value="1" <?php if($info['ysf_pay']==1): ?>checked<?php endif; ?>/>
								  <input type="radio" name="info[ysf_pay]" title="关闭" value="0" <?php if($info['ysf_pay']==0): ?>checked<?php endif; ?>/>
							  </div>
						  </div>
						  <div class="layui-form-item">
							  <label class="layui-form-label">Appid：</label>
							  <div class="layui-input-inline" style="width:300px">
								  <input type="text" name="info[ysf_appid]" value="<?php echo $info['ysf_appid']; ?>" class="layui-input"/>
							  </div>
							  <div class="layui-form-mid layui-word-aux">请在[<a href="https://ump.chinaums.com/v3/?redirectUri=https://saas.chinaums.com/saas-gateway/sys/casLoginIndex&clientId=0000000180&clientType=110&choiceShow=0#/logout" target="_blank">控制台右侧气泡</a>]-[我是开发者]-[开发者密钥]获取</div>
						  </div>
						  <div class="layui-form-item">
							  <label class="layui-form-label">AppKey：</label>
							  <div class="layui-input-inline" style="width:300px">
								  <input type="text" name="info[ysf_appkey]" value="<?php echo $info['ysf_appkey']; ?>" class="layui-input"/>
							  </div>
							  <div class="layui-form-mid layui-word-aux">请在[<a href="https://ump.chinaums.com/v3/?redirectUri=https://saas.chinaums.com/saas-gateway/sys/casLoginIndex&clientId=0000000180&clientType=110&choiceShow=0#/logout" target="_blank">控制台右侧气泡</a>]-[我是开发者]-[开发者密钥]获取</div>
						  </div>
						  <div class="layui-form-item">
							  <label class="layui-form-label">商户号：</label>
							  <div class="layui-input-inline" style="width:300px">
								  <input type="text" name="info[ysf_sub_mchid]" value="<?php echo $info['ysf_sub_mchid']; ?>" class="layui-input"/>
							  </div>
							  <div class="layui-form-mid layui-word-aux">请在[<a href="https://ump.chinaums.com/v3/?redirectUri=https://saas.chinaums.com/saas-gateway/sys/casLoginIndex&clientId=0000000180&clientType=110&choiceShow=0#/logout" target="_blank">控制台右侧气泡</a>]-[我是开发者]-[开发者密钥]获取</div>
						  </div>
						  <div class="layui-form-item">
							  <label class="layui-form-label">终端号：</label>
							  <div class="layui-input-inline" style="width:300px">
								  <input type="text" name="info[ysf_terminalid]" value="<?php echo $info['ysf_terminalid']; ?>" class="layui-input"/>
							  </div>
							  <div class="layui-form-mid layui-word-aux">请在[<a href="https://ump.chinaums.com/v3/?redirectUri=https://saas.chinaums.com/saas-gateway/sys/casLoginIndex&clientId=0000000180&clientType=110&choiceShow=0#/logout" target="_blank">控制台右侧气泡</a>]-[我是开发者]-[开发者密钥]获取</div>
						  </div>
						  <div class="layui-form-item">
							  <label class="layui-form-label">接入来源：</label>
							  <div class="layui-input-inline" style="width:300px">
								  <input type="text" name="info[ysf_source]" value="<?php echo $info['ysf_source']; ?>" class="layui-input"/>
							  </div>
							  <div class="layui-form-mid layui-word-aux">请在[<a href="https://ump.chinaums.com/v3/?redirectUri=https://saas.chinaums.com/saas-gateway/sys/casLoginIndex&clientId=0000000180&clientType=110&choiceShow=0#/logout" target="_blank">控制台右侧气泡</a>]-[我是开发者]-[开发者密钥]获取</div>
						  </div>
						  <div class="layui-form-item">
							  <label class="layui-form-label">通讯密钥：</label>
							  <div class="layui-input-inline" style="width:300px">
								  <input type="text" id="secret" name="info[ysf_secretkey]" lay-verify="required" lay-verType="tips" value="<?php echo $info['ysf_secretkey']; ?>" class="layui-input"/>
								  <?php if($info['ysf_secretkey']): ?>
								  <div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑 </div>
								  <?php endif; ?>
							  </div>
							  <div class="layui-form-mid layui-word-aux">请在[<a href="https://ump.chinaums.com/v3/?redirectUri=https://saas.chinaums.com/saas-gateway/sys/casLoginIndex&clientId=0000000180&clientType=110&choiceShow=0#/logout" target="_blank">控制台右侧气泡</a>]-[我是开发者]-[开发者密钥]获取</div>
						  </div>
						  <?php endif; if(getcustom('wxpay_global')): ?>
						  <div diandaSwitchMulti="info[wxpay_type]" data-val="9" <?php if($info['wxpay_type']!=9): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label">支付商户号：</label>
								<div class="layui-input-inline">
									<input type="text" name="info[transfer_scene_id]" value="<?php echo $info['transfer_scene_id']; ?>" class="layui-input"/>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">支付密钥：</label>
								<div class="layui-input-inline" style="width:280px">
									<input type="text" name="info[wxpay_mchkey_global]" value="<?php echo $info['wxpay_mchkey_global']; ?>" class="layui-input"/>
									<?php if($info['wxpay_mchkey_global']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
								</div>
								<div class="layui-form-mid layui-word-aux">请在 <a href="https://pay.weixin.qq.com/index.php/public/wechatpay_en" target="_blank">微信支付商户平台</a> [Account Setting]-[API Security]中设置[Set APIv3 Secret]</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">证书密钥：</label>
								<div class="layui-input-inline" style="width:450px">
									<input style="float:left;" readonly="readonly" class="layui-input" name="info[wxpay_apiclient_key_global]" value="<?php echo $info['wxpay_apiclient_key_global']; ?>" id="wxpay_apiclient_key_global"/>
								</div>
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary uploadfile" upload-input="wxpay_apiclient_key_global" upload-preview="keyPreview">上传</button>
								<div id="keyPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['wxpay_apiclient_key_global']): ?>已上传<?php endif; ?></div>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">apiclient_key.pem 请在 <a href="https://pay.weixin.qq.com/index.php/public/wechatpay_en" target="_blank">微信支付商户平台</a> [Account Setting]-[API Security]中设置[API certificate]，设置完成后上传</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">证书序列号：</label>
								<div class="layui-input-inline" style="width:360px">
									<input type="text" name="info[wxpay_serial_no_global]" value="<?php echo $info['wxpay_serial_no_global']; ?>" class="layui-input"/>
									<?php if($info['wxpay_serial_no_global']): ?><div style="position: absolute;left: 0;top: 0;right: 0;bottom: 0; background: #fff;padding: .35rem .7rem;border: 1px solid rgba(0, 0, 0, .15);border-radius: .15rem;color: #636c72;" onclick="$(this).hide()">已隐藏内容，点击查看或编辑</div><?php endif; ?>
								</div>
								<div class="layui-form-mid layui-word-aux">请在 <a href="https://pay.weixin.qq.com/index.php/public/wechatpay_en" target="_blank">登录商户平台</a> 【account settings】->【API security】->【API certificate】，可查看商户API证书序列号</div>
							</div>
			<!-- 				<div class="layui-form-item">
								<label class="layui-form-label">商户证书：</label>
								<div class="layui-input-inline" style="width:450px">
									<input style="float:left;" readonly="readonly" class="layui-input" type="text" name="info[wxpay_apiclient_cert_global]" value="<?php echo $info['wxpay_apiclient_cert_global']; ?>" id="wxpay_apiclient_cert_global"/>
								</div>
								<button style="float:left;" type="button" class="layui-btn layui-btn-primary uploadfile" upload-input="wxpay_apiclient_cert_global" upload-preview="certPreview">上传</button>
								<div id="certPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['wxpay_apiclient_cert_global']): ?>已上传<?php endif; ?></div>
								<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">apiclient_cert.pem 请在 <a href="https://pay.weixin.qq.com/index.php/public/wechatpay_en" target="_blank">微信支付商户平台</a> [Account Setting]-[API Security]中设置[API certificate]，设置完成后上传</div>
							</div> -->
							
							<div class="layui-form-item">
								<label class="layui-form-label">平台证书：</label>
								<div class="layui-input-inline" style="width:450px">
									<input style="float:left;" readonly="readonly" class="layui-input" type="text" name="info[wxpay_wechatpay_pem_global]" value="<?php echo $info['wxpay_wechatpay_pem_global']; ?>" id="wxpay_wechatpay_pem_global"/>
								</div>
								<button style="float:left;margin-left: 10px;" type="button" class="layui-btn layui-btn-primary" onclick="download_pem()">下载</button>
								<div id="wechatpayPreview" class="layui-form-mid" style="margin-left:20px;"><?php if($info['wxpay_wechatpay_pem_global']): ?>已上传<?php endif; ?></div>
								<div class="layui-form-mid layui-word-aux layui-clear">
									<br/>注：需开启php的shell_exec函数
									<!-- ，获取方式：平台证书只提供API下载,请求URL:https://apihk.mch.weixin.qq.com/v3/global/certificates -->
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">货币类型：</label>
								<div class="layui-input-inline">
									<select name="info[currency_global]">
										<?php foreach($currencys as $ck => $cv): ?>
											<option value="<?php echo $ck; ?>" <?php if($ck==$info['currency_global']): ?>selected<?php endif; ?>><?php echo $cv; ?></option>
										<?php endforeach; ?>
									</select>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">商户类别码（MCC）：</label>
								<div class="layui-input-inline">
									<input type="text" name="info[wxpay_mcc_global]" value="<?php echo $info['wxpay_mcc_global']; ?>" class="layui-input"/>
								</div>
							</div>

							<p>Nginx环境在宝塔控制面板中点击[网站]-[设置]-[配置文件],在server内增加以下信息
							<div>
<pre style="color:#000">
location /notify/ {  
	rewrite ^/notify/([0-9]+)$ /notify.php?aid=$1 last;  # 重写 URL  
}  
</pre>
								<div class="layui-popover layui-default-link layui-inline" 
									onclick="copyText(`location /notify/ { rewrite ^/notify/([0-9]+)$ /notify.php?aid=$1 last; } `)">复制</div>
								<div class="layui-popover layui-default-link layui-inline">
								示例
								<div class="layui-popover-div">
									<img src="/static/admin/img/wxpay_global.png" style="width: 600px" />
								</div>
							</p>
							</div>
						<?php endif; ?>
						{if getcustom('pay_qilinshuzi')}
							<div diandaSwitchMulti="info[wxpay_type]" data-val="10" <?php if($info['wxpay_type'] !=10): ?>style="display:none"<?php endif; ?>>
								<div class="layui-form-item">
									  <label class="layui-form-label">麒麟数字：</label>
									  <div class="layui-input-inline">
										  <div class="layui-form-mid layui-word-aux"> <a href="javascript:void(0)" onclick="openmax('<?php echo url('QilinshuziSet/index'); ?>&isopen=1')">点击查看设置</a></div>
									  </div>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px"></label>
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="formsubmit">提 交</button>
							</div>
						</div>
					</div>
				</div>
      </div>
    </div>
  </div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script>
	layui.form.on('radio(wxpay_type)', function(data){
		$('#wxpay_type0set').hide();
		$('#wxpay_type1set').hide();
		$('#wxpay_type2set').hide();
		$('#wxpay_type3set').hide();
		$('#wxpay_type4set').hide();
		$('#wxpay_type5set').hide();
		$('#wxpay_type6set').hide();
		if(data.value == '0'){
			$('#wxpay_type0set').show();
		}else if(data.value == '1'){
			$('#wxpay_type1set').show();
		}else if(data.value == '2'){
			$('#wxpay_type2set').show();
		}else if(data.value == '3'){
			$('#wxpay_type3set').show();
		}else if(data.value == '4'){
			$('#wxpay_type4set').show();
		}else if(data.value == '5'){
			$('#wxpay_type5set').show();
		}else if(data.value == '6'){
			$('#wxpay_type6set').show();
		}
	})
	layui.form.on('submit(formsubmit)', function(obj){
		var field = obj.field
		$.post('',field,function(data){
			dialog(data.msg,data.status,data.url);
		})
	})
	//下载微信支付平台证书
	function download_wechatkey(){
		var wxpay_mchid = $('input[name="info[wxpay_mchid]"]').val();
		if(!wxpay_mchid){
			return layer.msg('请填写【支付商户号】');
		}
		var wxpay_apiclient_key = $('input[name="info[wxpay_apiclient_key]"]').val();
		if(!wxpay_apiclient_key){
			return layer.msg('请上传【证书密钥】');
		}
		var wxpay_serial_no = $('input[name="info[wxpay_serial_no]"]').val();
		if(!wxpay_serial_no){
			return layer.msg('请填写【证书序列号】');
		}
		var wxpay_mchkey_v3 = $('input[name="info[wxpay_mchkey_v3]"]').val();
		if(!wxpay_mchkey_v3){
			return layer.msg('请填写【V3支付密钥】');
		}
		var wxpay_plate_serialno = $('input[name="info[wxpay_plate_serialno]"]').val();
		$.post('<?php echo url("Mppay/download_wechatkey"); ?>',{
			wxpay_mchkey_v3:wxpay_mchkey_v3,
			wxpay_mchid:wxpay_mchid,
			wxpay_apiclient_key:wxpay_apiclient_key,
			wxpay_serial_no:wxpay_serial_no,
			wxpay_plate_serialno:wxpay_plate_serialno
		},function(data){
			console.log(data);
			if(data.status==1){
				$('input[name="info[wxpay_wechatpay_pem]"]').val(data.data);
			}
			dialog(data.msg,data.status);
		})
	}
	//下载海外微信支付平台证书
	function download_wechatkey_global(){
		var wxpay_mchid = $('input[name="info[wxpay_mchid_global]"]').val();
		if(!wxpay_mchid){
			return layer.msg('请填写【支付商户号】');
		}
		var wxpay_apiclient_key = $('input[name="info[wxpay_apiclient_key_global]]"]').val();
		if(!wxpay_apiclient_key){
			return layer.msg('请上传【证书密钥】');
		}
		var wxpay_serial_no = $('input[name="info[wxpay_serial_no_global]]"]').val();
		if(!wxpay_serial_no){
			return layer.msg('请填写【证书序列号】');
		}
		var wxpay_mchkey_v3 = $('input[name="info[wxpay_mchkey_global]]"]').val();
		if(!wxpay_mchkey_v3){
			return layer.msg('请填写【V3支付密钥】');
		}
		$.post('<?php echo url("Mppay/download_wechatkey"); ?>',{
			wxpay_mchkey_v3:wxpay_mchkey_v3,
			wxpay_mchid:wxpay_mchid,
			wxpay_apiclient_key:wxpay_apiclient_key,
			wxpay_serial_no:wxpay_serial_no
		},function(data){
			console.log(data);
			if(data.status==1){
				$('input[name="info[wxpay_wechatpay_pem_global]"]').val(data.data);
			}
			dialog(data.msg,data.status);
		})
	}
	// //下载海外微信支付平台证书
	// function download_pem(){
		
	// 	$.post('<?php echo url("download_pem"); ?>',{},function(data){
	// 		console.log(data);
	// 		if(data.status==1){
	// 			$('input[name="info[wxpay_wechatpay_pem_global]"]').val(data.data);
	// 		}
	// 		dialog(data.msg,data.status);
	// 	})
	// }
  </script>
	
</body>
</html>