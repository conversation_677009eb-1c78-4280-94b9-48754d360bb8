<?php /*a:4:{s:76:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\business\edit.html";i:1753660781;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\user_auth.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>添加商户</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
	<style>
		.layui-form-label{width:120px!important}
	</style>
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-card layui-col-md12">
				<div class="layui-card-header">
					<?php if(!$info['id']): ?><i class="fa fa-plus"></i> 添加商户<?php else: ?><i class="fa fa-pencil"></i> 编辑商户<?php endif; ?>
					<i class="layui-icon layui-icon-close" style="font-size:18px;font-weight:bold;cursor:pointer" onclick="closeself()"></i>
				</div>
				<div class="layui-card-body" pad15>
					<?php if($info['status']==2): ?><blockquote class="layui-elem-quote" style="color:red">商户未审核通过，驳回原因：<?php echo $info['reason']; ?></blockquote><?php endif; ?>
					<div class="layui-form">
						<input type="hidden" name="info[id]" value="<?php echo $info['id']; ?>"/>
						<input type="hidden" name="uinfo[id]" value="<?php echo $uinfo['id']; ?>">
						
						<div class="layui-form-item">
							<label class="layui-form-label">商户分类：</label>
							<div class="layui-input-inline" style="width:300px">
								<select name="info[cid]" xm-select="selectCid" xm-select-max="5" xm-select-search>
								<option value="">--请选择--</option>
								<?php foreach($clist as $cv): ?>
									<option value="<?php echo $cv['id']; ?>" <?php if(in_array($cv['id'],$info['cid'])): ?>selected<?php endif; ?>><?php echo $cv['name']; ?></option>
								<?php endforeach; ?>
								</select>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">商户名称：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[name]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['name']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">管理员id（用于绑定上下级，收款）：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[mid]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['mid']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">副管理员id：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[mid2]" lay-verify="required" lay-verType="tips" class="layui-input" value="<?php echo $info['mid2']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">登录账号：</label>
							<div class="layui-input-inline">
								<input type="text" name="uinfo[un]" value="<?php echo $uinfo['un']; ?>" lay-verify="required" lay-verType="tips" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">登录密码：</label>
							<div class="layui-input-inline">
								<input type="text" class="layui-input" autocomplete="off" name="uinfo[pwd]" value="" <?php if($uinfo['id']): ?> placeholder="留空表示不修改密码"<?php else: ?> lay-verify="required" lay-verType="tips"<?php endif; ?>>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">抽成费率：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[feepercent]" class="layui-input" value="<?php echo $info['feepercent']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-left:10px;">%，平台收取的佣金比例，如设置10%，平台抽成10%，商家得90%</div>
						</div>
							<div class="layui-form-item">
							<label class="layui-form-label">保证金余额：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[depositPaid]" class="layui-input" value="<?php echo $info['depositPaid']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-left:10px;">填写后将展示在商家的首页界面</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">嘉联商户号：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[jlpay_mno]" class="layui-input" value="<?php echo $info['jlpay_mno']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-left:10px;">填写嘉联商户号</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">嘉联终端号(公众号)：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[jlpay_zd]" class="layui-input" value="<?php echo $info['jlpay_zd']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-left:10px;">填写嘉联终端号</div>
						</div>
						
						<!--<div class="layui-form-item">-->
						<!--	<label class="layui-form-label">嘉联终端号(小程序)：</label>-->
						<!--	<div class="layui-input-inline" style="width:200px">-->
						<!--		<input type="text" name="info[jlpay_zd2]" class="layui-input" value="<?php echo $info['jlpay_zd2']; ?>">-->
						<!--	</div>-->
						<!--	<div class="layui-form-mid" style="margin-left:10px;">填写嘉联终端号</div>-->
						<!--</div>-->
						<?php if($set['wxfw_status'] == 1): ?>
						<div class="layui-form-item">
							<label class="layui-form-label">子商户号收款：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="radio" name="info[wxpayst]" value="0" title="关闭" <?php if(!$info['id'] || $info['wxpayst']==0): ?>checked<?php endif; ?> lay-filter="wxpayst"/>
								<input type="radio" name="info[wxpayst]" value="1" title="开启" <?php if($info['wxpayst']==1): ?>checked<?php endif; ?> lay-filter="wxpayst"/>
							</div>
							<div class="layui-form-mid layui-word-aux">开启后可设置子商户号独立收款结算</div>
						</div>
						<div class="layui-form-item" <?php if(!$info['id'] || $info['wxpayst']==0): ?>style="display:none"<?php endif; ?> id="wxpayst_div">
							<label class="layui-form-label">子商户号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[wxpay_submchid]" class="layui-input" value="<?php echo $info['wxpay_submchid']; ?>">
							</div>
							<div class="layui-form-mid" style="margin-left:10px;">在微信支付服务商后台申请的商户号</div>
						</div>
						<?php endif; ?>
						<div class="layui-form-item">
							<label class="layui-form-label">联系人：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[linkman]" class="layui-input" value="<?php echo $info['linkman']; ?>">
							</div>
						</div>
					
						<div class="layui-form-item">
							<label class="layui-form-label">联系电话：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[linktel]" class="layui-input" value="<?php echo $info['linktel']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">客服电话：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[tel]" class="layui-input" value="<?php echo $info['tel']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">客服链接：</label>
							<div class="layui-input-inline" style="width:300px">
								<input type="text" name="info[kfurl]" value="<?php echo $info['kfurl']; ?>" class="layui-input"/>
							</div>
							<div class="layui-form-mid layui-word-aux">商品详情页客服链接，不填写则使用商城内部客服系统</div>
						</div>
						
						<!-- 买单限额管理设置 -->
						<div class="layui-form-item">
							<label class="layui-form-label" style="color:#FF5722;font-weight:bold;">买单限额管理：</label>
							<div class="layui-form-mid layui-word-aux">设置商家买单功能的限额，0或不填表示不限额</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">每笔限额：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[maidan_single_limit]" value="<?php echo (isset($info['maidan_single_limit']) && ($info['maidan_single_limit'] !== '')?$info['maidan_single_limit']:0); ?>" class="layui-input" placeholder="0">
							</div>
							<div class="layui-form-mid layui-word-aux">单次买单最大金额，0表示不限额</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">日限额：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[maidan_day_limit]" value="<?php echo (isset($info['maidan_day_limit']) && ($info['maidan_day_limit'] !== '')?$info['maidan_day_limit']:0); ?>" class="layui-input" placeholder="0">
							</div>
							<div class="layui-form-mid layui-word-aux">该商家每日买单总金额限制，0表示不限额</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">月限额：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[maidan_month_limit]" value="<?php echo (isset($info['maidan_month_limit']) && ($info['maidan_month_limit'] !== '')?$info['maidan_month_limit']:0); ?>" class="layui-input" placeholder="0">
							</div>
							<div class="layui-form-mid layui-word-aux">该商家每月买单总金额限制，0表示不限额</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">商家主图：</label>
							<input type="hidden" name="info[logo]" id="logo" lay-verType="tips" class="layui-input" value="<?php echo $info['logo']; ?>">
							<button style="float:left;" type="button" class="layui-btn layui-btn-primary" upload-input="logo" upload-preview="logoPreview" onclick="uploader(this)">上传图片</button>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：640×640像素</div>
							<div id="logoPreview" style="float:left;padding-top:10px;padding-left:150px;clear: both;">
								<div class="layui-imgbox" style="width:100px;">
									<div class="layui-imgbox-img"><a class="layui-imgbox-close" href="javascript:void(0)" onclick="$(this).parent().find('img').attr('src','');getpicsval('logo','logoPreview')" title="删除"><i class="layui-icon layui-icon-close-fill"></i></a><img src="<?php echo $info['logo']; ?>"/></div>
								</div>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">商家照片：</label>
							<input type="hidden" name="info[pics]" value="<?php echo $info['pics']; ?>" id="pics">
							<button style="float:left;" type="button" class="layui-btn layui-btn-primary" onclick="uploader(this,true)" upload-input="pics" upload-preview="picList" >批量上传</button>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">建议尺寸：600*340像素</div>
							<div id="picList" style="float:left;padding-top:10px;padding-left:150px;clear: both;">
								<?php if($info['pics']): $pics = explode(',',$info['pics']); foreach($pics as $pic): ?>
								<div class="layui-imgbox">
									<a class="layui-imgbox-close" href="javascript:void(0)" onclick="$(this).parent().remove();getpicsval('pics','picList')" title="删除"><i class="layui-icon layui-icon-close-fill"></i></a>
									<span class="layui-imgbox-img"><img src="<?php echo $pic; ?>" onclick="previewImg('<?php echo $pic; ?>')"></span>
								</div>
								<?php endforeach; ?><?php endif; ?>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">商家描述：</label>
							<div class="layui-input-inline" style="width:700px">
								<input type="text" name="info[desc]" class="layui-input" value="<?php echo $info['desc']; ?>">
							</div>
						</div>
						
						<!-- <div class="layui-form-item">
							<label class="layui-form-label">服务标签：</label>
							<div class="layui-input-inline" style="width:400px">
								<input type="text" name="info[fuwupoint]" class="layui-input" value="<?php echo $info['fuwupoint']; ?>">
							</div>
							<div class="layui-form-mid layui-word-aux">多个用空格隔开,如: 7天退换 48小时发货 假一赔十</div>
						</div> -->
						<div class="layui-form-item">
							<label class="layui-form-label">商家简介：</label>
							<div class="layui-input-inline" style="width:500px;">
								<script id="content" name="info[content]" type="text/plain" style="width:100%;height:500px"><?php echo $info['content']; ?></script>
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">商家地址：</label>
							<div class="layui-input-inline" style="width:500px">
								<input type="text" id="address" name="info[address]" class="layui-input" value="<?php echo $info['address']; ?>">
							</div>
							<button class="layui-btn layui-btn-primary" id="searchbtn">搜索</button>
						</div>
						
						<!--使用地址ID标记地址-->
						<?php if($area_on): ?>
						<div class="layui-form-item area-on">
							<label class="layui-form-label">地址区域：</label>
							<div class="layui-input-inline" style="width:1000px">
							    <div class="layui-form-item">
										<div class="layui-input-inline" style="width:12em !important">
											<select id="province" name="info[province_id]" lay-filter="changeprovince" style="width:60px !important">
											    <?php foreach($provinces as $province) {?>
											        <option value="<?php echo $province['id'];?>" <?php if ($info['province_id']==$province['id']) { echo "selected"; }?>><?php echo $province['name'];?></option>
											    <?php }?>
											</select>
										</div>
										<div class="layui-input-inline" style="width:12em !important">
											<select id="city" name="info[city_id]" lay-filter="changecity" style="width:60px !important">
											    
											    
											</select>	
										</div>
										
										<div class="layui-input-inline" style="width:12em !important">
											<select id="district" name="info[district_id]" lay-filter="changedistrict" style="width:60px !important">
											    
											    
											</select>
										</div>
										
										<div class="layui-input-inline" style="width:12em !important">
											<select id="street" name="info[street_id]" style="width:60px !important">
											    
											    
											</select>
										</div>
									</div>
							</div>
						</div>
						<?php endif; ?>
						
					   <div class="layui-form-item">
      <label class="layui-form-label"></label>
      <div id="l-map" style="width:600px;height:350px">地图加载中...</div>
      <div style="margin-left:150px;margin-top:10px">
        <div class="layui-input-inline" style="width:200px">
          <input type="text" name="info[longitude]" id="mapjd" class="layui-input" value="<?php echo (isset($info['longitude']) && ($info['longitude'] !== '')?$info['longitude']:'116.39782905578613'); ?>"/>
        </div>
        <div class="layui-input-inline" style="width:200px">
          <input type="text" name="info[latitude]" id="mapwd" class="layui-input" value="<?php echo (isset($info['latitude']) && ($info['latitude'] !== '')?$info['latitude']:'39.90358020251377'); ?>"/>
        </div>
      </div>
    </div>

						<div class="layui-form-item">
							<label class="layui-form-label">证明材料：</label>
							<input type="hidden" name="info[zhengming]" value="<?php echo $info['zhengming']; ?>" id="zhengming">
							<button style="float:left;" type="button" class="layui-btn layui-btn-primary" onclick="uploader(this,true)" upload-input="zhengming" upload-preview="zhengmingList" >批量上传</button>
							<div id="zhengmingList" style="float:left;padding-top:10px;padding-left:150px;clear: both;">
								<?php if($info['zhengming']): $pics = explode(',',$info['zhengming']); foreach($pics as $pic): ?>
								<div class="layui-imgbox">
									<a class="layui-imgbox-close" href="javascript:void(0)" onclick="$(this).parent().remove();getpicsval('zhengming','zhengmingList')" title="删除"><i class="layui-icon layui-icon-close-fill"></i></a>
									<span class="layui-imgbox-img"><img src="<?php echo $pic; ?>" onclick="previewImg('<?php echo $pic; ?>')"></span>
								</div>
								<?php endforeach; ?><?php endif; ?>
							</div>
						</div>
						
					<div class="layui-form-item">
							<label class="layui-form-label">微信号：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[weixin]" class="layui-input" value="<?php echo $info['weixin']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">支付宝：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[aliaccount]" class="layui-input" value="<?php echo $info['aliaccount']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">开户行：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[bankname]" class="layui-input" value="<?php echo $info['bankname']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">持卡人：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[bankcarduser]" class="layui-input" value="<?php echo $info['bankcarduser']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">银行卡号：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="text" name="info[bankcardnum]" class="layui-input" value="<?php echo $info['bankcardnum']; ?>">
							</div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label">消费人数：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[sales]" value="<?php echo (isset($info['sales']) && ($info['sales'] !== '')?$info['sales']:0); ?>" class="layui-input">
							</div>
						</div> 
						<div class="layui-form-item">
							<label class="layui-form-label">虚拟销售额：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[virtual_sales]" value="<?php echo (isset($info['virtual_sales']) && ($info['virtual_sales'] !== '')?$info['virtual_sales']:0); ?>" class="layui-input">
							</div>
						</div> 
						<div class="layui-form-item">
							<label class="layui-form-label">序号：</label>
							<div class="layui-input-inline">
								<input type="text" name="info[sort]" value="<?php echo (isset($info['sort']) && ($info['sort'] !== '')?$info['sort']:0); ?>" class="layui-input">
							</div>
							<div class="layui-form-mid layui-word-aux" style="margin-left:10px;">用于排序,越大越靠前</div>
						</div>
						

						<div class="layui-form-item">
							<label class="layui-form-label" style="width:130px">消耗模式：</label>
							<div class="layui-input-inline" style="width:200px">
								<input type="radio" name="info[consume_mode]" value="1" title="预充值" <?php if(!$info['id'] || $info['consume_mode']==1): ?>checked<?php endif; ?>/>
								<input type="radio" name="info[consume_mode]" value="2" title="月结" <?php if($info['consume_mode']==2): ?>checked<?php endif; ?>/>
							</div>
							<div class="layui-form-mid layui-word-aux">选择商家使用系统的计费方式</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">状态：</label>
							<div class="layui-input-inline" style="width:310px">
								<input type="radio" name="info[status]" value="0" title="待审核" <?php if($info['id'] && $info['status']==0): ?>checked<?php endif; ?> lay-filter="changest">
								<input type="radio" name="info[status]" value="1" title="已通过" <?php if(!$info['id'] || $info['status']==1): ?>checked<?php endif; ?> lay-filter="changest">
								<input type="radio" name="info[status]" value="2" title="已驳回" <?php if($info['id'] && $info['status']==2): ?>checked<?php endif; ?> lay-filter="changest">
							</div>
						</div>
						<div id="changestset" <?php if($info['status']!=2): ?>style="display:none"<?php endif; ?>>
							<div class="layui-form-item">
								<label class="layui-form-label">驳回原因：</label>
								<div class="layui-input-inline" style="width:310px">
									<input type="text" name="info[reason]" value="<?php echo $info['reason']; ?>" class="layui-input"/>
								</div>
							</div>
						</div>
						
						<!--<div class="layui-form-item">-->
						<!--	<label class="layui-form-label">模块权限：</label>-->
						<!--	<div class="layui-input-inline">-->
						<!--		<input type="radio" name="uinfo[auth_type]" value="1" title="所有" <?php if(!$uinfo['id'] || $uinfo['auth_type']==1): ?>checked<?php endif; ?> lay-filter="auth_type">-->
						<!--		<input type="radio" name="uinfo[auth_type]" value="0" title="选择" <?php if($uinfo['id'] && $uinfo['auth_type']==0): ?>checked<?php endif; ?> lay-filter="auth_type">-->
						<!--	</div>-->
						<!--</div>-->
						<!--<div class="layui-form-item" id="authdataset" style="<?php if(!$uinfo['id'] || $uinfo['auth_type']==1): ?>display:none<?php endif; ?>"></div>-->
		  					<input type="hidden" name="uinfo[auth_type]" value="0">
							<!-- 需同步修改 web_user\edit.html -->
<div class="layui-form-item">
    <label class="layui-form-label">权限设置：</label>
    <div class="layui-input-block">
        <div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
            <input type="checkbox" title="全部选择" lay-skin="primary" lay-filter="checkall_all"/>
        </div>
        <?php $i=0; foreach($menudata as $k=>$v): $i++; ?>
        <div>
            <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
                <input type="checkbox" title="<?php echo $v['name']; ?>" lay-skin="primary" lay-filter="checkall"/>
            </div>
            <div style="margin-left:20px">
                <?php foreach($v['child'] as $k1=>$v1): 
                if(!$v1['authdata'] && $v1['child']){
                $path = array();
                echo '<div style="width: 100px;float:left;margin-top:8px;clear:left">'.$v1['name'].'</div>';
                foreach($v1['child'] as $v2){
                echo '<div style="min-width: 120px;float: left;">';
                echo '	<input type="checkbox" value="'.$v2['path'].','.str_replace('/*','^_^',$v2['authdata']).'" name="auth_data[]" '.(in_array($v2['path'].','.$v2['authdata'],$auth_data)?'checked':'').' title="'.$v2['name'].'" lay-skin="primary"/>';
                echo '</div>';
                }
                echo '<div style="clear:both;float: left;"></div>';
                }
                 if(!$v1['child'] || $v1['authdata']): ?>
                <div style="min-width: 120px;float: left;">
                    <input type="checkbox" value="<?php echo $v1['path']; ?>,<?php echo str_replace('/*','^_^',$v1['authdata']); ?>" name="auth_data[]" <?php if(in_array($v1['path'].','.$v1['authdata'],$auth_data)): ?>checked<?php endif; ?> title="<?php echo $v1['name']; ?>" lay-skin="primary"/>
                </div>
                <?php endif; ?>
                <?php endforeach; ?>
            </div>
            <div style="clear:both;float: left;margin-bottom:10px"></div>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<div class="layui-form-item">
    <label class="layui-form-label">手机端权限：</label>
    <div class="layui-input-block">
        <div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
            <input type="checkbox" title="全部选择" lay-skin="primary" lay-filter="checkall_all"/>
        </div>
        <?php if($thisuser['isadmin'] != 0 && $thisuser_wxauth): ?>
        <div>
            <div style="margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
                <input type="checkbox" title="查看权限" lay-skin="primary" lay-filter="checkall"/>
            </div>
            <div style="margin-left:20px">
                <?php if($bid==0 && ($thisuser['auth_type'] == 1 || in_array('member',$thisuser_wxauth))): ?>
                <div style="min-width: 120px;float: left;">
                    <input type="checkbox" value="member" name="wxauth_data[]" <?php if(in_array('member',$wxauth_data)): ?>checked<?php endif; ?> title="<?php echo t('会员'); ?>" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('product',$thisuser_wxauth)): ?>
                <div style="min-width: 120px;float: left;">
                    <input type="checkbox" value="product" name="wxauth_data[]" <?php if(in_array('product',$wxauth_data)): ?>checked<?php endif; ?> title="商品" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('order',$thisuser_wxauth)): ?>
                <div style="min-width: 120px;float: left;">
                    <input type="checkbox" value="order" name="wxauth_data[]" <?php if(in_array('order',$wxauth_data)): ?>checked<?php endif; ?> title="订单" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('finance',$thisuser_wxauth)): ?>
                <div style="min-width: 120px;float: left;">
                    <input type="checkbox" value="finance" name="wxauth_data[]" <?php if(in_array('finance',$wxauth_data)): ?>checked<?php endif; ?> title="财务" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('zixun',$thisuser_wxauth)): ?>
                <div style="min-width: 120px;float: left;">
                    <input type="checkbox" value="zixun" name="wxauth_data[]" <?php if(in_array('zixun',$wxauth_data)): ?>checked<?php endif; ?> title="咨询" lay-skin="primary"/>
                </div>
                <?php endif; ?>
                <div style="clear:both;float: left;margin-bottom:10px"></div>
            </div>
        </div>
        <?php endif; if($thisuser['isadmin'] != 0 && $thisuser_notice_auth): ?>
        <div>
            <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
                <input type="checkbox" title="接收通知权限" lay-skin="primary" lay-filter="checkall"/>
            </div>
            <div style="margin-left:20px">
                <?php if($thisuser['auth_type'] == 1 || in_array('tmpl_orderconfirm',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="订单提交通知" name="notice_auth_data[]" <?php if(in_array('tmpl_orderconfirm',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_orderconfirm" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_orderpay',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="订单支付通知" name="notice_auth_data[]" <?php if(in_array('tmpl_orderpay',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_orderpay" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_ordershouhuo',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="订单收货通知" name="notice_auth_data[]" <?php if(in_array('tmpl_ordershouhuo',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_ordershouhuo" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_ordertui',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="退款申请通知" name="notice_auth_data[]" <?php if(in_array('tmpl_ordertui',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_ordertui" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_withdraw',$thisuser_notice_auth)): if($bid==0): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="提现申请通知" name="notice_auth_data[]" <?php if(in_array('tmpl_withdraw',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_withdraw" lay-skin="primary"/>
                </div>
                <?php endif; ?>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_uplv',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="升级申请通知" name="notice_auth_data[]" <?php if(in_array('tmpl_uplv',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_uplv" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_formsub',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="表单提交通知" name="notice_auth_data[]" <?php if(in_array('tmpl_formsub',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_formsub" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('tmpl_kehuzixun',$thisuser_notice_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="用户咨询通知" name="notice_auth_data[]" <?php if(in_array('tmpl_kehuzixun',$notice_auth_data)): ?>checked<?php endif; ?> value="tmpl_kehuzixun" lay-skin="primary"/>
                </div>
                <?php endif; ?>
                <div style="clear:both;float: left;margin-bottom:10px"></div>
            </div>
        </div>
        <?php endif; if($thisuser['isadmin'] != 0 && $thisuser_hexiao_auth): ?>
        <div>
            <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
                <input type="checkbox" title="核销权限" lay-skin="primary" lay-filter="checkall"/>
            </div>
            <div style="margin-left:20px">
                <?php if($thisuser['auth_type'] == 1 || in_array('shop',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="商城订单" name="hexiao_auth_data[]" <?php if(in_array('shop',$hexiao_auth_data)): ?>checked<?php endif; ?> value="shop" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('collage',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="拼团订单" name="hexiao_auth_data[]" <?php if(in_array('collage',$hexiao_auth_data)): ?>checked<?php endif; ?> value="collage" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('lucky_collage',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="幸运拼团订单" name="hexiao_auth_data[]" <?php if(in_array('lucky_collage',$hexiao_auth_data)): ?>checked<?php endif; ?> value="lucky_collage" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('cycle',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="周期购" name="hexiao_auth_data[]" <?php if(in_array('cycle',$hexiao_auth_data)): ?>checked<?php endif; ?> value="cycle" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('kanjia',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="砍价订单" name="hexiao_auth_data[]" <?php if(in_array('kanjia',$hexiao_auth_data)): ?>checked<?php endif; ?> value="kanjia" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('seckill',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="秒杀订单" name="hexiao_auth_data[]" <?php if(in_array('seckill',$hexiao_auth_data)): ?>checked<?php endif; ?> value="seckill" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('yuyue',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="预约订单" name="hexiao_auth_data[]" <?php if(in_array('yuyue',$hexiao_auth_data)): ?>checked<?php endif; ?> value="yuyue" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('scoreshop',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="<?php echo t('积分'); ?>兑换" name="hexiao_auth_data[]" <?php if(in_array('scoreshop',$hexiao_auth_data)): ?>checked<?php endif; ?> value="scoreshop" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('coupon',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="<?php echo t('优惠券'); ?>" name="hexiao_auth_data[]" <?php if(in_array('coupon',$hexiao_auth_data)): ?>checked<?php endif; ?> value="coupon" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('choujiang',$thisuser_hexiao_auth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="抽奖活动" name="hexiao_auth_data[]" <?php if(in_array('choujiang',$hexiao_auth_data)): ?>checked<?php endif; ?> value="choujiang" lay-skin="primary"/>
                </div>
                <?php endif; ?>
                <div style="clear:both;float: left;margin-bottom:10px"></div>
            </div>
        </div>
        <?php endif; if($thisuser['isadmin'] != 0 && $restaurant_auth): ?>
        <div>
            <div style="clear:left;margin-top:10px;color:#303030; font-size:14px; font-weight:600; ">
                <input type="checkbox" title="餐饮权限" lay-skin="primary" lay-filter="checkall"/>
            </div>
            <div style="margin-left:20px">
                <?php if($thisuser['auth_type'] == 1 || in_array('restaurant_product',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="菜品管理" name="wxauth_data[]" <?php if(in_array('restaurant_product',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_product" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_table',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="餐桌设置" name="wxauth_data[]" <?php if(in_array('restaurant_table',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_table" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_tableWaiter',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="餐桌管理" name="wxauth_data[]" <?php if(in_array('restaurant_tableWaiter',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_tableWaiter" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_shop',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="点餐订单" name="wxauth_data[]" <?php if(in_array('restaurant_shop',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_shop" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_takeaway',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="外卖订单" name="wxauth_data[]" <?php if(in_array('restaurant_takeaway',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_takeaway" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_booking',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="预定" name="wxauth_data[]" <?php if(in_array('restaurant_booking',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_booking" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_deposit',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="寄存" name="wxauth_data[]" <?php if(in_array('restaurant_deposit',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_deposit" lay-skin="primary"/>
                </div>
                <?php endif; if($thisuser['auth_type'] == 1 || in_array('restaurant_queue',$thisuser_wxauth)): ?>
                <div style="width: 120px;float: left;">
                    <input type="checkbox" title="排队" name="wxauth_data[]" <?php if(in_array('restaurant_queue',$wxauth_data)): ?>checked<?php endif; ?> value="restaurant_queue" lay-skin="primary"/>
                </div>
                <?php endif; ?>
                <div style="clear:both;float: left;margin-bottom:10px"></div>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>


						<div class="layui-form-item">
							<label class="layui-form-label"></label>
							<div class="layui-input-block">
								<button class="layui-btn layui-btn-danger" lay-submit lay-filter="formsubmit">提 交</button>
							</div>
						</div>
					</div>
				</div>
      </div>
    </div>
  </div>
	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=<?php echo $qq_key; ?>"></script>
	<script>
	<?php if($area_on): ?>
	    	var form
    	layui.use(['form','layer'], function() {
              form = layui.form;
              var layer = layui.layer;
              
        });

        
        
	    function getCitys(province){
	        if(!province)province=1;
	        $("#city").html("")
	        $.post("<?php echo url('Area/getcitys'); ?>",{province},function(data){
	            console.log(data)
	            // 遍历选项数据并添加到 select 中
	            let defaultcity = "<?php echo $info['city_id']; ?>"
	            let city = 0;
	            for(index in data.data){
	                let option = data.data[index];
	                if(!defaultcity)city= option.id;
                    let selected = option.id == defaultcity
                      $("#city").append($("<option>", {
                        value: option.id,
                        text: option.name,
                        selected: selected
                      }));
	                
	            }
	            form.render('select'); // 重新渲染表单，使新添加的 option 生效
                getDistricts(city);
	        })
	    }
	    function getDistricts(city=0){
	        console.log(city)
	        if(!city)city=$('#city').val();
	        $("#district").html("")
	        $.post("<?php echo url('Area/getdistricts'); ?>",{city},function(data){
	            console.log(data)
	            // 遍历选项数据并添加到 select 中
	            let defaultdistrict = "<?php echo $info['district_id']; ?>"
	            let district = 0;
	            for(index in data.data){
	                let option = data.data[index];
	                if(!defaultdistrict)district= option.id;
	                let selected = option.id == defaultdistrict
                      $("#district").append($("<option>", {
                        value: option.id,
                        text: option.name,
                        selected: selected
                      }));
	            }
                form.render('select'); // 重新渲染表单，使新添加的 option 生效
                getStreets(district);
	        })
	    } 
	    
	    function getStreets(district=0){
	        console.log(district)
	        if(!district)district=$('#district').val();
	        $("#street").html("")
	        $.post("<?php echo url('Area/getstreets'); ?>",{district},function(data){
	            console.log(data)
	            // 遍历选项数据并添加到 select 中
	            for(index in data.data){
	                let option = data.data[index];
	                let selected = option.id == "<?php echo $info['street_id']; ?>"
                      $("#street").append($("<option>", {
                        value: option.id,
                        text: option.name,
                        selected: selected
                      }));
	            }
                form.render('select'); // 重新渲染表单，使新添加的 option 生效
	        })
	    }
	
	
	    $(function(){
	        //初始化地址数据
	        getCitys($("#province").val());
	    })
	    
	layui.form.on('select(changeprovince)', function(data){
	    console.log(data)
		getCitys(data.value);
	})
	layui.form.on('select(changecity)', function(data){
		getDistricts(data.value);
	})
	layui.form.on('select(changedistrict)', function(data){
		getStreets(data.value);
	})
	
	<?php endif; ?>
	
	
	
	
	
	
	
	layui.form.on('radio(wxpayst)',function(data){
		if(data.value == '1'){
			$('#wxpayst_div').show();
		}else{
			$('#wxpayst_div').hide();
		}
	})

	layui.form.on('radio(auth_type)', function(data){
		if(data.value == '0'){
			$('#authdataset').show();
		}else{
			$('#authdataset').hide();
		}
	})
	layui.form.on('checkbox(checkall_all)',function(data){
		if(data.elem.checked){
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',true);
		}else{
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',false);
		}
		layui.form.render('checkbox'); 
	})
	layui.form.on('checkbox(checkall)',function(data){
		if(data.elem.checked){
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',true);
		}else{
			$(data.elem).parent().parent().find('input[type=checkbox]').prop('checked',false);
		}
		layui.form.render('checkbox'); 
	})

	
	layui.form.on('radio(changest)', function(data){
		if(data.value == '2'){
			$('#changestset').show();
		}else{
			$('#changestset').hide();
		}
	})
	var ueditor = UE.getEditor('content');
	layui.form.on('submit(formsubmit)', function(obj){
		var field = obj.field
		field['info[content]'] = ueditor.getContent();
		//console.log(field);return;
		var index = layer.load();
		$.post("<?php echo url('save'); ?>",field,function(data){
			layer.close(index);
			dialog(data.msg,data.status);
			if(data.status == 1){
				setTimeout(function(){
					parent.layer.close(parent.layer.getFrameIndex(window.name));
					parent.tableIns.reload()
				},1000)
			}
		})
	})
  </script>
	<script>
	layui.use(['form', 'layer'], function(){
      var form = layui.form;
      var layer = layui.layer;
      
      // 初始化地图
      var center = new qq.maps.LatLng(39.916527,116.397128);
      var map = new qq.maps.Map(document.getElementById('l-map'), {
        center: center,
        zoom: 13
      });

      // 初始化标记点
      var marker = new qq.maps.Marker({
        position: center,
        map: map
      });

      // 初始化地址服务
      var geocoder = new qq.maps.Geocoder({
        complete: function(result) {
          if(result.detail.status === 0) {
            var address = result.detail.address;
            $("#address").val(address);
          }
        }
      });

      // 如果已有经纬度,则定位到已有位置
      var lng = $("#mapjd").val();
      var lat = $("#mapwd").val();
      if(lng && lat) {
        var pos = new qq.maps.LatLng(lat, lng);
        map.setCenter(pos);
        marker.setPosition(pos);
      }
      
      // 点击地图更新标记和经纬度
      qq.maps.event.addListener(map, 'click', function(event) {
        var lat = event.latLng.getLat();
        var lng = event.latLng.getLng();
        
        marker.setPosition(event.latLng);
        $("#mapjd").val(lng);
        $("#mapwd").val(lat);
        
        // 根据坐标获取地址
        geocoder.getAddress(event.latLng);
      });
      
      // 监听地址输入,自动定位
      $("#searchbtn").click(function() {
        var address = $("#address").val().trim();
        if (!address) {
          layer.msg('请输入地址');
          return;
        }
        
        // 使用WebService API搜索
        var center = map.getCenter();
        $.ajax({
          url: 'https://apis.map.qq.com/ws/place/v1/search',
          type: 'GET',
          data: {
            keyword: address,
            boundary: 'nearby(' + center.getLat() + ',' + center.getLng() + ',1000)',
            key: '<?php echo $qq_key; ?>',
            output: 'jsonp'
          },
          dataType: 'jsonp',
          success: function(res) {
            if(res.status === 0 && res.data.length > 0) {
              var poi = res.data[0];
              var latLng = new qq.maps.LatLng(poi.location.lat, poi.location.lng);
              map.setCenter(latLng);
              marker.setPosition(latLng);
              $("#mapjd").val(poi.location.lng);
              $("#mapwd").val(poi.location.lat);
              $("#address").val(poi.address);
            } else {
              layer.msg('未找到该地址');
            }
          },
          error: function() {
            layer.msg('搜索服务出错');
          }
        });
      });
    });

	function previewImg(url){
		layer.open({type:1,area:'500px',content:'<div style="margin:auto auto;text-align:center"><img src="'+url+'" style="max-width:100%;max-height:100%"/></div>',title:false,shadeClose:true})
	}
	</script>
</body>
</html>
