<template>
	<view>
		<view class="common-header education-header pb16">
			<view class="common-header-title">{{ partJobVo.title }}</view>
			<view class="rpo-salary">
				<view class="rpo-salary-num">{{ partJobVo.salary }}</view>
			</view>
			<view class="common-list-tag list-tag-box"
				v-if="partJobVo.labelList && partJobVo.labelList.descLabels && partJobVo.labelList.descLabels.length > 0">
				<view class="list-tag" v-for="(label, index) in partJobVo.labelList.descLabels" :key="index">
					<view class="ellipsis">{{ label.labelName }}</view>
				</view>
			</view>
			
			<!-- <view class="anchor-new-company" v-if="partJobVo.company">
				<image :src="partJobVo.company.logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png'" mode="aspectFill"></image>
				<view class="anchor-new-info">
					<view class="name ellipsis">{{ partJobVo.company.name }}</view>
					<view class="desc ellipsis">{{ partJobVo.company.industry }} | {{ partJobVo.company.scale }}</view>
				</view>
			</view> -->
<!-- 
			<view class="anchor-new-place">
				<view class="place-bold">{{ partJobVo.address }}</view>
				<view class="place-normal">{{ partJobVo.province }} {{ partJobVo.city }} {{ partJobVo.district }}</view>
			</view> -->

			<view @tap="skiptoWebview" class="common-safe ptp_exposure_static" data-ptpid="69a4-1137-8c63-fcc7"
				id="pid=69a4-1137-8c63-fcc7">
				<view class="common-safe-left">
					<view class="iconfont iconsafeguard"></view>
					<image lazyLoad class="common-safe-image" src="https://qiniu-image.qtshe.com/20200812safe.png">
					</image>
					<view>放心投递 职位无忧</view>
				</view>
				<view class="iconfont iconarrow"></view>
			</view>
		</view>
		
		<view class="common-box" style="margin-top: 32rpx;">
			<view class="common-desc-box common-desc-box-top">
				<view class="iconfont iconcomputer"></view>
				不限时间地点
			</view>
			<view class="common-desc-box common-desc-box-top bt" >
				<view class="iconfont iconnote_confirm"></view>
				<view class="common-desc-right">
					<view class="common-desc-right-box">岗位要求</view>
					<view class="common-desc-right-text mt10">
						<block v-for="(item, index) in partJobVo.requireList" :key="index">
							<text style="margin-left: 8rpx; margin-right: 8rpx; font-size: 24rpx"
								v-if="index > 0">|</text>
							{{ item }}
							<text @tap="healthVisibleControl" class="iconfont iconquestion1" :data-index="index"
								data-ptpid="917e-1900-b017-74db" id="pid=917e-1900-b017-74db"
								v-if="item === '需要健康证'"></text>
						</block>
					</view>
				</view>
			</view>
		</view>

		<view class="common-title" style="margin-top: 48rpx;">职位描述</view>
		<view class="common-box">
			<view :class="'detail-info-box ' + (isDescShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.description || '暂无描述')"></rich-text>
			</view>
			<view @tap="descBtnTap" class="detail-info-btn" v-if="isDescShowBtn && isComputedDesc">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;">任职要求</view>
		<view class="common-box">
			<view :class="'detail-info-box ' + (isReqShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.requirement || '暂无要求')"></rich-text>
			</view>
			<view @tap="reqBtnTap" class="detail-info-btn" v-if="isReqShowBtn && isComputedReq">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;">培训详情</view>
		<view class="common-box">
			<view :class="'detail-info-box ' + (isInfoShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.jobDesc || '1. 岗前培训：我们将提供专业的岗前培训，包括工作流程、服务标准、操作规范等内容。\n2. 技能培训：定期组织专业技能培训，提升员工的专业水平。\n3. 职业发展：提供清晰的职业发展路径，有机会晋升为店长或区域经理。\n4. 在岗指导：配备经验丰富的导师，提供一对一指导。')"></rich-text>
			</view>
			<view @tap="infoBtnTap" class="detail-info-btn" v-if="isInfoShowBtn && isComputedInfo">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;">入选专区</view>
		<view class="chosen-inner-item single">
			<view class="single-box">
				<image class="logo" src="https://qiniu-image.qtshe.com/entrance/bd_small.png"></image>
				<view>
					<view class="title ellipsis">高薪急招</view>
					<view class="desc">入选高薪急招专区</view>
				</view>
			</view>
			<view class="btn orange">进入榜单</view>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;" v-if="partJobVo.company && partJobVo.company.introduction">公司介绍</view>
		<view class="common-box" v-if="partJobVo.company && partJobVo.company.introduction">
			<view :class="'detail-info-box ' + (isCompanyShowBtn ? '' : 'detail-info-show')">
				<rich-text class="detail-info-text" :nodes="formatRichText(partJobVo.company.introduction)"></rich-text>
			</view>
			<view @tap="companyBtnTap" class="detail-info-btn" v-if="isCompanyShowBtn && isComputedCompany">
				查看更多
				<view class="iconfont iconarrow_down"></view>
			</view>
		</view>
		
		<view class="common-title" style="margin-top: 48rpx;">{{ partJobVo.company.companyType === 2 ? '发布者' : '发布企业' }}</view>
		<view class="common-box" :class="(partJobVo.company.isOfficialAccount || partJobVo.company.companyType === 2 || (partJobVo.company.companyType === 1 && hasEyeAuth) ? 'pb0' : '')">
			<view @tap="$emit('clickCompany')" class="common-company ptp_exposure_static" data-ptpid="c4e6-18fa-96fc-d96d" id="pid=c4e6-18fa-96fc-d96d">
				<view class="common-company-box">
					<image lazyLoad class="common-company-logo" :src="partJobVo.company.logo || 'https://qiniu-image.qtshe.com/company_default_5.4.png'"></image>
					<view class="common-company-main">
						<view class="common-company-name ellipsis">{{ partJobVo.company.name }}</view>
						<view class="common-company-auth ignoreT2">
							<view class="iconfont iconverified company-icon-blue"></view>
							<view>企业认证</view>
							<image class="common-company-auth-icon" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon.png"></image>
						</view>
					</view>
				</view>
				<view class="iconfont iconarrow" v-if="hasEyeAuth"></view>
			</view>
			<view class="official-box">
				<view class="official-item">
					<view class="iconfont iconconfirm_round"></view>
					已通过天眼查认证
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			partJobVo: {
				type: Object,
				default: () => ({
					title: '',
					salary: '',
					description: '',
					requirement: '',
					address: '',
					province: '',
					city: '',
					district: '',
					jobDesc: '',
					company: {
						name: '',
						logo: '',
						industry: '',
						scale: '',
						introduction: ''
					},
					labelList: {
						descLabels: []
					},
					requireList: []
				})
			},
			chosenList: {
				type: Array,
				default: () => []
			},
			tabCurrent: {
				type: Number,
				default: 0
			},
			hasEyeAuth: {
				type: Boolean,
				default: false
			},
			isShowAll: {
				type: Boolean,
				default: false
			},
			agreementVo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				isInfoShowBtn: true,
				isComputedInfo: false,
				isDescShowBtn: true,
				isComputedDesc: false,
				isReqShowBtn: true,
				isComputedReq: false,
				isCompanyShowBtn: true,
				isComputedCompany: false,
				healthVisible: false
			}
		},
		created() {
			console.log('education组件初始化', this.partJobVo);
			this.$nextTick(() => {
				this.getInfoHeight();
				this.getDescHeight();
				this.getReqHeight();
				this.getCompanyHeight();
			});
		},
		methods: {
			// 跳转到公司
			jumpToCompany() {
				if (this.hasEyeAuth && this.partJobVo.company && this.partJobVo.company.id) {
					uni.navigateTo({
						url: "/pagesExa/zhaopin/company?id=" + this.partJobVo.company.id
					});
				}
			},
			// 格式化富文本
			formatRichText(text) {
				if (!text) return '';
				return text;
			},
			// 判断职位描述是否显示更多
			getDescHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(4) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isDescShowBtn = res1.height > (44 * 4);
							this.isComputedDesc = true;
						} else {
							this.isDescShowBtn = false;
							this.isComputedDesc = true;
						}
					}).exec();
				}, 500);
			},
			// 判断任职要求是否显示更多
			getReqHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(6) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isReqShowBtn = res1.height > (44 * 4);
							this.isComputedReq = true;
						} else {
							this.isReqShowBtn = false;
							this.isComputedReq = true;
						}
					}).exec();
				}, 500);
			},
			// 判断培训详情是否显示更多
			getInfoHeight() {
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.common-box:nth-child(8) .detail-info-text').boundingClientRect(res1 => {
						if (res1 && res1.height) {
							this.isInfoShowBtn = res1.height > (44 * 4);
							this.isComputedInfo = true;
						} else {
							this.isInfoShowBtn = false;
							this.isComputedInfo = true;
						}
					}).exec();
				}, 500);
			},
			// 判断公司介绍是否显示更多
			getCompanyHeight() {
				if (this.partJobVo.company && this.partJobVo.company.introduction) {
					setTimeout(() => {
						const query = uni.createSelectorQuery().in(this);
						query.select('.common-box:nth-child(12) .detail-info-text').boundingClientRect(res1 => {
							if (res1 && res1.height) {
								this.isCompanyShowBtn = res1.height > (44 * 4);
								this.isComputedCompany = true;
							} else {
								this.isCompanyShowBtn = false;
								this.isComputedCompany = true;
							}
						}).exec();
					}, 500);
				}
			},
			// 点击展开职位描述
			descBtnTap() {
				this.isDescShowBtn = false;
			},
			// 点击展开任职要求
			reqBtnTap() {
				this.isReqShowBtn = false;
			},
			// 点击展开培训详情
			infoBtnTap() {
				this.isInfoShowBtn = false;
			},
			// 点击展开公司介绍
			companyBtnTap() {
				this.isCompanyShowBtn = false;
			}
		}
	}
</script>

<style lang="scss">
	@import './education.scss';	
	
	.common-header-title {
	    color: #111e38;
	    font-size: 44rpx;
	    line-height: 60rpx;
	    font-weight: 700;
	    display: -webkit-box;
	    -webkit-box-orient: vertical;
	    -webkit-line-clamp: 2;
	    overflow: hidden;
	}
	
	.detail-info-box {
		position: relative;
		overflow: hidden;
		transition: all 0.3s;
		
		&:not(.detail-info-show) {
			.detail-info-text {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 4;
				overflow: hidden;
				padding-bottom: 40rpx;
			}
		}
		
		&.detail-info-show {
			.detail-info-text {
				display: block;
				padding-bottom: 24rpx;
			}
		}
		
		.detail-info-text {
			font-size: 28rpx;
			line-height: 44rpx;
			color: #111E38;
			
			::v-deep {
				p {
					margin: 0;
					padding: 0;
					line-height: 44rpx;
				}
				
				ul {
					margin: 16rpx 0;
					padding-left: 40rpx;
					list-style: disc;
					
					li {
						margin: 16rpx 0;
						line-height: 44rpx;
						
						p {
							margin: 0;
							line-height: 44rpx;
						}
					}
				}
				
				a {
					color: #3370ff;
					text-decoration: none;
				}
			}
		}
		
		&:not(.detail-info-show)::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 120rpx;
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.9) 50%, #FFFFFF 100%);
			pointer-events: none;
		}
	}
	
	.detail-info-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		color: #3370ff;
		font-size: 28rpx;
		margin-top: -40rpx;
		position: relative;
		z-index: 1;
		
		.iconarrow_down {
			margin-left: 8rpx;
			font-size: 24rpx;
		}
	}
	
	
	
	.chosen-inner-item.single {
		width: 686rpx;
		height: 132rpx;
		padding: 24rpx 32rpx 24rpx 24rpx;
		background: #fff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 24rpx;
		margin: 0 auto;
		
		.single-box {
			display: flex;
			align-items: center;
			
			.logo {
				width: 64rpx;
				height: 64rpx;
				margin-right: 16rpx;
			}
			
			.title {
				font-size: 32rpx;
				color: #111e38;
				font-weight: 700;
				line-height: 44rpx;
				max-width: 406rpx;
			}
			
			.desc {
				font-size: 24rpx;
				color: #808999;
				line-height: 32rpx;
				margin-top: 4rpx;
			}
		}
		
		.btn {
			width: 140rpx;
			height: 56rpx;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			
			&.orange {
				color: #ff8000;
				background: #fff2e5;
			}
			
			&.green {
				color: #00ca88;
				background: #e5fcf4;
			}
		}
	}
</style>
