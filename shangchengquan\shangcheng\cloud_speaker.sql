-- 云喇叭功能数据库更新SQL
-- 在admin_set表中添加云喇叭相关字段

-- 添加云喇叭开关字段
ALTER TABLE `ddwx_admin_set` ADD COLUMN `cloud_speaker_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '云喇叭开关：0关闭，1开启';

-- 添加云喇叭设备号字段
ALTER TABLE `ddwx_admin_set` ADD COLUMN `cloud_speaker_device_id` varchar(50) NOT NULL DEFAULT '' COMMENT '云喇叭设备编号';

-- 创建云喇叭播报日志表（可选，用于记录播报历史）
CREATE TABLE IF NOT EXISTS `ddwx_cloud_speaker_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `device_id` varchar(50) NOT NULL DEFAULT '' COMMENT '设备编号',
  `message` text NOT NULL COMMENT '播报消息',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联订单ID',
  `order_type` varchar(20) NOT NULL DEFAULT '' COMMENT '订单类型：maidan买单',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '播报状态：0失败，1成功',
  `response` text COMMENT '接口响应内容',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_aid` (`aid`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_order` (`order_id`, `order_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云喇叭播报日志表';
