<?php
/**
 * 鲸犀商城 - 微信公众号小程序商城系统!
 * Copyright © 2020 鲸犀网络科技有限公司 保留所有权利
 * =========================================================
 * 版本：供应链版本
 * 授权主体：无限制版本
 * 授权域名：@@@
     
 * ----------------------------------------------
 * 您只能在商业授权范围内使用，不可二次转售、分发、分享、传播
 * 任何企业和个人不得对代码以任何目的任何形式的再发布
 * =========================================================
 */

namespace app\model;
use think\facade\Db;
use think\facade\Log;
class Payorder
{
	//创建支付订单
	public static function createorder($aid,$bid,$mid,$type,$orderid,$ordernum,$title,$money,$score=0,$pay_s_info=""){
		$data = [];
		$data['aid'] = $aid;
		$data['bid'] = $bid;
		$data['mid'] = $mid;
		$data['orderid'] = $orderid;
		$data['ordernum'] = $ordernum;
		$data['title'] = $title;
		$data['money'] = $money;
        $data['s_money'] = 0;
        $data['qixian_zhongheng'] = 0;
		$data['score'] = $score;
		$data['pay_s_info'] = $pay_s_info;
		$data['type'] = $type; //shop collage scoreshop kanjia seckill recharge designerpage form
		$data['createtime'] = time();
		$id = Db::name('payorder')->insertGetId($data);
        // var_dump($data);die;
		if($type == 'shop_hb'){
			Db::name('shop_order')->where('ordernum','like',$ordernum.'%')->update(['payorderid'=>$id]);
		}else{
			Db::name(''.$type.'_order')->where('id',$orderid)->update(['payorderid'=>$id]);
		}
		return $id;
	}
	//修改订单
	public static function updateorder($id,$newOrdernum,$newprice,$updateOrderId=0){

//        // 查询订单原价
//        $order_info = Db::name('shop_order')->where('aid',aid)->where('id', $orderid)->find();
//        // 查询订单原来应该支付金额
//        $pay_money = Db::name('payorder')->where('aid',aid)->where('id', $order_info['payorderid'])->value('money');
//        // 计算修改差价
//        $order_num = explode('_', $order_info['ordernum'])[0] ?? $order_info['ordernum'] ; // 原支付订单号
//        // 查询同笔订单信息
//        $shop_order_info = Db::name('shop_order')->where('aid',aid)->where('ordernum', 'like',  $order_num . '%' )->select()->toArray();
//
//        // print_r($shop_order_info);die;
//
//        foreach ($shop_order_info as $k=>$v){
//            // 修改订单号
//            $new_order_num = str_replace($order_num, $newordernum, $v['ordernum']) ;
//            $update_arr = [ 'ordernum'=>$new_order_num  ] ;
//            // 只有当前修改的订单改价 其它子订单只修改单号 解决支付出现重复单号-金额不同问题
//            if($v['id'] == $orderid){
//                $update_arr['totalprice'] = $newprice ; // 后台改动后金额
//            }
//            Db::name('shop_order')->where('aid',aid)->where('id',$v['id'])->update($update_arr);
//            Db::name('shop_order_goods')->where('aid',aid)->where('orderid',$v['id'])->update(['ordernum'=>$new_order_num]);
//            // 修改子支付订单 订单号
//            Db::name('payorder')->where('ordernum', $v['ordernum'])->update(['ordernum'=>$new_order_num]);
//        }
//
//        // 计算差价 修改 总支付订单差价
//        if($order_info['totalprice'] > $newprice){
//            $newprice = bcsub($pay_money, bcsub($order_info['totalprice'], $newprice, 2), 2) ;
//        }else{
//            $newprice = bcadd($pay_money, bcsub($newprice, $order_info['totalprice'], 2), 2) ;
//        }

        $payorder = Db::name('payorder')->where('id',$id)->find();
        if(in_array($payorder['type'],['shop_hb','restaurant_takeaway_hb'])){
            //合并支付订单重新计算
            // 查询同笔订单信息
            $child_order = Db::name('payorder')->where('aid',$payorder['aid'])->where('ordernum', 'like',  $payorder['ordernum'] . '_%' )->select()->toArray();
            //修改子价格，修改订单号
            if($child_order){
                $totalprice = 0;
                $newOrdernumArr = explode('_',$newOrdernum);
                $newOrdernum = $newOrdernumArr[0];
                foreach ($child_order as $v){
                    // 修改订单号
                    $order_num = explode('_',$v['ordernum']);
                    $new_order_num = str_replace($order_num[0], $newOrdernum, $v['ordernum']) ;
                    $update_arr = ['ordernum'=>$new_order_num] ;
                    if($v['orderid'] == $updateOrderId){
                        $update_arr['money'] = $newprice ; //改动后金额
                        $totalprice += $newprice;
                    }else{
                        $totalprice += $v['money'];
                        $where=[];
                        $where[]=['aid','=',$v['aid']];
                        $where[]=['id','=',$v['orderid']];
                        Db::name($v['type'].'_order')->where($where)->where('id',$v['orderid'])->update($update_arr);
                        Db::name($v['type'].'_order_goods')->where($where)->where('orderid',$v['orderid'])->update($update_arr);
                    }
                    Db::name('payorder')->where('id',$v['id'])->update($update_arr);
                }

                //修改主价格，修改订单号
                Db::name('payorder')->where('id',$id)->update(['ordernum'=>$newOrdernum,'money'=>$totalprice]);
            }
        }else{
            Db::name('payorder')->where('id',$id)->update(['ordernum'=>$newOrdernum,'money'=>$newprice]);
        }
	}
	
	//商店支付订单;
	public static function shop_pay_order($orderid)
	{
// 	    $member = Db::name('member')->where('id',$order['mid'])->find();
// 	    $tmplcontent = array();
// 		$tmplcontent['first'] = '该订单支付成功';
// 		$tmplcontent['remark'] = '点击进入查看~';
// 		$tmplcontent['accountType'] = t('会员').'昵称';
// 		$tmplcontent['account'] = $member['nickname'];
// 		$tmplcontent['amount'] = $order['money'].'元' . ($givemoney>0?'+赠送'.$givemoney.'元':'');
// 		$tmplcontent['result'] = '支付成功';
// 		\app\common\Wechat::sendhttmpl($aid,0,'tmpl_recharge',$tmplcontent,m_url('admin/finance/rechargelog', $aid),$order['mdid']);
	}
	
	//充值订单
	public static function recharge_pay_no_add_member($orderid){
		$order = Db::name('recharge_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];

		// \app\common\Member::addmoney($aid,$mid,$order['money'],t('余额').'充值');
		//充值赠送
		$giveset = Db::name('recharge_giveset')->where('aid',$aid)->find();
		if($giveset && $giveset['status']==1){
			$givedata = json_decode($giveset['givedata'],true);
		}else{
			$givedata = array();
		}
		$givemoney = 0;
		$givescore = 0;
		$moneyduan = 0;
		if($givedata){
			foreach($givedata as $give){
				if($order['money']*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
					$moneyduan = $give['money']*1;
					$givemoney = $give['give']*1;
                    $givescore = $give['give_score']*1;
				}
			}
		}
		if($givemoney > 0){
			// \app\common\Member::addmoney($aid,$mid,$givemoney,'充值赠送');
		}
        if($givescore > 0){
            \app\common\Member::addscore($aid,$mid,$givescore,'充值赠送');
        }



		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'recharge',$order['money']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}

		//升级
		\app\common\Member::uplv($aid,$mid);


		$tmplcontent = array();
		$tmplcontent['first'] = '有新充值订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['accountType'] = t('会员').'昵称';
		$tmplcontent['account'] = $member['nickname'];
		$tmplcontent['amount'] = $order['money'].'元' . ($givemoney>0?'+赠送'.$givemoney.'元':'');
		$tmplcontent['result'] = '充值成功';
		\app\common\Wechat::sendhttmpl($aid,0,'tmpl_recharge',$tmplcontent,m_url('admin/finance/rechargelog', $aid),$order['mdid']);

		//充值通知
        if(getcustom('zhaopin')){
            \app\model\Zhaopin::sendhtsms('tmpl_recharge',[],$order['aid'],$order['bid'],$order['mdid']);
        }
	}
	
	public  static function payorder_by_shop($orderid,$paytype,$paytypeid,$paynum='')
	{
	      if(!$orderid) return;
		$payorder = Db::name('payorder')->where('id',$orderid)->find();
		//dump($payorder.'看看支付后的操作');
		if(!$payorder || $payorder['status']==1) return ['status'=>0,'msg'=>'该订单已支付'];
        
        
		if(getcustom('pay_yuanbao') && $payorder['type'] == 'shop'){
            if($payorder['is_yuanbao_pay'] == 1){
                $paytype = t('元宝').'支付+'.$paytype;
            }
            //元宝 更新shop_order和payorder
            self::yuanbao_up($payorder['type'],$payorder);
        }
       

		if($payorder['type'] == 'restaurant_shop' && $paytypeid == 4) {
            Db::name('payorder')->where('id',$orderid)->update(['paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum]);
        } else {
            Db::name('payorder')->where('id',$orderid)->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum]);
        }
		$type = $payorder['type'];
 

		if($type == 'shop_hb'){
		    
			Db::name('shop_order')->where('ordernum','like',$payorder['ordernum'].'%')->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}elseif($type == 'balance'){
			Db::name('shop_order')->where('id',$payorder['orderid'])->update(['balance_pay_status'=>1,'balance_pay_orderid'=>$orderid]);
			return ['status'=>1,'msg'=>''];
		}elseif($type == 'yuyue_balance'){
			Db::name('yuyue_order')->where('id',$payorder['orderid'])->update(['status'=>3,'balance_pay_status'=>1,'balance_pay_orderid'=>$orderid]);
		}elseif($type == 'yuyue_addmoney'){
			Db::name('yuyue_order')->where('id',$payorder['orderid'])->update(['addmoneyStatus'=>1]);
		}elseif($type == 'seckill2'){
			Db::name('seckill2_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
			$form_sale = Db::name('seckill2_order')->alias('or')->join('seckill2_product pro','or.proid = pro.id')->field('pro.saleid')->where('or.ordernum',$payorder['ordernum'])->find();
			$sale = Db::name('seckill2_sale')->where('id',$form_sale['saleid'])->find();
			Db::name('seckill2_order')->where('ordernum',$sale['form_ordernum'])->update(['status'=>10]);
		}elseif($type == 'form'){
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paystatus'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}elseif($type == 'plug_business_pay'){
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'restaurant_shop' && $paytypeid == 4){
            // Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'workorder'){  //工单订单
            Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paystatus'=>1,'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'yuyue_package'){  // 处理套餐订单特殊情况，使用pay_time而不是paytime
            // 套餐订单表使用pay_time字段而不是paytime
            Db::name($type.'_order')->where('id',$payorder['orderid'])->update([
                'status'=>1,
                'pay_time'=>time(),  // 注意：这里使用pay_time而不是paytime
                'paytype'=>$paytype,
                'paytypeid'=>$paytypeid,
                'paynum'=>$paynum,
                'platform'=>$payorder['platform']
            ]);
        }else{
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}

		if(getcustom('invite_free')){
        	if($payorder['type'] == 'shop'){
        		//发送订单通知
        		self::send_free_notice($payorder);
        	}
        }
		$fun = $type.'_pay';
		
		// $res = self::$fun($payorder['orderid'],$payorder['ordernum']);
       // $res = self::recharge_pay_no_add_member($payorder['orderid'],$payorder['ordernum']);
        self::afterusecoupon($payorder['orderid'],$type,1,$payorder['ordernum']);
        
//         //升级
// 		\app\common\Member::uplv($aid,$mid);
        
        
//         $member = Db::name('member')->where('id',$order['mid'])->find();
// 		$tmplcontent = array();
// 		$tmplcontent['first'] = '有新订单支付成功';
// 		$tmplcontent['remark'] = '点击进入查看~';
// 		$tmplcontent['accountType'] = t('会员').'昵称';
// 		$tmplcontent['account'] = $member['nickname'];
// 		$tmplcontent['amount'] = $order['money'].'元' . ($givemoney>0?'+赠送'.$givemoney.'元':'');
// 		$tmplcontent['result'] = '充值成功';
// 		\app\common\Wechat::sendhttmpl($aid,0,'tmpl_recharge',$tmplcontent,"",$order['mdid']);
		return ['status'=>1,'msg'=>'']; 
           
	}
	
	public static function payorder_no_updateMemberbalance($orderid,$paytype,$paytypeid,$paynum='')
	{
	    
	    if(!$orderid) return;
		$payorder = Db::name('payorder')->where('id',$orderid)->find();
		//dump($payorder.'看看支付后的操作');
		if(!$payorder || $payorder['status']==1) return ['status'=>0,'msg'=>'该订单已支付'];

		if(getcustom('pay_yuanbao') && $payorder['type'] == 'shop'){
            if($payorder['is_yuanbao_pay'] == 1){
                $paytype = t('元宝').'支付+'.$paytype;
            }
            //元宝 更新shop_order和payorder
            self::yuanbao_up($payorder['type'],$payorder);
        }
       

		if($payorder['type'] == 'restaurant_shop' && $paytypeid == 4) {
            Db::name('payorder')->where('id',$orderid)->update(['paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum]);
        } else {
            Db::name('payorder')->where('id',$orderid)->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum]);
        }
		$type = $payorder['type'];
 

		if($type == 'shop_hb'){
			Db::name('shop_order')->where('ordernum','like',$payorder['ordernum'].'%')->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}elseif($type == 'balance'){
			Db::name('shop_order')->where('id',$payorder['orderid'])->update(['balance_pay_status'=>1,'balance_pay_orderid'=>$orderid]);
			return ['status'=>1,'msg'=>''];
		}elseif($type == 'yuyue_balance'){
			Db::name('yuyue_order')->where('id',$payorder['orderid'])->update(['status'=>3,'balance_pay_status'=>1,'balance_pay_orderid'=>$orderid]);
		}elseif($type == 'yuyue_addmoney'){
			Db::name('yuyue_order')->where('id',$payorder['orderid'])->update(['addmoneyStatus'=>1]);
		}elseif($type == 'seckill2'){
			Db::name('seckill2_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
			$form_sale = Db::name('seckill2_order')->alias('or')->join('seckill2_product pro','or.proid = pro.id')->field('pro.saleid')->where('or.ordernum',$payorder['ordernum'])->find();
			$sale = Db::name('seckill2_sale')->where('id',$form_sale['saleid'])->find();
			Db::name('seckill2_order')->where('ordernum',$sale['form_ordernum'])->update(['status'=>10]);
		}elseif($type == 'form'){
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paystatus'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}elseif($type == 'plug_business_pay'){
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'restaurant_shop' && $paytypeid == 4){
            // Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'workorder'){  //工单订单
            Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paystatus'=>1,'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'yuyue_package'){  // 处理套餐订单特殊情况，使用pay_time而不是paytime
            // 套餐订单表使用pay_time字段而不是paytime
            Db::name($type.'_order')->where('id',$payorder['orderid'])->update([
                'status'=>1,
                'pay_time'=>time(),  // 注意：这里使用pay_time而不是paytime
                'paytype'=>$paytype,
                'paytypeid'=>$paytypeid,
                'paynum'=>$paynum,
                'platform'=>$payorder['platform']
            ]);
        }else{
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}

		if(getcustom('invite_free')){
        	if($payorder['type'] == 'shop'){
        		//发送订单通知
        		self::send_free_notice($payorder);
        	}
        }
		$fun = $type.'_pay';
		
		// $res = self::$fun($payorder['orderid'],$payorder['ordernum']);
        $res = self::recharge_pay_no_add_member($payorder['orderid'],$payorder['ordernum']);
        self::afterusecoupon($payorder['orderid'],$type,1,$payorder['ordernum']);
		return ['status'=>1,'msg'=>''];
	}
	//支付完成后操作
	public static function payorder($orderid,$paytype,$paytypeid,$paynum=''){
		if(!$orderid) return;
		$payorder = Db::name('payorder')->where('id',$orderid)->find();
		//dump($payorder.'看看支付后的操作');
		if(!$payorder || $payorder['status']==1) return ['status'=>0,'msg'=>'该订单已支付'];

		if(getcustom('pay_yuanbao') && $payorder['type'] == 'shop'){
            if($payorder['is_yuanbao_pay'] == 1){
                $paytype = t('元宝').'支付+'.$paytype;
            }
            //元宝 更新shop_order和payorder
            self::yuanbao_up($payorder['type'],$payorder);
        }
       	

		if($payorder['type'] == 'restaurant_shop' && $paytypeid == 4) {
            Db::name('payorder')->where('id',$orderid)->update(['paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum]);
        } else {
            Db::name('payorder')->where('id',$orderid)->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum]);
        }
		$type = $payorder['type'];

		if($type == 'shop_hb'){
			Db::name('shop_order')->where('ordernum','like',$payorder['ordernum'].'%')->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}elseif($type == 'balance'){
			Db::name('shop_order')->where('id',$payorder['orderid'])->update(['balance_pay_status'=>1,'balance_pay_orderid'=>$orderid]);
			return ['status'=>1,'msg'=>''];
		}elseif($type == 'yuyue_balance'){
			Db::name('yuyue_order')->where('id',$payorder['orderid'])->update(['status'=>3,'balance_pay_status'=>1,'balance_pay_orderid'=>$orderid]);
		}elseif($type == 'yuyue_addmoney'){
			Db::name('yuyue_order')->where('id',$payorder['orderid'])->update(['addmoneyStatus'=>1]);
		}elseif($type == 'seckill2'){
			Db::name('seckill2_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
			$form_sale = Db::name('seckill2_order')->alias('or')->join('seckill2_product pro','or.proid = pro.id')->field('pro.saleid')->where('or.ordernum',$payorder['ordernum'])->find();
			$sale = Db::name('seckill2_sale')->where('id',$form_sale['saleid'])->find();
			Db::name('seckill2_order')->where('ordernum',$sale['form_ordernum'])->update(['status'=>10]);
		}elseif($type == 'form'){
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paystatus'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}elseif($type == 'plug_business_pay'){
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'restaurant_shop' && $paytypeid == 4){
            // Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type == 'workorder'){  //工单订单
            Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['paystatus'=>1,'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
        }elseif($type=='huodong_baoming') {
            $aid = $payorder['aid'];
            Db::name('huodong_baoming_order')->where('id', $payorder['orderid'])->update(['status' => 1, 'paytime' => time(), 'paytype' => $paytype, 'paytypeid' => $paytypeid, 'platform' => $payorder['platform']]);
            $order = Db::name('huodong_baoming_order')->where('id', $payorder['orderid'])->find();
            Db::name('huodong_baoming_product')->where('aid',$aid)->where('id',$order['proid'])->update(['sales'=>Db::raw("sales+".$order['num'])]);
            if ($order['givescore'] > 0) {
                \app\common\Member::addscore($aid,$order['mid'],$order['givescore'],'参与活动赠送'.t('积分'));
            }
            return ['status' => 1, 'msg' => ''];
        }elseif($type == 'yuyue_package'){  // 处理套餐订单特殊情况，使用pay_time而不是paytime
            // 套餐订单表使用pay_time字段而不是paytime
            Db::name($type.'_order')->where('id',$payorder['orderid'])->update([
                'status'=>1,
                'pay_time'=>time(),  // 注意：这里使用pay_time而不是paytime
                'paytype'=>$paytype,
                'paytypeid'=>$paytypeid,
                'paynum'=>$paynum,
                'platform'=>$payorder['platform']
            ]);
        }elseif ($type == 'periodicservice') {
            // 周期服务订单: 在此处直接更新核心状态
            $updateData = [
                'pay_status' => 1, // 支付状态: 已支付
                'status' => 1,     // 订单状态: 服务中 (或根据业务设置为其他初始状态，如 待排期)
                'pay_time' => time(),
                'pay_type' => $paytype,
                'paytypeid'=>$paytypeid,
                // 'pay_money' => $payMoney, // 记录支付金额
                // 'platform' => $platform,
                'platform'=>$payorder['platform'],
                'paynum' => $paynum,
                // 'paytypeid' => $paytypeid, // 如果 periodic_service_order 表也有此字段，则添加
            ];
           // var_dump($updateData);die;
            Db::name('periodicservice_order')
              ->where('aid', $payorder['aid']) // 添加 aid 条件确保安全
              ->where('id', $payorder['orderid'])
              ->update($updateData);
            \think\facade\Log::info("Payorder-周期服务订单状态更新成功-订单ID: " . $payorder['orderid']);
    
        }else{
			Db::name($type.'_order')->where('id',$payorder['orderid'])->update(['status'=>1,'paytime'=>time(),'paytype'=>$paytype,'paytypeid'=>$paytypeid,'paynum'=>$paynum,'platform'=>$payorder['platform']]);
		}

		if(getcustom('invite_free')){
        	if($payorder['type'] == 'shop'){
        		//发送订单通知
        		self::send_free_notice($payorder);
        	}
        }
		$fun = $type.'_pay';
		$res = self::$fun($payorder['orderid'],$payorder['ordernum']);
        self::afterusecoupon($payorder['orderid'],$type,1,$payorder['ordernum']);
		return ['status'=>1,'msg'=>''];
	}
	//商城订单合并支付
	public static function shop_hb_pay($orderid,$ordernum){
		$orderlist = Db::name('shop_order')->where('ordernum','like',$ordernum.'%')->select()->toArray();
		foreach($orderlist as $order){
			self::shop_pay($order['id']);
		}
	}
	//商城订单
// 商城订单
public static function shop_pay($orderid){
    $order = Db::name('shop_order')->where('id', $orderid)->find();
    $member = Db::name('member')->where('id', $order['mid'])->find();
    $aid = $order['aid'];
    $mid = $order['mid'];
    // var_dump($order);die;
      // 检查是否开启提货编号功能
      $sysset = Db::name('shop_sysset')->where('aid', $aid)->find();
      if($sysset && $sysset['enable_pickup_code'] == 1) {
          // 获取门店设置
          $mendian = Db::name('mendian')->where('id', $order['mdid'])->find();
          if($mendian) {
              // 获取门店的提货编号配置
              $prefix = !empty($mendian['code_prefix']) ? $mendian['code_prefix'] : 'A';
              $max_layer = !empty($mendian['code_max_layer']) ? $mendian['code_max_layer'] : 5;
              $max_number = !empty($mendian['code_max_number']) ? $mendian['code_max_number'] : 50;

              // 获取重置间隔小时数，默认24小时
              $reset_interval = !empty($sysset['pickup_code_reset_interval']) ? intval($sysset['pickup_code_reset_interval']) : 24;
              
              // 计算重置时间范围
              $current_timestamp = time();
              // 将小时转换为秒计算
              $hours_from_epoch = floor($current_timestamp / (3600 * $reset_interval));
              $period_start = $hours_from_epoch * (3600 * $reset_interval);
              $period_end = $period_start + (3600 * $reset_interval);

              // 获取当前时间段内已使用的编号
              $used_codes = Db::name('shop_order')
                  ->where('mdid', $order['mdid'])
                  ->where('pickup_code', '<>', '')
                  ->where('paytime', '>=', $period_start)
                  ->where('paytime', '<', $period_end)
                  ->column('pickup_code');

              // 生成新的编号
              for($layer = 1; $layer <= $max_layer; $layer++) {
                  for($number = 1; $number <= $max_number; $number++) {
                      $code = $prefix . '-' . $layer . '-' . $number;
                      if(!in_array($code, $used_codes)) {
                          // 更新订单的提货编号
                          Db::name('shop_order')->where('id', $orderid)->update([
                              'pickup_code' => $code
                          ]);
                          break 2;
                      }
                  }
              }
          }
      }
    // 扣除用户现金券
    if($order['scoredkscorehei'] > 0){
        // 减去会员的现金券
        \app\common\Member::addhei($aid, $mid, -$order['scoredkscorehei'], '支付订单,订单号: '.$order['ordernum']);
    }
    if($order['scoredkscoreyu'] > 0){
        // 减去会员的余额
        \app\common\Member::addmoney($aid, $mid, -$order['scoredkscoreyu'], '支付订单,订单号: '.$order['ordernum']);
    }
    
    // 返创业值
    $memberlevel = Db::name('member_level')->where('aid', $aid)->where('id', $member['levelid'])->find();
    if($memberlevel['zijifh'] == 1)
    {
        $zijifh_jiang = json_decode($memberlevel['zijifh_jiang'],1);
        
        if($order['totalprice'] >= $zijifh_jiang[3]['min'] &&  $zijifh_jiang[3]['min'] != 0)
        {
            $beishu = $zijifh_jiang[3]['beishu'];
        } elseif($order['totalprice'] >= $zijifh_jiang[2]['min'] && $order['totalprice'] < $zijifh_jiang[2]['max'])
        {
            $beishu = $zijifh_jiang[2]['beishu'];
        } elseif($order['totalprice'] >= $zijifh_jiang[1]['min'] && $order['totalprice'] < $zijifh_jiang[1]['max'])
        {
            $beishu = $zijifh_jiang[1]['beishu'];
        } elseif($order['totalprice'] >= $zijifh_jiang[0]['min'] && $order['totalprice'] < $zijifh_jiang[0]['max'])
        {
            $beishu = $zijifh_jiang[0]['beishu'];
        } else{
            $beishu = 0;
        }
        $jiangli = $order['totalprice'] * $beishu;
        if($jiangli > 0)
        {
            \app\common\Member::addzijifh($aid, $mid, $jiangli, '自己消费奖励'.$jiangli.',倍数为'.$beishu);
        }
    }
    
    if($memberlevel['is_bus_bound_multiples'] == 1){
        $cyz = $memberlevel['bus_bound_multiples'] * $order['totalprice'];
        \app\common\Member::addbusTotal($aid, $mid, $mid, $cyz, '购买产品赠送'.t('创业值'));
    }
    
    if($memberlevel['fxfhc'] == 1)
    {
        $zijifh_jiang = json_decode($memberlevel['fxfhc_jiang'],1);
        if($order['totalprice'] >= $zijifh_jiang[3]['min'] &&  $zijifh_jiang[3]['min'] != 0)
        {
            $beishu = $zijifh_jiang[3]['beishu'];
        } elseif($order['totalprice'] >= $zijifh_jiang[2]['min'] && $order['totalprice'] < $zijifh_jiang[2]['max'])
        {
            $beishu = $zijifh_jiang[2]['beishu'];
        } elseif($order['totalprice'] >= $zijifh_jiang[1]['min'] && $order['totalprice'] < $zijifh_jiang[1]['max'])
        {
            $beishu = $zijifh_jiang[1]['beishu'];
        } elseif($order['totalprice'] >= $zijifh_jiang[0]['min'] && $order['totalprice'] < $zijifh_jiang[0]['max'])
        {
            $beishu = $zijifh_jiang[0]['beishu'];
        } else{
            $beishu = 0;
        }
        $jiangli = $order['totalprice'] * $beishu;
        if($jiangli > 0)
        {
            \app\common\Member::addfxfhc($aid, $mid, $jiangli, '分享分红池奖励'.$jiangli.',倍数为'.$beishu);
        }
    }
    
    if($memberlevel['syc'] == 1)
    {
        $q = 0;
        $zijifh_jiang = json_decode($memberlevel['syc_jiang'],1);
        if($order['totalprice'] >= $zijifh_jiang[3]['min'] &&  $zijifh_jiang[3]['min'] != 0)
        {
            $beishu = $zijifh_jiang[3]['beishu'];
            $jiangli = $order['totalprice'] * $beishu;
        } elseif($order['totalprice'] >= $zijifh_jiang[2]['min'] && $order['totalprice'] < $zijifh_jiang[2]['max'])
        {
            $beishu = $zijifh_jiang[2]['beishu'];
            $jiangli = $order['totalprice'] * $beishu;
        } elseif($order['totalprice'] >= $zijifh_jiang[1]['min'] && $order['totalprice'] < $zijifh_jiang[1]['max'])
        {
            $beishu = $zijifh_jiang[1]['beishu'];
            $jiangli = $order['totalprice'] * $beishu;
        } elseif($order['totalprice'] >= $zijifh_jiang[0]['min'] && $order['totalprice'] < $zijifh_jiang[0]['max'])
        {
            $youwu = Db::name('member_syclog')->where('mid', $mid)->where('aid', $aid)->where('q', 1)->find();
            if(empty($youwu))
            {
                $beishu = $zijifh_jiang[0]['beishu'];
                $jiangli = $beishu;
                // 修改我的状态;
                $q = 1;
            } else{
                $jiangli = 0; 
            }
        } else{
            $beishu = 0;
            $jiangli = 0;
        }
        if($jiangli > 0)
        {
            \app\common\Member::addsyc($aid, $mid, $jiangli, '收益池奖励'.$jiangli.',倍数为'.$beishu, $q);
        }
    }
    
    // 下单后返现处理
    $oglist = Db::name('shop_order_goods')->where('aid',$aid)->where('orderid',$order['id'])->select()->toArray();
    //购物返现
    $cashbacklist = Db::name('cashback')
        ->where('aid',$aid)
        ->where('bid',$order['bid'])
        ->where('starttime','<',$order['paytime'])
        ->where('endtime','>',$order['paytime'])
        ->order('sort desc')->select()->toArray();

    //查询购买用户
    if($oglist && $member && $cashbacklist){
        foreach($oglist as $og){
            $product = Db::name('shop_product')
                ->where('id',$og['proid'])
                ->field('id,cid')
                ->find();

            if($product){
                foreach($cashbacklist as $v){
                    // 只处理下单后返现的活动
                    if($v['back_time'] != 2){
                        continue;
                    }

                    $gettj = explode(',',$v['gettj']);
                    if(!in_array('-1',$gettj) && !in_array($member['levelid'],$gettj)){ //不是所有人
                        continue;
                    }

                    if($v['fwtype']==2){//指定商品可用
                        $productids = explode(',',$v['productids']);
                        if(!in_array($product['id'],$productids)){
                            continue;
                        }
                    }

                    if($v['fwtype']==1){//指定类目可用
                        $categoryids = explode(',',$v['categoryids']);
                        $cids = explode(',',$product['cid']);
                        $clist = Db::name('shop_category')->where('pid','in',$categoryids)->select()->toArray();
                        foreach($clist as $vc){
                            $categoryids[] = $vc['id'];
                            $cate2 = Db::name('shop_category')->where('pid',$vc['id'])->find();
                            $categoryids[] = $cate2['id'];
                        }
                        if(!array_intersect($cids,$categoryids)){
                            continue;
                        }
                    }

                    //如果返现利率大于0
                    if($v['back_ratio']>0){
                        //根据奖励计算方式计算返现
                        $back_price = 0;
                        $calc_base = 0; // 计算基数
                        
                        // 获取商品的规格信息（包含成本价）
                        $guige = Db::name('shop_guige')->where('aid',$aid)->where('id',$og['ggid'])->find();
                        $cost_price = $guige ? $guige['cost_price'] : 0;
                        $sell_price = $og['sell_price']; // 商品销售价格
                        
                        // 根据奖励计算方式确定计算基数
                        $reward_calc_type = isset($v['reward_calc_type']) ? $v['reward_calc_type'] : 1;
                        switch($reward_calc_type){
                            case 1: // 按销售额百分比奖励
                                $calc_base = $og['real_totalprice'];
                                break;
                            case 2: // 按成本价奖励
                                $calc_base = $cost_price * $og['num'];
                                break;
                            case 3: // 按利润奖励
                                $profit_per_unit = $sell_price - $cost_price;
                                $calc_base = $profit_per_unit * $og['num'];
                                break;
                            default:
                                $calc_base = $og['real_totalprice']; // 默认按销售额
                        }
                        
                        // 计算返现金额
                        $back_price = $v['back_ratio'] * $calc_base / 100;
                        
                        // 记录计算详情日志
                        \think\facade\Log::write('购物返现计算详情:计算方式='.$reward_calc_type.',计算基数='.$calc_base.',返现比率='.$v['back_ratio'].'%,返现金额='.$back_price.',订单商品ID='.$og['id']);

                        //返现类型 1、余额 2、佣金 3、积分 4、贡献值
                        if($v['back_type'] == 1 || $v['back_type']== 2){
                            $back_price = round($back_price,2);
                        }

                        if($back_price>0){
                            if($v['back_type'] == 1 ){
                                \app\common\Member::addmoney($aid,$order['mid'],$back_price,$v['name'].'(下单后返现)');
                            }
                            if($v['back_type'] == 2){
                                \app\common\Member::addcommission($aid,$order['mid'],$order['mid'],$back_price,$v['name'].'(下单后返现)');
                            }
                            if($v['back_type'] == 3){
                                \app\common\Member::addscore($aid,$order['mid'],$back_price,$v['name'].'(下单后返现)');
                            }
                            if($v['back_type'] == 4){
                                \app\common\Member::addgongxianzhi($aid,$order['mid'],$back_price,$v['name'].'(下单后返现)',$order['id']);
                            }
                            if($v['back_type'] == 5){
                                \app\common\Member::addhuang($aid,$order['mid'],$back_price,$v['name'].'(下单后返现)',$order['id']);
                            }
                            if($v['back_type'] == 6){
                                \app\common\Member::addhei($aid,$order['mid'],$back_price,$v['name'].'(下单后返现)',$order['id']);
                            }
                            if($v['back_type'] == 7){
                                \app\common\Member::addduihuanfen($aid,$order['mid'],$back_price,$v['name'].'(下单后返现)',$order['id']);
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 给黑积分上级 $member['pid']
    if($memberlevel['tjmoney'] > 0)
    {
        if($member['jiangcount'] < $memberlevel['tjmoney'])
        {
            $dedao = 0;
            if($order['totalprice'] >= $memberlevel['tjmoney'])
            {
                $dedao = $memberlevel['tjmoney'] - $member['jiangcount'];
            } else{
                // 消��� 2000 设置 3000
                $quezhexie = $memberlevel['tjmoney'] - $member['jiangcount'];
                if($quezhexie >= $order['totalprice'])
                {
                    $dedao = $order['totalprice'];
                } else{
                    $dedao = $quezhexie;
                }
            }
            if($dedao > 0)
            {
                \app\common\Member::addhei($aid, $member['pid'], $dedao, '推荐奖励金额累计'.$memberlevel['tjmoney']);
                // 更改拿到的钱数
                $myjiangcount = $member['jiangcount'] + $dedao;
                Db::name('member')->where('aid', $aid)->where('id', $member['id'])->update(['jiangcount' => $myjiangcount]);
            }
        }
    }
    
    //// 给用户添加贡献值 每消费多少元赠送多少贡献值
    // \app\common\Member::getAddContribution($aid, $mid, 3000, '订单消费送贡献值');
    // // 给用户添加红包 每消费多少元赠送多少红包
    // \app\common\Member::getAddhuangjifen($aid, $mid, $order['totalprice'], $payorder['ordernum'], '订单消费送金币');
    // // 给用户金币 每消费多少元赠送多少金币
    // \app\common\Member::getAddjinbi($aid, $mid, $order['totalprice'], $payorder['ordernum'], '订单消费送金币');
    
    // 根据区间返创业值
    $sysset = Db::name('admin_set')->where('aid', $aid)->find();
    $cyz_qu = json_decode($sysset['cyz_qu'],1);
    $myzonge = $order['product_price'];
    if($sysset['cyz_shi']){
        if($myzonge >= $cyz_qu[6]['min'])
        {
            $jiang = $cyz_qu[6]['jiang'];
        } elseif($myzonge >= $cyz_qu[5]['min'] && $myzonge <= $cyz_qu[5]['max'])
        {
            $jiang = $cyz_qu[5]['jiang'];
        } elseif($myzonge >= $cyz_qu[4]['min'] && $myzonge <= $cyz_qu[4]['max'])
        {
            $jiang = $cyz_qu[4]['jiang'];
        } elseif($myzonge >= $cyz_qu[3]['min'] && $myzonge <= $cyz_qu[3]['max'])
        {
            $jiang = $cyz_qu[3]['jiang'];
        } elseif($myzonge >= $cyz_qu[2]['min'] && $myzonge <= $cyz_qu[2]['max'])
        {
            $jiang = $cyz_qu[2]['jiang'];
        } elseif($myzonge >= $cyz_qu[1]['min'] && $myzonge <= $cyz_qu[1]['max'])
        {
            $jiang = $cyz_qu[1]['jiang'];
        } else{
            $jiang = 0;
        }
        if($jiang > 0)
        {
            $jiangli = $myzonge * $jiang;
            \app\common\Member::addbusTotal($aid, $mid, $mid, $jiangli, '购买产品赠送区间'.t('创业值'));
        }
    }
    
    $oglist = Db::name('shop_order_goods')->where('orderid', $orderid)->select()->toArray();
    
            foreach ($oglist as $og) {
                    // 处理 parent1 的越级奖励
                    if ($og['parent1'] > 0) {
                        $parent1 = Db::name('member')->where('id', $og['parent1'])->find();
                        $parent1_level = Db::name('member_level')->where('aid', $aid)->where('id', $parent1['levelid'])->find();
            
                        if ($parent1_level && isset($parent1_level['upgrade_level_ids']) && isset($parent1_level['upgrade_reward'])) {
                            $upgrade_level_ids = explode(',', $parent1_level['upgrade_level_ids']);
                            $upgrade_reward = $parent1_level['upgrade_reward'] / 100; // 将百分比转换为小数
            
                            if (in_array($member['levelid'], $upgrade_level_ids)) {
                                $jiangli_parent1 = $order['totalprice'] * $upgrade_reward; // 使用 totalprice 计算奖励
            
                                // 调用 addcommission 方法发放奖励到佣金
                                \app\common\Member::addcommission($aid, $og['parent1'], $mid, $jiangli_parent1, '上级奖励单号: '.$order['ordernum']);
                            }
                        }
                    }
                }
    if($sysset['cyz_shi']){
        foreach($oglist as $og)
        {
            
            if($og['parent1'] > 0){
                $parent1 = Db::name('member')->where('id', $og['parent1'])->find();
                if($parent1['bus_total'] >= $og['parent1commission'])
                {
                    // 扣除创业值
                    \app\common\Member::addbusTotal($aid, $og['parent1'], $og['parent1'], '-'.$og['parent1commission'], '佣金扣除'.t('创业值'));
                } else{
                    // 把佣金改成0
                    $upcommission = $parent1['bus_total'];
                    \app\common\Member::addbusTotal($aid, $og['parent1'], $og['parent1'], '-'.$upcommission, '佣金扣除'.t('创业值'));
                    Db::name('member_commission_record')->where('jl_type',1)->where('ogid', $og['id'])->where('mid', $og['parent1'])->update(['commission' => $upcommission]);
                }
            }
            if($og['parent2'] > 0){
                $parent2 = Db::name('member')->where('id', $og['parent2'])->find();
                if($parent2['bus_total'] >= $og['parent2commission'])
                {
                    // 扣���创业值
                    \app\common\Member::addbusTotal($aid, $og['parent2'], $og['parent2'], '-'.$og['parent2commission'], '佣金扣除'.t('创业值'));
                } else{
                    // 把佣金改成0
                    $upcommission = $parent2['bus_total'];
                    \app\common\Member::addbusTotal($aid, $og['parent2'], $og['parent2'], '-'.$upcommission, '佣金扣除'.t('创业值'));
                    Db::name('member_commission_record')->where('jl_type',1)->where('ogid', $og['id'])->where('mid', $og['parent2'])->update(['commission' => $upcommission]);
                }
            }
            if($og['parent3'] > 0){
                $parent3 = Db::name('member')->where('id', $og['parent3'])->find();
                if($parent3['bus_total'] >= $og['parent3commission'])
                {
                    // 扣除创业值
                    \app\common\Member::addbusTotal($aid, $og['parent3'], $og['parent3'], '-'.$og['parent3commission'], '佣金扣除'.t('创业值'));
                } else{
                    // 把佣金改成0
                    $upcommission = $parent3['bus_total'];
                    \app\common\Member::addbusTotal($aid, $og['parent3'], $og['parent3'], '-'.$upcommission, '佣金扣除'.t('创业值'));
                    Db::name('member_commission_record')->where('jl_type',1)->where('ogid', $og['id'])->where('mid', $og['parent3'])->update(['commission' => $upcommission]);
                }
            }
            // var_dump($parent1);die;
        }
    }

    	//去排队
    	foreach($oglist as $og)
    	{
    	    if($og['paidui_jiang'] >0 || $og['paidui_give'] >0)
    	    {
    	    	$type_status = 0;
    	        $type = 0;
    	       // if($og['bid'] >0)
    	       // {
    	            $buss = Db::name('paidui_maidan_set')->where('aid',aid)->where('bid',$og['bid'])->find();
    	            if(!empty($buss))
    	            {
          	            $type_status = $buss['type_status'];
          	            $type = $buss['type'];  
    	            }
    	       // }
    	        if($type_status == 1 || $og['bid'] == 0)
    	        {
    	           // $buss = Db::name('paidui_maidan_set')->where('aid',aid)->where('bid',0)->find();
        	        $paiduidata = [
        	            'aid'=>$og['aid'],
        	            'mid'=>$og['mid'],
        	            'bid'=>$og['bid'],
        	            'type_status'=>$type_status,
        	            'type'=>$type,
        	            'orderid'=>$og['orderid'],
        	            'ordergoodid'=>$og['id'],
        	            'paidui_jiang'=>$og['paidui_jiang'],
        	            'paidui_give'=>$og['paidui_give'],
        	            'give_shengyu'=>$og['paidui_jiang'],
        	            'shiji_dedao'=>0,
        	            'createtime'=>time(),
        	            'status'=>0,
        	            ];

        	        $paiduiId = Db::name('paidui_list')->insertGetId($paiduidata);

        	        // 检查是否启用分期模式，如果启用则创建分期记录
        	        if($paiduiId && \app\common\PaiduiPeriods::isPeriodsEnabled($og['aid'])) {
        	            $periods_result = \app\common\PaiduiPeriods::createPeriods(
        	                $og['aid'], 
        	                $paiduiId, 
        	                $og['mid'], 
        	                $og['bid'], 
        	                $og['paidui_jiang']
        	            );
        	            
        	            if($periods_result['status'] == 1) {
        	                \think\facade\Log::write("排队分期创建成功: 排队ID={$paiduiId}, 分期数={$periods_result['periods']}, 总金额={$periods_result['total_amount']}");
        	            } else {
        	                \think\facade\Log::error("排队分期创建失败: 排队ID={$paiduiId}, 错误信息={$periods_result['msg']}");
        	            }
        	        }

        	        if($og['bid'] == 0)
        	        	$buss = Db::name('paidui_maidan_set')->where('aid',aid)->where('bid', 0)->find();

        	        if($buss['model'] == 1){
        	            
        	        	\app\common\Member::givepaiduijiangModel($order['aid'],$order['mid'],$paiduiId,$order['ordernum'], $buss);
        	        }
        	        else{
    	    	        //排队给奖励
    	    	      
    	    	        \app\common\Member::givepaiduijiang($og['aid'],$og['mid'],$og['orderid'],$og['id'],$og['ordernum']);
    	    	    }
    	        }
    	    }
    	}

    // 逢单奖励时更新奖金数据
    if(getcustom('fengdanjiangli')){
        $oglist = Db::name('shop_order_goods')->where('orderid', $orderid)->select()->toArray();
        foreach($oglist as $og){
            $member = Db::name('member')->where('id', $order['mid'])->find();
            $newbus_total = $member['bus_total'];
            $product = Db::name('shop_product')->where('id', $og['proid'])->find();
            if($product['fengdanjiangli'] && $product['commissionset'] == 2){
                $commissiondata = json_decode($product['commissiondata2'],true);
                $fengdanjiangliArr = explode(',', $product['fengdanjiangli']);
                $num = $og['num'];
                $ogupdate = [];
                
                if($og['parent1']){
                    $parent1 = Db::name('member')->where('id', $og['parent1'])->find();
                    $agleveldata1 = Db::name('member_level')->where('aid', $aid)->where('id', $parent1['levelid'])->find();
                    $dannum = Db::name('shop_order_goods')->where('aid', $aid)->where('proid', $product['id'])->where('status','in','1,2,3')->where("parent1", $og['parent1'])->sum('num');

                    $ogupdate['parent1commission'] = 0;
                    for($i = 0; $i < $num; $i++){
                        $thisdannum = ($dannum + 1 + $i) % 10;
                        if(in_array($thisdannum.'', $fengdanjiangliArr)){
                            $ogupdate['parent1commission'] += $commissiondata[$agleveldata1['id']]['commission1'];
                            $ogupdate['isfenhong'] = 2;
                        }
                    }
                    $ogupdate['fhparent2commission'] = 0;
                    $ogupdate['fhparent3commission'] = 0;
                    $ogupdate['fhparent4commission'] = 0;

                    if($parent1['pid'] > 0){
                        $parent2 = Db::name('member')->where('id', $parent1['pid'])->find();
                        if($parent2 && $ogupdate['parent1commission'] > 0){
                            $agleveldata2 = Db::name('member_level')->where('aid', $aid)->where('id', $parent2['levelid'])->find();
                            $ogupdate['fhparent2commission'] = $ogupdate['parent1commission'] * $agleveldata2['fh_commission2'] / 100;
                            if($parent2['pid'] > 0){
                                $parent3 = Db::name('member')->where('id', $parent2['pid'])->find();
                                if($parent3 && $ogupdate['parent1commission'] > 0){
                                    $agleveldata3 = Db::name('member_level')->where('aid', $aid)->where('id', $parent3['levelid'])->find();
                                    $ogupdate['fhparent3commission'] = $ogupdate['parent1commission'] * $agleveldata3['fh_commission3'] / 100;
                                }
                                if($parent3['pid'] > 0){
                                    $parent4 = Db::name('member')->where('id', $parent3['pid'])->find();
                                    if($parent4 && $ogupdate['parent1commission'] > 0){
                                        $agleveldata4 = Db::name('member_level')->where('aid', $aid)->where('id', $parent4['levelid'])->find();
                                        $ogupdate['fhparent4commission'] = $ogupdate['parent1commission'] * $agleveldata4['fh_commission4'] / 100;
                                    }
                                }
                            }
                        }
                    }
                }
                if($og['parent2']){
                    $parent2 = Db::name('member')->where('id', $og['parent2'])->find();
                    $agleveldata2 = Db::name('member_level')->where('aid', $aid)->where('id', $parent2['levelid'])->find();
                    $dannum = Db::name('shop_order_goods')->where('aid', $aid)->where('proid', $product['id'])->where('status','in','1,2,3')->where("parent2", $og['parent2'])->sum('num');
                    $ogupdate['parent2commission'] = 0;
                    for($i = 0; $i < $num; $i++){
                        $thisdannum = ($dannum + 1 + $i) % 10;
                        if(in_array($thisdannum.'', $fengdanjiangliArr)){
                            $ogupdate['parent2commission'] += $commissiondata[$agleveldata2['id']]['commission2'];
                            $ogupdate['isfenhong'] = 2;
                        }
                    }
                }
                if($og['parent3']){
                    $parent3 = Db::name('member')->where('id', $og['parent3'])->find();
                    $agleveldata3 = Db::name('member_level')->where('aid', $aid)->where('id', $parent3['levelid'])->find();
                    $dannum = Db::name('shop_order_goods')->where('aid', $aid)->where('proid', $product['id'])->where('status','in','1,2,3')->where("parent3", $og['parent3'])->sum('num');
                    $ogupdate['parent3commission'] = 0;
                    for($i = 0; $i < $num; $i++){
                        $thisdannum = ($dannum + 1 + $i) % 10;
                        if(in_array($thisdannum.'', $fengdanjiangliArr)){
                            $ogupdate['parent3commission'] += $commissiondata[$agleveldata3['id']]['commission3'];
                            $ogupdate['isfenhong'] = 2;
                        }
                    }
                    if(!empty($parent3['pid'])){
                        $parent4 = Db::name('member')->where('id', $parent3['pid'])->find();
                        if($parent4 && $ogupdate['parent1commission'] > 0){
                            $ogupdate['fhparent4commission'] = $ogupdate['parent1commission'] * $agleveldata4['fh_commission4'] / 100;
                        }
                    }
                }

                if($ogupdate){
                    $comzong = $ogupdate['parent1commission'] + $ogupdate['parent2commission'] + $ogupdate['parent3commission'];
                    if($comzong <= $newbus_total){
                        Db::name('shop_order_goods')->where('id', $og['id'])->update($ogupdate);
                        if($og['parent1'] && $ogupdate['parent1commission']){
                            Db::name('member_commission_record')->insert([
                                'aid' => $aid,
                                'mid' => $og['parent1'],
                                'frommid' => $og['mid'],
                                'orderid' => $orderid,
                                'ogid' => $og['id'],
                                'type' => 'shop',
                                'commission' => $ogupdate['parent1commission'],
                                'remark' => '下级购买商品奖励',
                                'createtime' => time()
                            ]);
                        }
                        if($og['parent2'] && $ogupdate['parent2commission']){
                            Db::name('member_commission_record')->insert([
                                'aid' => $aid,
                                'mid' => $og['parent2'],
                                'frommid' => $og['mid'],
                                'orderid' => $orderid,
                                'ogid' => $og['id'],
                                'type' => 'shop',
                                'commission' => $ogupdate['parent2commission'],
                                'remark' => '下二级购买商品奖励',
                                'createtime' => time()
                            ]);
                        }
                        if($og['parent3'] && $ogupdate['parent3commission']){
                            Db::name('member_commission_record')->insert([
                                'aid' => $aid,
                                'mid' => $og['parent3'],
                                'frommid' => $og['mid'],
                                'orderid' => $orderid,
                                'ogid' => $og['id'],
                                'type' => 'shop',
                                'commission' => $ogupdate['parent3commission'],
                                'remark' => '下三级购买商品奖励',
                                'createtime' => time()
                            ]);
                        }
                        if($parent2 && $ogupdate['fhparent2commission'] > 0){
                            Db::name('member_commission_record')->insert([
                                'aid' => $aid,
                                'jl_type' => 2,
                                'mid' => $parent2['id'],
                                'frommid' => $og['mid'],
                                'orderid' => $orderid,
                                'ogid' => $og['id'],
                                'type' => 'shop',
                                'commission' => $ogupdate['fhparent2commission'],
                                'remark' => '下一级收入分红',
                                'createtime' => time()
                            ]);
                        }
                        if($parent3 && $ogupdate['fhparent3commission'] > 0){
                            Db::name('member_commission_record')->insert([
                                'aid' => $aid,
                                'jl_type' => 2,
                                'mid' => $parent3['id'],
                                'frommid' => $og['mid'],
                                'orderid' => $orderid,
                                'ogid' => $og['id'],
                                'type' => 'shop',
                                'commission' => $ogupdate['fhparent3commission'],
                                'remark' => '下二级收入分红',
                                'createtime' => time()
                            ]);
                        }
                        if($parent4 && $ogupdate['fhparent4commission'] > 0){
                            Db::name('member_commission_record')->insert([
                                'aid' => $aid,
                                'jl_type' => 2,
                                'mid' => $parent4['id'],
                                'frommid' => $og['mid'],
                                'orderid' => $orderid,
                                'ogid' => $og['id'],
                                'type' => 'shop',
                                'commission' => $ogupdate['fhparent4commission'],
                                'remark' => '下三级收入分红',
                                'createtime' => time()
                            ]);
                        }
                        if($memberlevel['is_bus_bound_multiples'] == 1){
                            \app\common\Member::addbusTotal($aid, $mid, $mid, '-'.$comzong, '奖励佣金扣除'.t('创业值'));
                        }
                    } else{
                        if($memberlevel['is_bus_bound_multiples'] == 0){
                            Db::name('shop_order_goods')->where('id', $og['id'])->update($ogupdate);
                            if($og['parent1'] && $ogupdate['parent1commission']){
                                Db::name('member_commission_record')->insert([
                                    'aid' => $aid,
                                    'mid' => $og['parent1'],
                                    'frommid' => $og['mid'],
                                    'orderid' => $orderid,
                                    'ogid' => $og['id'],
                                    'type' => 'shop',
                                    'commission' => $ogupdate['parent1commission'],
                                    'remark' => '下级购买商品奖励',
                                    'createtime' => time()
                                ]);
                            }
                            if($og['parent2'] && $ogupdate['parent2commission']){
                                Db::name('member_commission_record')->insert([
                                    'aid' => $aid,
                                    'mid' => $og['parent2'],
                                    'frommid' => $og['mid'],
                                    'orderid' => $orderid,
                                    'ogid' => $og['id'],
                                    'type' => 'shop',
                                    'commission' => $ogupdate['parent2commission'],
                                    'remark' => '下二级购买商品奖励',
                                    'createtime' => time()
                                ]);
                            }
                            if($og['parent3'] && $ogupdate['parent3commission']){
                                Db::name('member_commission_record')->insert([
                                    'aid' => $aid,
                                    'mid' => $og['parent3'],
                                    'frommid' => $og['mid'],
                                    'orderid' => $orderid,
                                    'ogid' => $og['id'],
                                    'type' => 'shop',
                                    'commission' => $ogupdate['parent3commission'],
                                    'remark' => '下三级购买商品奖励',
                                    'createtime' => time()
                                ]);
                            }
                            if($parent2 && $ogupdate['fhparent2commission'] > 0){
                                Db::name('member_commission_record')->insert([
                                    'aid' => $aid,
                                    'jl_type' => 2,
                                    'mid' => $parent2['id'],
                                    'frommid' => $og['mid'],
                                    'orderid' => $orderid,
                                    'ogid' => $og['id'],
                                    'type' => 'shop',
                                    'commission' => $ogupdate['fhparent2commission'],
                                    'remark' => '下一级收入分红',
                                    'createtime' => time()
                                ]);
                            }
                            if($parent3 && $ogupdate['fhparent3commission'] > 0){
                                Db::name('member_commission_record')->insert([
                                    'aid' => $aid,
                                    'jl_type' => 2,
                                    'mid' => $parent3['id'],
                                    'frommid' => $og['mid'],
                                    'orderid' => $orderid,
                                    'ogid' => $og['id'],
                                    'type' => 'shop',
                                    'commission' => $ogupdate['fhparent3commission'],
                                    'remark' => '下二级收入分红',
                                    'createtime' => time()
                                ]);
                            }
                            if($parent4 && $ogupdate['fhparent4commission'] > 0){
                                Db::name('member_commission_record')->insert([
                                    'aid' => $aid,
                                    'jl_type' => 2,
                                    'mid' => $parent4['id'],
                                    'frommid' => $og['mid'],
                                    'orderid' => $orderid,
                                    'ogid' => $og['id'],
                                    'type' => 'shop',
                                    'commission' => $ogupdate['fhparent4commission'],
                                    'remark' => '下三级收入分红',
                                    'createtime' => time()
                                ]);
                            }
                        }
                    }
                }
            }
        }
    }

    if(getcustom('to86yk')){
        $oglist = Db::name('shop_order_goods')->where('orderid', $orderid)->select()->toArray();
        foreach($oglist as $og){
            if($og['to86yk_tid']){
                $shopset = Db::name('shop_sysset')->where('aid', $aid)->find();
                $formdata = \app\model\Freight::getformdata($orderid, 'shop_order');
                $successnum = 0;
                for($i = 0; $i < $og['num']; $i++){
                    $url = 'http://api.yukawl.cn/api.php?act=pay&tid='.$og['to86yk_tid'].'&input1='.$formdata[0][1].'&user='.$shopset['to86yk_user'].'&pass='.$shopset['to86yk_pwd'];
                    $rs = request_get($url);
                    // \think\facade\Log::write($url);
                 
                    $rs = json_decode($rs, true);
                  
                    if($rs && $rs['code'] == 0) $successnum++;
                }
                Db::name('shop_order_goods')->where('id', $og['id'])->update(['to86yk_successnum' => $successnum]);
            }
        }
    }

    // 处理BPV分红
    try {
        // 调用BpvProfit类处理订单分红
        \app\common\BpvProfit::handleOrderProfit($aid, $orderid, $order['ordernum'], $order['totalprice']);
    } catch (\Exception $e) {
        // 记录异常，但不影响订单正常流程
        \think\facade\Log::write('BPV分红处理异常：'.$e->getMessage(), 'error');
    }

    Db::name('shop_order_goods')->where('orderid', $orderid)->update(['status' => 1]);

    if($order['fromwxvideo'] == 1){
        if($order['paytypeid'] != 60){ // 不是视频号微信支付的
            Db::name('shop_order')->where('id', $orderid)->update(['fromwxvideo' => 0]);
            $order['fromwxvideo'] = 0;
            // \app\common\Wxvideo::createorder($orderid);
        } else{
            \app\common\Wxvideo::orderpay($orderid);
        }
    }

    // 判断是否开启云库存,开启云库存的,查我的等级可不可以存货,添加我的云库存,然后订单完成
    $ykcrs = Db::name('yunkucun_sysset')->where('aid', $aid)->find();
    if(!empty($ykcrs))
    {
        if($ykcrs['status'] == 1)
        {
            if($ykcrs['zouhuo'] == 1 && $member['pid'] > 0 && $order['ti'] == 3)
            {
                $giveArr = json_decode($order['nahuoArr'],1);
                $oglist = Db::name('shop_order_goods')->where('orderid', $orderid)->select()->toArray();
                $goodssttaus = \app\common\Member::isyunproduct($aid, $oglist[0]['proid']);
                if($goodssttaus){
                    foreach($oglist as $og){
                        $product = Db::name('shop_product')->where('id', $og['proid'])->find();
                        $guige = Db::name('shop_guige')->where('id', $og['ggid'])->find();
                        if(!empty($giveArr) && count($giveArr) > 1 && $product['kuncun_type'] != 1)
                        {
                            $keys = array_keys($giveArr);
                            $keynum = 0;
                            $num = $og['num'];
                            foreach($giveArr as $kk1 => $vv1)
                            {
                                if($kk1)
                                {
                                    if($order['nahuoid'] != $kk1)
                                    {
                                        $curmembernahuo = Db::name('member')->where('aid', $aid)->where('id', $kk1)->find();
                                        $curmemberlevelnahuo = Db::name('member_level')->where('aid', $aid)->where('id', $curmembernahuo['levelid'])->find();
                                        
                                        // 计算我的拿货价格
                                        if(empty($guige['lvprice_data']))
                                        {
                                            $nahuoprice = $guige['sell_price'];
                                        } else{
                                            $lvprice_data = json_decode($guige['lvprice_data'],1);
                                            $nahuoprice = $lvprice_data[$curmembernahuo['levelid']];
                                        }
                                        if($curmemberlevelnahuo['commissiontype'] == 1)
                                        {
                                            $commission1nahuo = $curmemberlevelnahuo['commission1'];
                                        } else{
                                            $commission1nahuo = $nahuoprice * $num * $curmemberlevelnahuo['commission1'] * 0.01; 
                                        }
                                        $nahuodata = [
                                            'orderid' => $orderid,
                                            'aid' => $aid,
                                            'mid' => $kk1,
                                            'nahuoid' => isset($keys[$keynum + 1]) ? $keys[$keynum + 1] : 0,
                                            'proid' => $product['id'],
                                            'ggid' => $guige['id'],
                                            'proname' => $product['name'],
                                            'ggname' => $guige['name'],
                                            'pic' => $product['pic'],
                                            'num' => $num,
                                            'price' => $nahuoprice * $num,
                                            'pid' => $curmembernahuo['pid'],
                                            'pidprice' => $commission1nahuo,
                                            'ylevelid' => $curmembernahuo['levelid'],
                                            'createtime' => time(),
                                        ];
                                        Db::name('yun_nahuo')->insert($nahuodata);
                                        // 给直推奖和培养奖
                                        Db::name('member_commission_record')->insert([
                                            'aid' => $aid,
                                            'mid' => $curmembernahuo['pid'],
                                            'frommid' => $kk1,
                                            'orderid' => $orderid,
                                            'ogid' => 0,
                                            'type' => 'shop',
                                            'commission' => $commission1nahuo,
                                            'remark' => '用户:'.$kk1.'补货-下级购买商品奖励,订单编号:'.$order['ordernum'],
                                            'createtime' => time()
                                        ]);
                                        \app\common\Member::givepeiyang($aid, $nahuoprice * $num, $kk1, $orderid, $curmembernahuo['levelid'], 1);
                                        $keynum = $keynum + 1;
                                    } else{
                                        break;
                                    }
                                }
                            }
                        }
                    } 
                    if($member['pid'] > 0 && $order['ti'] == 2 && $ykcrs['zouhuo'] == 0)
                    {
                        if($order['zouhuoshouxu'] == 0)
                        {
                            $shouxufei = 0;
                            $totalprice_daozhang = $order['totalprice'];
                        } else{
                            $shouxufei = $order['totalprice'] * $order['zouhuoshouxu'] * 0.01; 
                            $totalprice_daozhang = $order['totalprice'] - $shouxufei;
                        }
                         
                        \app\common\Member::addcommission($aid, $member['pid'], $mid, $totalprice_daozhang, '下级下单我的云库存商品,订单编号:'.$order['ordernum'].',应到账:'.$order['totalprice'].',实际到账:'.$totalprice_daozhang.',扣除手续费:'.$shouxufei);
                    } elseif($ykcrs['zouhuo'] == 1 && $member['pid'] > 0 && $order['ti'] == 3)
                    {
                        $giveArr = json_decode($order['nahuoArr'],1);
                        $keys = array_keys($giveArr);
                        $keynums = 0;
                        foreach ($giveArr as $k => $v)
                        {
                            $peiyang = 0;
                            // 新改
                            if($keynums == 0)
                            {
                                // 查订单的
                                $zhitui = 0 + Db::name('shop_order_goods')->where('aid', $aid)->where('mid', $order['mid'])->where('orderid', $orderid)->sum('parent1commission');
                                foreach($oglist as $og){
                                    $product = Db::name('shop_product')->where('id', $og['proid'])->where('aid', $aid)->find();
                                    if($product['kuncun_type'] != 1)
                                    {
                                        $peiyangog = \app\common\Member::givepeiyang2($aid, $og['sell_price'], $order['mid'], $order['id'], $order['ylevelid'], 1);
                                        $peiyang = $peiyang + $peiyangog;
                                    }
                                }
                            } else{
                                // 查补货的
                                $xiajiid = $keys[$keynums - 1];
                                $zhitui = 0 + Db::name('yun_nahuo')->where('aid', $aid)->where('nahuoid', $k)->where('orderid', $orderid)->sum('pidprice');
                                $buhuoorder = Db::name('yun_nahuo')->where('aid', $aid)->where('nahuoid', $k)->where('orderid', $orderid)->find();
                                $buhuoorder2  =  Db::name('yun_nahuo')->where('aid', $aid)->where('nahuoid', $k)->where('orderid', $orderid)->select()->toArray();
                                foreach($buhuoorder2 as $buhuoorder){
                                    $peiyangog = \app\common\Member::givepeiyang2($aid, $buhuoorder['price'], $xiajiid, $order['id'], $buhuoorder['ylevelid'], 1);
                                    $peiyang = $peiyang + $peiyangog;
                                }
                            }
                            $keynums = $keynums + 1;
                            // 新改结束
                            $tuijian = $peiyang + $zhitui;
                            $yingdao = $v - $tuijian;
                            // 给佣金
                            if($yingdao > 0){
                                \app\common\Member::addcommission($aid, $k, $mid, $yingdao, '下级下单云库存商品,订单编号:'.$order['ordernum'].',应到账:'.$v.',实际到账:'.$yingdao.',扣除培养奖和推荐奖:'.$tuijian);
                            }
                        }
                    }
                }
                // 给我的上级返钱
                \app\common\Member::givepeiyang($aid, $order['totalprice'], $mid, $orderid, $member['levelid']);
            }
            $goodssttaus = \app\common\Member::isyunproduct($aid, $oglist[0]['proid']);
            $levelArr = Db::name('member_level')->where('aid', $aid)->where('id', $member['levelid'])->find();
            if($levelArr['can_cunhuo'] == 1 && $goodssttaus)
            {
                // 添加我的云库存 ddwx_users_yun
                foreach($oglist as $ordergoods)
                {
                    \app\common\Member::yunkucunlog($aid, $mid, $ordergoods['proid'], $ordergoods['ggid'], $ordergoods['num'], $ordergoods, '用户自己下单购买');
                    // 添加我的当前商品捆绑商品
                    // $product = Db::name('shop_product')->where('id', $ordergoods['proid'])->find();
                    // if(!empty($product['dddata']))
                    // {
                    //     $dddata = json_decode($product['dddata'],1);
                    //     foreach ($dddata as $kgoods => $vgoods)
                    //     {
                    //         $productgood = Db::name('shop_product')->where('id', $kgoods)->find();
                    //         $gg = Db::name('shop_guige')->where('id', $vgoods['ggid'])->find();
                    //         $ordergoods2 =[
                    //             'aid' => $ordergoods['aid'],
                    //             'bid' => 0,
                    //             'mid' => $mid,
                    //             'proid' => $kgoods,
                    //             'ggid' => $vgoods['ggid'],
                    //             'ggname' => $gg['name'],
                    //             'name' => $productgood['name'],
                    //             'pic' => $productgood['pic'],
                    //             'procode' => '',
                    //             'num' => $vgoods['num'] * $ordergoods['num']
                    //         ];
                    //         $num1 = $vgoods['num'] * $ordergoods['num'];
                    //         \app\common\Member::yunkucunlog($aid, $mid, $kgoods, $vgoods['ggid'], $num1, $ordergoods2, '用户自己下单组合商品购买');
                    //     }
                    // }
                }
                // 然后订单完成
                \app\common\Order::collect($order);
                Db::name('shop_order')->where('id', $orderid)->update(['status' => 3, 'collect_time' => time(), 'send_time' => time()]);
                Db::name('shop_order_goods')->where('orderid', $orderid)->update(['status' => 3, 'endtime' => time()]);
            }
        }
    }

    if ($order['paytypeid'] == 41) { // 确保用户为月结用户
        // 调用 addarrears 方法更新欠费金额
        \app\common\Member::addarrears($aid, $order['mid'], $order['totalprice'], '订单号: ' . $order['ordernum'] . ' 月结支付');
        
        // 日志记录
      
    }

    // 自动发货
    if($order['freight_type'] == 3){
        $og = Db::name('shop_order_goods')->where('orderid', $order['id'])->find();
        $freight_content = Db::name('shop_product')->where('id', $og['proid'])->value('freightcontent');
        Db::name('shop_order')->where('id', $order['id'])->update(['freight_content' => $freight_content, 'status' => 2, 'send_time' => time()]);
        Db::name('shop_order_goods')->where('orderid', $order['id'])->update(['status' => 2]);

        if($order['fromwxvideo'] == 1){
            \app\common\Wxvideo::deliverysend($orderid);
        }
        // 发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'], $order);
        }
        if(getcustom('plug_zhiming')){
            \app\common\Order::collect($order);
            Db::name('shop_order')->where('id', $orderid)->update(['status' => 3, 'collect_time' => time()]);
            Db::name('shop_order_goods')->where('orderid', $orderid)->update(['status' => 3, 'endtime' => time()]);
        }
    }
    
    // 在线卡密
    if($order['freight_type'] == 4){
        $og = Db::name('shop_order_goods')->where('orderid', $order['id'])->find();
        $codelist = Db::name('shop_codelist')->where('proid', $og['proid'])->where('status', 0)->order('id')->limit($og['num'])->select()->toArray();
        if($codelist && count($codelist) >= $og['num']){
            $pscontent = [];
            foreach($codelist as $codeinfo){
                $pscontent[] = $codeinfo['content'];
                Db::name('shop_codelist')->where('id', $codeinfo['id'])->update([
                    'orderid' => $order['id'],
                    'ordernum' => $order['ordernum'],
                    'headimg' => $member['headimg'],
                    'nickname' => $member['nickname'],
                    'buytime' => time(),
                    'status' => 1
                ]);
            }
            $pscontent = implode("\r\n", $pscontent);
            Db::name('shop_order')->where('id', $order['id'])->update(['freight_content' => $pscontent, 'status' => 2, 'send_time' => time()]);
            Db::name('shop_order_goods')->where('orderid', $order['id'])->update(['status' => 2]);
        }
        if($order['fromwxvideo'] == 1){
            \app\common\Wxvideo::deliverysend($orderid);
        }

        // 发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'], $order);
        }
        if(getcustom('plug_zhiming')){
            \app\common\Order::collect($order);
            Db::name('shop_order')->where('id', $orderid)->update(['status' => 3, 'collect_time' => time()]);
            Db::name('shop_order_goods')->where('orderid', $orderid)->update(['status' => 3, 'endtime' => time()]);
        }
    }
    
    if(getcustom('huawa') && $aid == 1){ // 定制1 订单对接 同步到花娃
        \app\custom\Huawa::api($order);
    }
    if(getcustom('cefang') && $aid == 2){ // 定制2 订单对接 同步到策方
        \app\custom\Cefang::api($order);
    }

    if(getcustom('hmy_yuyue')){  // 红蚂蚁定制同步到跑腿订单
        \app\custom\Yuyue::api($order);
    }

    // 支付后送券
    $couponlist = \app\common\Coupon::getpaygive($aid, $mid, 'shop', $order['totalprice'], $order['id']);
    if($couponlist){
        $proids = db('shop_order_goods')->where('orderid', '=', $order['id'])->column('proid');
        foreach($couponlist as $coupon){
            if($coupon['paygive'] == 1 && $coupon['paygive_minprice'] <= $order['totalprice'] && $coupon['paygive_maxprice'] >= $order['totalprice'] && in_array('shop', explode(',', $coupon['paygive_scene']))){
                \app\common\Coupon::send($aid, $mid, $coupon['id']);
            }
            if($coupon['buyprogive'] == 1){
                $coupon['buyproids'] = explode(',', $coupon['buyproids']);
                $coupon['buypro_give_num'] = explode(',', $coupon['buypro_give_num']);
                foreach($coupon['buyproids'] as $k => $proid) {
                    if(in_array($proid, $proids) && $coupon['buypro_give_num'][$k] > 0) {
                        for($i = 0; $i < $coupon['buypro_give_num'][$k]; $i++) {
                            \app\common\Coupon::send($aid, $mid, $coupon['id']);
                        }
                    }
                }
            }
        }
    }
    
    // 送积分
    if($order['givescore2'] > 0){
        \app\common\Member::addscore($aid, $order['mid'], $order['givescore2'], '购买产品赠送'.t('积分'));
    }
     
    // 处理满额赠送的资产奖励
    if (isset($order['give_assets']) && !empty($order['give_assets'])) {
        // 解析JSON数据
       // var_dump($order['give_assets']);die;
        $give_assets = is_array($order['give_assets']) ? $order['give_assets'] : json_decode($order['give_assets'], true);
        
        // 处理各种资产赠送
        if (isset($give_assets['jifen']) && $give_assets['jifen'] > 0) {
            \app\common\Member::addscore($aid, $order['mid'], $give_assets['jifen'], '满额赠送'.t('积分'));
        }
        
        if (isset($give_assets['money']) && $give_assets['money'] > 0) {
            \app\common\Member::addmoney($aid, $order['mid'], $give_assets['money'], '满额赠送'.t('余额'));
        }
        
        if (isset($give_assets['commission']) && $give_assets['commission'] > 0) {
            \app\common\Member::addcommission($aid, $order['mid'], 0, $give_assets['commission'], '满额赠送'.t('佣金'));
        }
        
        if (isset($give_assets['contribution']) && $give_assets['contribution'] > 0) {
            \app\common\Member::addgongxianzhi($aid, $order['mid'], $give_assets['contribution'], '满额赠送'.t('贡献值'));
        }
        
        if (isset($give_assets['yellow_points']) && $give_assets['yellow_points'] > 0) {
            \app\common\Member::addscorehuang($aid, $order['mid'], $give_assets['yellow_points'], '满额赠送'.t('黄积分'));
        }
        
        if (isset($give_assets['cash_coupon']) && $give_assets['cash_coupon'] > 0) {
            \app\common\Member::addhei($aid, $order['mid'], $give_assets['cash_coupon'], '满额赠送'.t('现金券'));
        }
        
        // 记录日志
     
        
    }

    $ress = Db::name('shop_order_goods')->where('orderid', $orderid)->select()->toArray();
    // 送积分2  $order
    $jifen2info = Db::name('jifechi')->where('aid', $aid)->find();
    if($jifen2info['jfc_status'] == 1){
        $jing = 0;
        $dong = 0;
        $totalprice = 0;
        foreach($ress as $v)
        {
            $product = Db::name('shop_product')->where('id', $v['proid'])->field('id,zeng_jingtai,zeng_dongtai')->find();
            if($product['zeng_jingtai'] > 0 || $product['zeng_dongtai'] > 0)
            {
                $jing = $jing + $v['totalprice'] * $product['zeng_jingtai'] * 0.01;
                $dong = $dong + $v['totalprice'] * $product['zeng_dongtai'] * 0.01;
            } elseif($jifen2info['fanbeishu'] > 0){
                $totalprice = $totalprice + $v['totalprice'];
            }
        }
        // 我的下单金额大于等于设置的消费金额 返积分
        if($totalprice > 0 && $totalprice >= $jifen2info['xiaofeimoney'])
        {
            $jifen2 = $totalprice * $jifen2info['fanbeishu']; // 要返的积分
            $jingA = $jifen2 * $jifen2info['baifenbi'] / 100;
            $jingB = $jifen2 * (100 - $jifen2info['baifenbi']) / 100;
            \app\common\Member::addscoreA($aid, $order['mid'], $jingA, '购买产品满'.$jifen2info['xiaofeimoney'].'赠送'.$jifen2info['fanbeishu'].'倍积分的'.$jifen2info['baifenbi'].'%的静态积分');
            \app\common\Member::addscoreB($aid, $order['mid'], $jingB, '购买产品满'.$jifen2info['xiaofeimoney'].'赠送'.$jifen2info['fanbeishu'].'倍积分的'.(100 - $jifen2info['baifenbi']).'%的动态积分');
        }
        if($jing > 0)
        {
           \app\common\Member::addscoreA($aid, $order['mid'], $jing, '购买产品赠送静态积分'); 
        }
        if($dong > 0)
        {
            \app\common\Member::addscoreB($aid, $order['mid'], $dong, '购买产品送动态积分');
            \app\common\Member::addshengjiscore($aid, $order['mid'], $dong, '购买产品送升级积分');
        }
        // 加速释放动态积分
        $xiadanmember = Db::name('member')->where('aid', $aid)->where('id', $order['mid'])->field('id,pid')->find(); // 下单用户信息  
        if($xiadanmember['pid'] > 0)
        {
            $jiazhimember = Db::name('member')->where('aid', $aid)->where('id', $xiadanmember['pid'])->field('id,pid,scoreB')->find(); // 直接加速用户信息  
            // 直接返
            $jiasumoney1 = $order['totalprice'] * $jifen2info['zhiB'] / 100;
            $jiasumoney1 = round($jiasumoney1, 2);
            $jiasumoney123 = 0;
            $jiasumoney123_hei = 0;
            if($jiazhimember['scoreB'] >= $jiasumoney1)
            {
                $jiasumoney12 = $jiasumoney1;
            } else{
                $jiasumoney12 = $jiazhimember['scoreB'];
            }
            if($jifen2info['dongtaiB_money'] > 0)
            {
                $jiasumoney123 = round($jiasumoney12 * $jifen2info['dongtaiB_money'] * 0.01, 2);
            }
            if($jifen2info['dongtaiB_hei'] > 0)
            {
                $jiasumoney123_hei = round($jiasumoney12 * $jifen2info['dongtaiB_hei'] * 0.01, 2);
            }
            // var_dump($jiasumoney123);die;
            // var_dump($jiasumoney123_hei);die;
            $day = date('Y-m-d'); // 添加日期变量定义
            $res1 = \app\common\Member::addscoreB($aid, $jiazhimember['id'], '-'.$jiasumoney12, $day.'分享加速释放减去动态积分:'.$jiasumoney12);
            if($jiasumoney123 > 0 ){
                \app\common\Member::addmoney($aid, $jiazhimember['id'], $jiasumoney123, $day.'分享加速释放加入金额'.$jiasumoney123);
            }
            if($jiasumoney123_hei > 0 ){
                \app\common\Member::addhei($aid, $jiazhimember['id'], $jiasumoney123_hei, $day.'分享加速释放加入'.t('现金券').$jiasumoney123_hei);
            }
            if($jiazhimember['pid'] > 0)
            {
                $jiajianjiemember = Db::name('member')->where('aid', $aid)->where('id', $jiazhimember['pid'])->field('id,pid,scoreB')->find(); // 间接加速用户信息
                // 间接返
                $jiasumoney2 = $order['totalprice'] * $jifen2info['jiaB'] / 100;
                $jiasumoney2 = round($jiasumoney2, 2);
                $jiasumoney21 = 0;
                $jiasumoney21_hei = 0;
                if($jiajianjiemember['scoreB'] >= $jiasumoney2)
                {
                    $jiasumoney22 = $jiasumoney2;
                } else{
                    $jiasumoney22 = $jiazhimember['scoreB'];
                }
                if($jifen2info['dongtaiB_money'] > 0)
                {
                    $jiasumoney21 = round($jiasumoney22 * $jifen2info['dongtaiB_money'] * 0.01, 2);
                }
                if($jifen2info['dongtaiB_hei'] > 0)
                {
                    $jiasumoney21_hei = round($jiasumoney22 * $jifen2info['dongtaiB_hei'] * 0.01, 2);
                }
                $res2 = \app\common\Member::addscoreB($aid, $jiajianjiemember['pid'], '-'.$jiasumoney22, $day.'分销加速释放减去动态积分:'.$jiasumoney22);
                if($jiasumoney21 > 0 ){
                    \app\common\Member::addmoney($aid, $jiajianjiemember['pid'], $jiasumoney21, $day.'分销加速释放加入金额'.$jiasumoney21);
                }
                if($jiasumoney21_hei > 0 ){
                    \app\common\Member::addhei($aid, $jiajianjiemember['pid'], $jiasumoney21_hei, $day.'分销加速释放加入'.t('现金券').$jiasumoney21_hei);
                }
            }
        }
    }

    // 这里修改用户的星级
    $jiuxingsetinfo = Db::name('jifechi')->where('aid', $aid)->find();
    $jiuxingsetinfo = json_decode($jiuxingsetinfo['jiuxing'],1);
    if($jiuxingsetinfo['jiuxing_status'] == 1)
    {
        $counttotalprice = 0;
        $oldxingji = Db::name('member')->where('aid', $aid)->where('id', $mid)->field('xingji')->find();
        $oldxingji = $oldxingji['xingji'];
        $ress1 = Db::name('shop_order_goods')->where('aid', $aid)->where('mid', $mid)->where('status','in','1,2,3')->select()->toArray();
        foreach($ress1 as $ordergoods){
            $product = Db::name('shop_product')->where('id', $ordergoods['proid'])->find(); 
            if($product['is_yeji'] == 1)
            {
                $counttotalprice = $counttotalprice + $ordergoods['totalprice'];
            }
        }
        // $counttotalprice =  Db::name('shop_order')->where('aid', $aid)->where('mid', $mid)->where('status','in','1,2,3')->sum('totalprice');
        $xingji = floor($counttotalprice / $jiuxingsetinfo['xiaofeimoney']);
        if($xingji > 9)
        {
           $xingji = 9; 
        }
        Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['xingji' => $xingji]);
        // 开��星级了,返钱
        \app\common\Member::getjieidanpid($aid, $mid, $order['totalprice'], $xingji, $order['id'], $oldxingji);
    }

    foreach($ress as $ordergoods)
    {
        $product = Db::name('shop_product')->where('id', $ordergoods['proid'])->find();
        // 送消费值
        if($product['xiaofeizhi'])
        {
            $xiaofeizhi = json_decode($product['xiaofeizhi'],1);
            $xiaofeifan = $xiaofeizhi[$member['levelid']];
            $xiaofeifan = $xiaofeifan * $ordergoods['num'];
            $xiaofeizhicount = $xiaofeifan;
            // 转换绿积分
            // 绿积分的价值 $sysset['lvjifenjiazhi']
            // 消费值绿积分转换 xiaofeizhizhuan
            $sysset = Db::name('admin_set')->where('aid', $aid)->find();
            $kouchulvjifen = $xiaofeizhicount * $sysset['xiaofeizhizhuan'] * 0.01 / $sysset['lvjifenjiazhi'];
            $kouchulvjifen = round($kouchulvjifen, 2);
            if($ordergoods['bid'] == 0){
                // 给消费值
                \app\common\Member::addscorexiaofeizhi($aid, $mid, $xiaofeizhicount, '购买产品赠送消费值'.$xiaofeizhicount);
            } else{
                // 扣绿积分
                $store_info = Db::name('business')->where('aid', $aid)->where('id', $ordergoods['bid'])->find();
                $store_member_info = Db::name('member')->where('aid', $aid)->where('id', $store_info['mid2'])->find();
                if($store_info && $store_member_info)
                {
                    \app\common\Member::addscorexiaofeizhi($aid, $mid, $xiaofeizhicount, '购买产品赠送消费值'.$xiaofeizhicount);
                    \app\common\Member::addscorelv($aid, $store_info['mid2'], '-'.$kouchulvjifen, '用户购买产品赠送消费值,扣除绿积分'.$kouchulvjifen);
                }
            }
        }
        
        if ($product['xianjinquan'])
        {
            $xianjinquan2 = json_decode($product['xianjinquan'],1);
            $xiaofeifan2 = $xianjinquan2[$member['levelid']];
            $xiaofeifan2 = $xiaofeifan2 * $ordergoods['num'];
            $xiaofeizhicount2 = $xiaofeifan2;
            // 转换绿积分
            // 绿积分的价值 $sysset['lvjifenjiazhi']
            // 消费值绿积分转换 xiaofeizhizhuan
            $sysset = Db::name('admin_set')->where('aid', $aid)->find();
            $kouchulvjifen2 = $xiaofeizhicount2;
            $kouchulvjifen2 = round($kouchulvjifen2, 2);
            if($ordergoods['bid'] == 0){
                // 给消费值
                \app\common\Member::addhei($aid, $mid, $xiaofeizhicount2, '购买产品赠送'.t('现金券').$xiaofeizhicount2);
            } else{
                // 扣绿积分
                $store_info = Db::name('business')->where('aid', $aid)->where('id', $ordergoods['bid'])->find();
                $store_member_info = Db::name('member')->where('aid', $aid)->where('id', $store_info['mid2'])->find();
                if($store_info && $store_member_info)
                {
                    // 暂时不处理
                    // \app\common\Member::addscorexiaofeizhi($aid, $mid, $xiaofeizhicount, '购买产品赠送消费值'.$xiaofeizhicount);
                    // \app\common\Member::addscorelv($aid, $store_info['mid2'], '-'.$kouchulvjifen, '用户购买产品赠送消费值,扣除绿积分'.$kouchulvjifen);
                }
            }
        }
        
        if ($product['shengjiscore']) {
            // 解析产品中的升级积分设置
            $shengjiscore2 = json_decode($product['shengjiscore'], true);
            
            // 获取当前会员等级对应的积分值
            $shengjiscore2 = $shengjiscore2[$member['levelid']];
            // 根据购买数量计算总积分值
            $shengjiscore2 =  $shengjiscore2 * $ordergoods['num'];
            
            // 总消费值
            $shengjiscorecount2 =  $shengjiscore2;
    
            // 获取系统设置
            $sysset = Db::name('admin_set')->where('aid', $aid)->find();
            // 计算扣除的绿积分，实际操作中不再使用
            $kouchulvjifen2 =   $shengjiscorecount2;
            $kouchulvjifen2 = round($kouchulvjifen2, 2);
            
            // 如果订单商品的 bid 为 0，则直接给消费值
            if ($ordergoods['bid'] == 0) {
                \app\common\Member::addshengjiscore($aid, $mid, $shengjiscorecount2, '购买产品赠送' . t('升级积分') . $xiaofeizhicount2);
            } else {
                // 这里不再进行扣除绿积分的操作，仅记录相关信息
                // \app\common\Member::addscorexiaofeizhi($aid, $mid, $xiaofeizhicount, '购买产品赠送消费值' . $xiaofeizhicount);
                // \app\common\Member::addscorelv($aid, $store_info['mid2'], '-' . $kouchulvjifen, '用户购买产品赠送消费值,扣除绿积分' . $kouchulvjifen);
            }
        }
        
        // 返佣新
        if($product['if_fanyong'] > 0)
        {
            if($ordergoods['totalprice'] > 0)
            {
                $levelid = $member['levelid'];
                $fanyong = json_decode($product['fanyong'],1);
                // var_dump('用户等级'.$fanyong);
                if(isset($fanyong[$levelid]))
                {
                    $fanyongbl = $fanyong[$levelid];
                } else{
                    $fanyongbl = $fanyong[0];
                }
                if($product['if_fanyong'] == 1)
                {
                    $fangou_product = $ordergoods['totalprice'] * $fanyongbl * 0.01;
                    $fangou_product = round($fangou_product, 2);
                } elseif($product['if_fanyong'] == 2)
                {
                    $fangou_product = $fanyongbl * $ordergoods['num'];
                }
                Db::name('member_commission_record')->insert([
                    'aid' => $aid,
                    'mid' => $ordergoods['mid'],
                    'frommid' => $mid,
                    'orderid' => $orderid,
                    'ogid' => $ordergoods['id'],
                    'type' => 'shop',
                    'commission' => $fangou_product,
                    'remark' => '自购商品'.$ordergoods['name'].'返佣,奖励'.$fangou_product,
                    'createtime' => time()
                ]);
                \app\common\Member::getzhituichouyong($aid, $mid, $fangou_product, $orderid, $ordergoods['id'], $ordergoods['ordernum']);
                \app\common\Member::getjiantuichouyong($aid, $mid, $fangou_product, $orderid, $ordergoods['id'], $ordergoods['ordernum']);
                \app\common\Member::addParentcommissionLogs($aid, $mid, $orderid, $ordergoods['id'], $fangou_product, '下级分佣奖励2');
            }
        }
    }

    // 推荐送消费值
    foreach($ress as $ordergoods){
        // 一级
        if($ordergoods['xfzparent2id'] && $ordergoods['xfzparent2commission'])
        {
             \app\common\Member::addscorexiaofeizhi($aid, $ordergoods['xfzparent2id'], $ordergoods['xfzparent2commission'], '下级购买产品赠送消费值'.$ordergoods['xfzparent2commission']);
        }
        // 二级
        if($ordergoods['xfzparent3id'] && $ordergoods['xfzparent3commission'])
        {
             \app\common\Member::addscorexiaofeizhi($aid, $ordergoods['xfzparent3id'], $ordergoods['xfzparent3commission'], '下下级购买产品赠送消费值'.$ordergoods['xfzparent3commission']);
        }
        // 三级
        if($ordergoods['xfzparent4id'] && $ordergoods['xfzparent4commission'])
        {
           \app\common\Member::addscorexiaofeizhi($aid, $ordergoods['xfzparent4id'], $ordergoods['xfzparent4commission'], '下下下级购买产品赠送消费值'.$ordergoods['xfzparent4commission']); 
        }
    }

    foreach($ress as $ordergoods)
    {
        $member = Db::name('member')->where('id', $order['mid'])->find();
        $newbus_total = $member['bus_total'];
        
        // 给积分
        \app\common\Member::addscore($aid, $order['mid'], $ordergoods['jingji'], '购买产品静态分佣'.t('积分'));
        if($memberlevel['is_bus_bound_multiples'] == 1)
        {
            if($newbus_total >= $ordergoods['jingyong'])
            {
                // 给佣金 
                if($ordergoods['jingyong'] > 0){
                    // \app\common\Member::addcommission($aid, $order['mid'], $order['mid'], $ordergoods['jingyong'], '购买产品静态分佣佣金');
                    Db::name('member_commission_record')->insert([
                        'jl_type' => 3,
                        'aid' => $aid,
                        'mid' => $order['mid'],
                        'frommid' => $order['mid'],
                        'orderid' => $orderid,
                        'ogid' => $ordergoods['id'],
                        'type' => 'shop',
                        'commission' => $ordergoods['jingyong'],
                        'remark' => '购买产品静态分佣佣金',
                        'createtime' => time()
                    ]);
                    \app\common\Member::addbusTotal($aid, $mid, $mid, '-'.$ordergoods['jingyong'], '奖励静态分佣扣除'.t('创业值'));
                }
            }
        } else{
            if($ordergoods['jingyong'] > 0){
                // 给佣金 
                // \app\common\Member::addcommission($aid, $order['mid'], $order['mid'], $ordergoods['jingyong'], '购买产品静态分佣佣金');
                Db::name('member_commission_record')->insert([
                    'jl_type' => 3,
                    'aid' => $aid,
                    'mid' => $order['mid'],
                    'frommid' => $order['mid'],
                    'orderid' => $orderid,
                    'ogid' => $ordergoods['id'],
                    'type' => 'shop',
                    'commission' => $ordergoods['jingyong'],
                    'remark' => '购买产品静态分佣佣金',
                    'createtime' => time()
                ]);
            }
        }
    }

    // 给积分一级二级三级持续
    foreach($ress as $ordergoods)
    {
        // 一级
        \app\common\Member::addscore($aid, $ordergoods['parent1'], $ordergoods['parent1commissionscore'], '下级购买产品产生'.t('积分'));
        // 二级
        \app\common\Member::addscore($aid, $ordergoods['parent2'], $ordergoods['parent2commissionscore'], '下下级购买产品产生'.t('积分'));
        // 三级 
        \app\common\Member::addscore($aid, $ordergoods['parent3'], $ordergoods['parent3commissionscore'], '下下下级购买产品产生'.t('积分'));
        // 持续推荐奖励
        \app\common\Member::addscore($aid, $ordergoods['parent4'], $ordergoods['parent4commissionscore'], '下下下下级购买产品产生'.t('积分'));
    }

    // 开发奖直推下级所有业绩每达到25箱就奖励12800
    $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->find();
    if($member['pid'] > 0 )
    {
        // 代表有上级
        \app\common\Member::sendkfj($aid, $member['pid']);
    }

    // 发送创业基金
    \app\common\Member::sendcyjijin($aid, $mid);
    $set = Db::name('admin_set')->where('aid', $aid)->find();
    // var_dump(1);
    if($set['gongpaizhi_shi'] > 0 && $set['gongpaizhi_ren'] > 0 && $set['gongpaizhi_xiaofei'] > 0)
    {
        // var_dump($product['songduihuanfen']);die;
        \app\common\Member::addduihuanfen($aid, $order['mid'], $product['songduihuanfen'], '购买产品赠送');
        \app\common\Member::setgongpai($aid, $mid);
    }

    \app\common\Member::uplv($aid, $mid, $order['id']);
    // 在 \app\common\Member::uplv($aid, $mid, $order['id']); 后面添加

// 处理加速卡
foreach($oglist as $og) {
    // 获取商品信息
    $product = Db::name('shop_product')->where('id', $og['proid'])->find();
    
    // 检查商品是否开启加速卡功能
    if($product && $product['card_enable'] == 1) {
        // 获取购买者信息
        $buyer = Db::name('member')->where('id', $mid)->find();
        
        // 处理直推加速卡
        if($buyer['pid'] > 0 && $product['direct_speed'] > 0) {
            Db::name('acceleration_card')->insert([
                'aid' => $aid,
                'mid' => $buyer['pid'], // 发给直推上级
                'from_mid' => $mid, // 来源用户
                'order_id' => $orderid,
                'goods_id' => $og['proid'],
                'type' => 1, // 1直推 2间推
                'speed_ratio' => $product['direct_speed'],
                'days' => $product['card_days'],
                'start_time' => time(),
                'end_time' => time() + ($product['card_days'] * 86400),
                'status' => 1,
                'create_time' => time()
            ]);
            
         
        }
        
        // 处理间推加速卡
        if($buyer['pid'] > 0 && $product['indirect_speed'] > 0) {
            // 获取二级上级
            $parent = Db::name('member')->where('id', $buyer['pid'])->find();
            if($parent && $parent['pid'] > 0) {
                Db::name('acceleration_card')->insert([
                    'aid' => $aid,
                    'mid' => $parent['pid'], // 发给间推上级
                    'from_mid' => $mid, // 来源用户
                    'order_id' => $orderid,
                    'goods_id' => $og['proid'],
                    'type' => 2, // 1直推 2间推
                    'speed_ratio' => $product['indirect_speed'],
                    'days' => $product['card_days'],
                    'start_time' => time(),
                    'end_time' => time() + ($product['card_days'] * 86400),
                    'status' => 1,
                    'create_time' => time()
                ]);
                
                
            }
        }
    }
}

// 继续原有的通知发送逻辑
\app\common\Wifiprint::print($aid, 'shop', $order['id']);
    \app\common\Wifiprint::print($aid, 'shop', $order['id']);

    // 公众号通知 订单支付成功
    $tmplcontent = [];
    if($order['paytypeid'] != 4) {
        $tmplcontent['first'] = '有新订���支付成功';
    } else {
        $tmplcontent['first'] = '有新订单下单成功（'.$order['paytype'].'）';
    }
    $tmplcontent['remark'] = '点击进入查看~';
    $tmplcontent['keyword1'] = $member['nickname']; // 用户名
    $tmplcontent['keyword2'] = $order['ordernum']; // 订单号
    $tmplcontent['keyword3'] = $order['totalprice'].'元'; // 订单金额
    $tmplcontent['keyword4'] = $order['title']; // 商品信息
    $tmplcontentNew = [];
    $tmplcontentNew['thing8'] = \app\common\Mendian::getNameWithBusines($order); // 门店
    $tmplcontentNew['phrase18'] = $member['nickname']; // 用户名
    $tmplcontentNew['character_string2'] = $order['ordernum']; // 订单号
    $tmplcontentNew['amount5'] = $order['totalprice'] == 0 ? '0.00' : $order['totalprice']; // 订单金额
    $tmplcontentNew['thing3'] = $order['title']; // 商品信息
    
    $adminResponse = \app\common\Wechat::sendhttmpl($aid, 0, 'tmpl_orderpay', $tmplcontent, m_url('admin/order/shoporder', $aid), $order['mdid'], $tmplcontentNew);
   
    // 添加次数 type2
    $adminnotice_data = [
        'aid' => $aid,
        'bid' => $order['bid'],
        'uid' => $order['mid'],
        'title' => $order['title'],
        'content' => json_encode($tmplcontent),
        'createtime' => time(),
        'ti_time' => time(),
        'type' => 0,
        'type2' => 'shop_order',
        'count' => 0,
        'ordernum' => $order['ordernum'],
    ];
    Db::name('admin_notice')->insertGetId($adminnotice_data);
    if($order['paytypeid'] != 4) {
        $tmplcontent['first'] = '恭喜您的订单已支付成功';
    } else {
        $tmplcontent['first'] = '恭喜您的订单已下单成功';
    }
    $rs = \app\common\Wechat::sendtmpl($aid, $mid, 'tmpl_orderpay', $tmplcontent, m_url('pages/my/usercenter', $aid), $tmplcontentNew);

    $tmplcontent = [];
    $tmplcontent['thing11'] = $order['title'];
    $tmplcontent['character_string2'] = $order['ordernum'];
    if($order['paytypeid'] != 4) {
        $tmplcontent['phrase10'] = '已支付';
    } else{
        $tmplcontent['phrase10'] = $order['paytype'];
    }
    $tmplcontent['amount13'] = $order['totalprice'].'元';
    $tmplcontent['thing27'] = $member['nickname'];
    \app\common\Wechat::sendhtwxtmpl($aid, $order['bid'], 'tmpl_orderpay', $tmplcontent, 'admin/order/shoporder', $order['mdid']);

    // 短信通知
    $rs = \app\common\Sms::send($aid, $member['tel'] ? $member['tel'] : $order['tel'], 'tmpl_orderpay', ['ordernum' => $order['ordernum']]);
 // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
    $set = Db::name('admin_set')->where('aid', $aid)->find();
    if($set['fxjiesuantime'] == 1 && $set['fxjiesuantime_delaydays'] == '0'){
        \app\common\Order::giveCommission($order, 'shop');
    }

    // 同步更新 daigou_order 表的订单状态和 paytypeid
    try {
        // 获取当前 shop_order 记录
        $shopOrder = Db::name('shop_order')->where('id', $orderid)->find();

        if ($shopOrder && isset($shopOrder['d_orderid'])) {
            $d_orderid = $shopOrder['d_orderid'];

            // 查找对应的 daigou_order 记录
            $daigouOrder = Db::name('daigou_order')->where('id', $d_orderid)->find();

            if ($daigouOrder) {
                // 更新 daigou_order 的状态和 paytypeid
                $updateData = [
                    'status' => '1', // 请根据您的状态定义进行调整，例如 'paid', 'completed' 等
                   
                    'paytypeid' => $shopOrder['paytypeid'], // 同步 paytypeid
                    // 其他需要更新的字段
                ];

                Db::name('daigou_order')->where('id', $d_orderid)->update($updateData);

                
            } else {
              
            }
        } else {
         
        }
    } catch (\Exception $e) {
        // 处理异常，例如记录日志
     
    }
    
    // 订单支付成功后处理排单逻辑
    try {
        // 检查是否启用排单功能
        $paidan_enabled = Db::name('paidan_config')
            ->where('aid', $aid)
            ->where('is_enabled', 1)
            ->count();
            
        if($paidan_enabled > 0) {
            // 调用排单逻辑创建点位
            \app\common\PaidanLogic::createPosition($aid, $mid, $orderid, $order['totalprice']);
            Log::write('订单支付成功调用排单逻辑-订单ID:' . $orderid . '-会员ID:' . $mid . '-金额:' . $order['totalprice']);
        } else {
            Log::write('排单功能未启用，跳过排单逻辑-订单ID:' . $orderid);
        }
    } catch (\Exception $e) {
        Log::error('排单逻辑处理异常-订单ID:' . $orderid . '-错误信息:' . $e->getMessage());
    }
    
    // 分红点计算逻辑
    try {
        \app\common\FenhongDian::calculateAndReward($aid, $mid, $orderid, 'order_pay');
        Log::write('订单支付成功分红点计算-订单ID:' . $orderid . '-会员ID:' . $mid);
    } catch (\Exception $e) {
        Log::error('分红点计算异常-订单ID:' . $orderid . '-错误信息:' . $e->getMessage());
    }
}

	//积分商城订单
	public static function scoreshop_pay($orderid){
		$order = Db::name('scoreshop_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		//自动发货
		if($order['freight_type']==3){
			$og = Db::name('scoreshop_order_goods')->where('orderid',$order['id'])->find();
			$freight_content = Db::name('scoreshop_product')->where('id',$og['proid'])->value('freightcontent');
			Db::name('scoreshop_order')->where('id',$order['id'])->update(['freight_content'=>$freight_content,'status'=>2,'send_time'=>time()]);
			Db::name('scoreshop_order_goods')->where('orderid',$order['id'])->update(['status'=>2]);
			if(getcustom('scoreshop_wx_hongbao')){
			    //如果是兑��红包
                if($og['type'] ==1){
                    $hb_scoreshop_product = Db::name('scoreshop_product')->where('id',$og['proid'])->field('hongbao_money,scene_id')->find();

                    if($hb_scoreshop_product['hongbao_money'] > 0){
                        $money =  dd_money_format($hb_scoreshop_product['hongbao_money'],2);
                        $rs = \app\common\Wxpay::sendredpackage($order['aid'],$order['mid'],$order['platform'],$money,mb_substr($order['title'],0,10),'微信红包','恭喜发财','微信红包',$hb_scoreshop_product['scene_id']);
                        if($rs['status']==0){ //发放失败
                            Db::name('scoreshop_order')->where('id',$order['id'])->update(['send_remark'=>$rs['msg']]);
                        }else{
                            //修改订单状态
                            Db::name('scoreshop_order')->where('id',$order['id'])->update(['status'=>3,'send_time'=>time(),'send_remark'=>'红包发放成功']);
                            Db::name('scoreshop_order_goods')->where('orderid',$order['id'])->update(['status'=>3]);
                        }
                    }
                }
            }
            if(getcustom('scoreshop_to_money')){
                if($order['type'] ==2 && $order['bid'] == 0){
                    $give_money = Db::name('scoreshop_product')->where('id',$og['proid'])->value('give_money');
                     if($give_money > 0){
                         $give_money =  dd_money_format($give_money,2);
                         //增加余额
                         $rs = \app\common\Member::addmoney($order['aid'],$mid,$give_money,'积分兑换,订单号:'.$order['ordernum']);
                         if($rs['status'] ==1){
                             //修改订单状态
                             Db::name('scoreshop_order')->where('id',$order['id'])->update(['status'=>3,'send_time'=>time(),'send_remark'=>'余额发放成功']);
                             Db::name('scoreshop_order_goods')->where('orderid',$order['id'])->update(['status'=>3]);
                         }else{
                             Db::name('scoreshop_order')->where('id',$order['id'])->update(['send_remark'=>'余额发放失败']);
                         }

                     }
                }
            }
            //发货信息录入 微信小程序+微信支付
            if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                \app\common\Order::wxShipping($order['aid'],$order,'scoreshop');
            }
		}

		//在线卡密
		if($order['freight_type']==4){
			$og = Db::name('scoreshop_order_goods')->where('orderid',$order['id'])->find();
			$codelist = Db::name('scoreshop_codelist')->where('proid',$og['proid'])->where('status',0)->order('id')->limit($og['num'])->select()->toArray();
			if($codelist && count($codelist) >= $og['num']){
				$pscontent = [];
				foreach($codelist as $codeinfo){
					$pscontent[] = $codeinfo['content'];
					Db::name('scoreshop_codelist')->where('id',$codeinfo['id'])->update(['orderid'=>$order['id'],'ordernum'=>$order['ordernum'],'headimg'=>$member['headimg'],'nickname'=>$member['nickname'],'buytime'=>time(),'status'=>1]);
				}
				$pscontent = implode("\r\n",$pscontent);
				Db::name('scoreshop_order')->where('id',$order['id'])->update(['freight_content'=>$pscontent,'status'=>2,'send_time'=>time()]);
				Db::name('scoreshop_order_goods')->where('orderid',$order['id'])->update(['status'=>2]);
			}

            //发货信息录入 微信小程序+微信支付
            if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                \app\common\Order::wxShipping($order['aid'],$order,'scoreshop');
			}
		}
		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'scoreshop',$order['totalprice']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}
		 \app\common\Member::addgongxianzhi($aid, $mid, $order['givescoregongxianzhi'], '参与积分兑换赠送'.t('贡献值'));
		\app\common\Wifiprint::print($aid,'scoreshop',$order['id']);
		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新'.t('积分').'商城订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalscore'].t('积分').($order['totalprice']>0?' + '.$order['totalprice'].'元':'');//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,0,'tmpl_orderpay',$tmplcontent,m_url('admin/order/scoreshoporder', $aid),$order['mdid']);

		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('activity/scoreshop/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,0,'tmpl_orderpay',$tmplcontent,'admin/order/scoreshoporder',$order['mdid']);
		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

		$set = Db::name('admin_set')->where('aid',$aid)->find();
		if($set['fxjiesuantime'] == 1 && $set['fxjiesuantime_delaydays'] == '0'){
			\app\common\Order::giveCommission($order,'scoreshop');
		}
	}
	//拼团订单
	public static function collage_pay($orderid){
		$order = Db::name('collage_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		if($order['buytype']!=1){
			$team = Db::name('collage_order_team')->where('id',$order['teamid'])->find();
			$tdata = [];
			$tdata['num'] = $team['num'] + 1;
			if($team['num'] + 1 >= $team['teamnum']){
				$tdata['status'] = 2;
				//团长奖励积分
				$orderlist = Db::name('collage_order')->where(['teamid'=>$team['id'],'status'=>1])->select()->toArray();
				$leader = Db::name('member')->where('id',$team['mid'])->find();
				foreach($orderlist as $v){
					if($v['givescore'] > 0){
						\app\common\Member::addscore($aid,$v['mid'],$v['givescore'],'购买产品奖励'.t('积分'));
					}
					//自动发货
					if($order['freight_type']==3){
						$freight_content = Db::name('collage_product')->where('id',$order['proid'])->value('freightcontent');
						Db::name('collage_order')->where('id',$order['id'])->update(['freight_content'=>$freight_content,'status'=>2,'send_time'=>time()]);
  //发货信息录入 微信小程序+微信支付
  if($v['platform'] == 'wx' && $v['paytypeid'] == 2){
	\app\common\Order::wxShipping($v['aid'],$v,'collage');
}					}
					//在线卡密
					if($order['freight_type']==4){
						$codelist = Db::name('collage_codelist')->where('proid',$order['proid'])->where('status',0)->order('id')->limit($order['num'])->select()->toArray();
						if($codelist && count($codelist) >= $order['num']){
							$pscontent = [];
							foreach($codelist as $codeinfo){
								$pscontent[] = $codeinfo['content'];
								Db::name('collage_codelist')->where('id',$codeinfo['id'])->update(['orderid'=>$order['id'],'ordernum'=>$order['ordernum'],'headimg'=>$member['headimg'],'nickname'=>$member['nickname'],'buytime'=>time(),'status'=>1]);
							}
							$pscontent = implode("\r\n",$pscontent);
							Db::name('collage_order')->where('id',$order['id'])->update(['freight_content'=>$pscontent,'status'=>2,'send_time'=>time()]);
						}
					}
					//公众号通知 拼团成功通知
					$tmplcontent = [];
					$tmplcontent['first'] = '有新拼团订单拼团成功';
					$tmplcontent['remark'] = '点击进入查看~';
					$tmplcontent['keyword1'] = $order['title']; //商品名称
					$tmplcontent['keyword2'] = $leader['nickname'];//团长
					$tmplcontent['keyword3'] = $team['teamnum'];//成团人数
					//\app\common\Wechat::sendhttmpl(aid,$order['bid'],'tmpl_collagesuccess',$tmplcontent,m_url('admin/order/collageorder'));
					$tmplcontent['first'] = '恭喜您拼团成功';
					$rs = \app\common\Wechat::sendtmpl($aid,$v['mid'],'tmpl_collagesuccess',$tmplcontent,m_url('activity/collage/orderlist', $aid));
					//订阅消息
					$tmplcontent = [];
					$tmplcontent['thing1'] = $order['title'];
					$tmplcontent['thing10'] = $leader['nickname'];
					$tmplcontent['number12'] = $team['teamnum'];

					$tmplcontentnew = [];
					$tmplcontentnew['thing7'] = $order['title'];
					$tmplcontentnew['thing12'] = $leader['nickname'];
					$tmplcontentnew['number2'] = $team['teamnum'];
					\app\common\Wechat::sendwxtmpl($aid,$v['mid'],'tmpl_collagesuccess',$tmplcontentnew,'pages/my/usercenter',$tmplcontent);
					//短信通知
					$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_collagesuccess',['ordernum'=>$order['ordernum']]);
				}
			}else{
				$tdata['status'] = 1;
			}
			Db::name('collage_order_team')->where('aid',$aid)->where('id',$order['teamid'])->update($tdata);
		}else{
			//自动发货
			if($order['freight_type']==3){
				$freight_content = Db::name('collage_product')->where('id',$order['proid'])->value('freightcontent');
				Db::name('collage_order')->where('id',$order['id'])->update(['freight_content'=>$freight_content,'status'=>2,'send_time'=>time()]);
                //发货信息录入 微信小程序+微信支付
                if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                    \app\common\Order::wxShipping($order['aid'],$order,'collage');
                }
			}
			//在线卡密
			if($order['freight_type']==4){
				$codelist = Db::name('collage_codelist')->where('proid',$order['proid'])->where('status',0)->order('id')->limit($order['num'])->select()->toArray();
				if($codelist && count($codelist) >= $order['num']){
					$pscontent = [];
					foreach($codelist as $codeinfo){
						$pscontent[] = $codeinfo['content'];
						Db::name('collage_codelist')->where('id',$codeinfo['id'])->update(['orderid'=>$order['id'],'ordernum'=>$order['ordernum'],'headimg'=>$member['headimg'],'nickname'=>$member['nickname'],'buytime'=>time(),'status'=>1]);
					}
					$pscontent = implode("\r\n",$pscontent);
					Db::name('collage_order')->where('id',$order['id'])->update(['freight_content'=>$pscontent,'status'=>2,'send_time'=>time()]);
				}
                //发货信息录入 微信小程序+微信支付
                if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                    \app\common\Order::wxShipping($order['aid'],$order,'collage');
			}
			}
            \app\common\Wifiprint::print($aid,'collage',$order['id']);//成功后打印
		}

		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'collage',$order['totalprice']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}
		\app\common\Wifiprint::print($aid,'collage',$order['id']);

		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新拼团订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/collageorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('activity/collage/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/collageorder',$order['mdid']);

		//短信通知
		if($order['buytype']==1){ //直接购买
			$rs = \app\common\Sms::send($aid,$member['tel'] ? $member['tel'] : $order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);
		}
	}
    public static function cycle_pay($orderid){
        $order = Db::name('cycle_order')->where('id',$orderid)->find();

        $member = Db::name('member')->where('id',$order['mid'])->find();
        $aid = $order['aid'];
        $mid = $order['mid'];
        Db::name('cycle_order_stage')->where('aid',$aid)->where('orderid',$order['id'])->update(['status' =>1]);

        //支付后送券
        $couponlist = \app\common\Coupon::getpaygive($aid,$mid,'cycle',$order['totalprice']);
        if($couponlist){
            foreach($couponlist as $coupon){
                \app\common\Coupon::send($aid,$mid,$coupon['id']);
            }
        }
        \app\common\Wifiprint::print($aid,'cycle',$order['id']);

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'cycle');
        }

        //公众号通知 订单支付成功
        $tmplcontent = [];
        $tmplcontent['first'] = '有新周期购订单支付成功';
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $member['nickname']; //用户名
        $tmplcontent['keyword2'] = $order['ordernum'];//订单号
        $tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
        $tmplcontent['keyword4'] = $order['title'];//商品信息
        \app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/cycleorder', $aid),$order['mdid']);
        $tmplcontent['first'] = '恭喜您的订单已支付成功';
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('pagesExt/cycle/orderList', $aid));

        $tmplcontent = [];
        $tmplcontent['thing11'] = $order['title'];
        $tmplcontent['character_string2'] = $order['ordernum'];
        $tmplcontent['phrase10'] = '已支付';
        $tmplcontent['amount13'] = $order['totalprice'].'元';
        $tmplcontent['thing27'] = $member['nickname'];
        \app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/cycleorder',$order['mdid']);

        $rs = \app\common\Sms::send($aid,$member['tel'] ? $member['tel'] : $order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
    }
		//幸运拼团订单
	public static function lucky_collage_pay($orderid){

		$order = Db::name('lucky_collage_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		if($order['buytype'] !=1 ){
		    \app\common\Member::addgongxianzhi($aid, $order['mid'], -$order['gongxianzhiNumber'], '参与拼团扣除'.t('贡献值'));
		      //self::calculateUpwardRewards($order, $member);
			//支付成功后奖励分享者余额，积分，佣金，优惠券
			if(getcustom('plug_luckycollage')){
				if($order['shareid']>0){
					//取出分享
					$pmember = Db::name('member')->where('id',$order['shareid'])->find();
					if($pmember && $order['sharemoney'] > 0){  //1 奖励余额
						\app\common\Member::addmoney($aid,$order['shareid'],$order['sharemoney'],'推荐奖励'.t('余额'));
					}else if($order['sharescore'] > 0){  //2 奖励积分
						\app\common\Member::addscore($aid,$order['shareid'],$order['sharescore'],'推荐奖励'.t('积分'));
					}else if($order['sharecommission'] > 0){  //3 奖励佣金
						\app\common\Member::addcommission($aid,$order['shareid'],$order['mid'],$order['sharecommission'],'推荐奖励'.t('佣金'));
					}elseif($order['share_yhqids']){ //奖励优惠券
						foreach($order['share_yhqids'] as $yhqid){
							\app\common\Coupon::send($aid,$order['shareid'],$yhqid);
						}
					}
				}
				if($order['buytype']==2){
					//减掉开团次数
					if($order['isjiqiren']!=1 && $member['ktnum']>0){
						$ktnum = $member['ktnum']-1;
						Db::name('member')->where(['aid'=>$aid,'id'=>$order['mid']])->update(['ktnum'=>$ktnum]);
					}
				}
			}

			Db::startTrans();
	        try {
				$team = Db::name('lucky_collage_order_team')->lock(true)->where('id',$order['teamid'])->find();

				$tData['num'] = $team['num'] + 1;
				$tData['status'] = ($tData['num'] >= $team['teamnum']) ? 2 : 1;
				Db::name('lucky_collage_order_team')->where('id', $team['id'])->update($tData);

				// 增加拼团统计数据
				$userData = Db::name('lucky_collage_user_data')->where('uid', $member['id'])->find();
				if(!empty($userData)){
					$userData['number_day']  = $userData['number_day'] + 1;
					$userData['total_number']  = $userData['total_number'] + 1;
					Db::name('lucky_collage_user_data')->update($userData);
				}
				Db::commit();
			} catch (\Exception $e) {
	            // 回滚
	            Db::rollback();
	            // 退款
	            \app\common\Order::refund($order, $order['totalprice'], '拼团失败订单退款');
	            Db::name('lucky_collage_order')->where('id', $order['id'])->update(['status'=>4,'refund_status'=>2,'refund_time'=>time(),'refund_money'=>$order['totalprice'],'refund_reason'=>'拼团失败退款','iszj'=>2]);
	            
	         
	            
	            return;
	        }

			$product = Db::name('lucky_collage_product')->where('id', $team['proid'])->find();
			if($tData['status'] == 2 && $product['start_from_full'] == 1){
				\app\model\LuckyCollage::kaijiang($order);
			}
		}

		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'lucky_collage',$order['totalprice']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}

		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新拼团订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/luckycollageorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('activity/luckycollage/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/luckycollageorder',$order['mdid']);

		\app\common\Wifiprint::print($aid,'collage',$order['id']);
		//短信通知
		if($order['buytype']==1){ //直接购买
			$rs = \app\common\Sms::send($aid,$member['tel'] ? $member['tel'] : $order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);
		}

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}
            public static function calculateUpwardRewards($order, $member)
            {
                $aid = $order['aid'];
                $mid = $order['mid'];
                $totalPrice = $order['totalprice'];
                
                // 获取用户的 path 字段，表示所有上级的 ID 列表
                $path = $member['path']; // 直接从 $member 中获取 path
                if (!$path) {
                    return; // 没有上级，不进行奖励分配
                }
            
                // 将 path 字段解析为数组
                $parentIds = explode(',', $path);
                $parentIds = array_reverse($parentIds); // 从最近的上级开始遍历
            
                // 遍历每个上级，计算奖励并分配
                foreach ($parentIds as $parentId) {
                    $parentMember = Db::name('member')->where('aid', $aid)->where('id', $parentId)->find();
                    if (!$parentMember) {
                        continue; // 上级不存在，跳过
                    }
            
                    // 获取上级的会员等级
                    $parentLevel = Db::name('member_level')->where('aid', $aid)->where('id', $parentMember['levelid'])->find();
                    if (!$parentLevel || $parentLevel['can_agent'] == 0) {
                        continue; // 上级没有分销资格，跳过
                    }
            
                    // 计算奖励金额
                    $rewardAmount = 0;
                    if ($order['uprewardset'] == 1) { // 单独设置奖励比例
                        $uprewarddata = json_decode($order['uprewarddata'], true);
                        if (isset($uprewarddata[$parentLevel['id']])) {
                            $rewardPercentage = $uprewarddata[$parentLevel['id']]['upreward'];
                            $rewardAmount = $rewardPercentage * $totalPrice * 0.01;
                        }
                    } else { // 按照会员等级默认的比例进行奖励
                        $rewardPercentage = $parentLevel['upreward'];
                        $rewardAmount = $rewardPercentage * $totalPrice * 0.01;
                    }
            
                    // 分配奖励（可以是余额、积分、佣金等）
                    if ($rewardAmount > 0) {
                        // 根据需要修改这里的奖励类型，当前示例为佣金
                        \app\common\Member::addcommission($aid, $parentId, $mid, $rewardAmount, '下级购买商品奖励');
                    }
                }
            }

	//砍价订单
	public static function kanjia_pay($orderid){
		$order = Db::name('kanjia_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$product = Db::name('kanjia_product')->where('id',$order['proid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		//自动发货
		if($order['freight_type']==3){
			$freight_content = $product['freightcontent'];
			Db::name('collage_order')->where('id',$order['id'])->update(['freight_content'=>$freight_content,'status'=>2,'send_time'=>time()]);
		 //发货信息录入 微信小程序+微信支付
		 if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
			\app\common\Order::wxShipping($order['aid'],$order,'kanjia');
		}
	}
		//在线卡密
		if($order['freight_type']==4){
			$codelist = Db::name('kanjia_codelist')->where('proid',$order['proid'])->where('status',0)->order('id')->limit($order['num'])->select()->toArray();
			if($codelist && count($codelist) >= $order['num']){
				$pscontent = [];
				foreach($codelist as $codeinfo){
					$pscontent[] = $codeinfo['content'];
					Db::name('kanjia_codelist')->where('id',$codeinfo['id'])->update(['orderid'=>$order['id'],'ordernum'=>$order['ordernum'],'headimg'=>$member['headimg'],'nickname'=>$member['nickname'],'buytime'=>time(),'status'=>1]);
				}
				$pscontent = implode("\r\n",$pscontent);
				Db::name('kanjia_order')->where('id',$order['id'])->update(['freight_content'=>$pscontent,'status'=>2,'send_time'=>time()]);
			}
            //发货信息录入 微信小程序+微信支付
            if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                \app\common\Order::wxShipping($order['aid'],$order,'kanjia');
            }
			
		}

		//买单后发放帮砍得积分或余额
		if($product['helpgive_ff'] == 1 && $product['helpgive_percent'] > 0 && $order['joinid']){
			$helplist = Db::name('kanjia_help')->where('aid',$aid)->where('joinid',$order['joinid'])->where('mid','<>',$mid)->select()->toArray();
			foreach($helplist as $help){
				if($product['helpgive_type'] == 0){ //积分
					$givescore = intval($help['cut_price'] * $product['helpgive_percent'] * 0.01);
					if($givescore > 0){
						\app\common\Member::addscore($aid,$help['mid'],$givescore,'帮好友砍价奖励');
					}
				}
				if($product['helpgive_type'] == 1){ //余额
					$givemoney = round($help['cut_price'] * $product['helpgive_percent'] * 0.01,2);
					if($givemoney > 0){
						\app\common\Member::addmoney($aid,$help['mid'],$givemoney,'帮好友砍价奖励');
					}
				}
			}
		}
		//下单增加帮砍次数
		if($product['perhelpnum_buyadd'] > 0){
			$sharelog = Db::name('kanjia_sharelog')->where('aid',$aid)->where('proid',$product['id'])->where('mid',$mid)->find();
			if($sharelog){
				Db::name('kanjia_sharelog')->where('id',$sharelog['id'])->inc('addtimes',$product['perhelpnum_buyadd'])->update();
			}else{
				$data = [];
				$data['aid'] = $aid;
				$data['proid'] = $product['id'];
				$data['mid'] = $mid;
				$data['addtimes'] = $product['perhelpnum_buyadd'];
				Db::name('kanjia_sharelog')->insert($data);
			}
		}
		//下单送抽奖/余额/积分/优惠券
		if($product['paygive_choujiangtimes'] > 0 && $product['paygive_choujiangid'] > 0){
			$sharelog = Db::name('choujiang_sharelog')->where('aid',$aid)->where('hid',$product['paygive_choujiangid'])->where('mid',$mid)->find();
			if($sharelog){
				Db::name('choujiang_sharelog')->where('id',$sharelog['id'])->inc('extratimes',$product['paygive_choujiangtimes'])->update();
			}else{
				$data = [];
				$data['aid'] = $aid;
				$data['hid'] = $product['paygive_choujiangid'];
				$data['mid'] = $mid;
				$data['extratimes'] = $product['paygive_choujiangtimes'];
				Db::name('choujiang_sharelog')->insert($data);
			}
		}
		if($product['paygive_money'] > 0){
			\app\common\Member::addmoney($aid,$mid,$product['paygive_money'],'砍价活动下单赠送');
		}
		if($product['paygive_score'] > 0){
			\app\common\Member::addscore($aid,$mid,$product['paygive_score'],'砍价活动下单赠送');
		}
		if($product['paygive_couponid'] > 0){
			\app\common\Coupon::send($aid,$mid,$product['paygive_couponid']);
		}


		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'collage',$order['totalprice']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}
		\app\common\Wifiprint::print($aid,'kanjia',$order['id']);

		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新砍价订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/kanjiaorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('activity/kanjia/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/kanjiaorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel'] ? $member['tel'] : $order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}
	//秒杀订单
	public static function seckill_pay($orderid){
		$order = Db::name('seckill_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		//自动发货
		if($order['freight_type']==3){
			$freight_content = Db::name('seckill_product')->where('id',$order['proid'])->value('freightcontent');
			Db::name('seckill_order')->where('id',$order['id'])->update(['freight_content'=>$freight_content,'status'=>2,'send_time'=>time()]);
            //发货信息录入 微信小程序+微信支付
            if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                \app\common\Order::wxShipping($order['aid'],$order,'seckill');
            }
		}
		//在线卡密
		if($order['freight_type']==4){
			$codelist = Db::name('seckill_codelist')->where('proid',$order['proid'])->where('status',0)->order('id')->limit($order['num'])->select()->toArray();
			if($codelist && count($codelist) >= $order['num']){
				$pscontent = [];
				foreach($codelist as $codeinfo){
					$pscontent[] = $codeinfo['content'];
					Db::name('seckill_codelist')->where('id',$codeinfo['id'])->update(['orderid'=>$order['id'],'ordernum'=>$order['ordernum'],'headimg'=>$member['headimg'],'nickname'=>$member['nickname'],'buytime'=>time(),'status'=>1]);
				}
				$pscontent = implode("\r\n",$pscontent);
				Db::name('seckill_order')->where('id',$order['id'])->update(['freight_content'=>$pscontent,'status'=>2,'send_time'=>time()]);
			}
            //发货信息录入 微信小程序+微信支付
            if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                \app\common\Order::wxShipping($order['aid'],$order,'seckill');
            }
			
		}

		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'seckill',$order['totalprice']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}
		 \app\common\Member::addgongxianzhi($aid, $mid, $order['givescoregongxianzhi'], '参与秒杀奖励'.t('贡献值'));
		\app\common\Wifiprint::print($aid,'seckill',$order['id']);

		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新秒杀订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/seckillorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('activity/seckill/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/seckillorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel'] ? $member['tel'] : $order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);

		$set = Db::name('admin_set')->where('aid',$aid)->find();
		if($set['fxjiesuantime'] == 1 && $set['fxjiesuantime_delaydays'] == '0'){
			\app\common\Order::giveCommission($order,'seckill');
		}
	}

	//充值订单
	public static function recharge_pay($orderid){
		$order = Db::name('recharge_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];

		\app\common\Member::addmoney($aid,$mid,$order['money'],t('余额').'充值');
		//充值赠送
		$giveset = Db::name('recharge_giveset')->where('aid',$aid)->find();
		if($giveset && $giveset['status']==1){
			$givedata = json_decode($giveset['givedata'],true);
		}else{
			$givedata = array();
		}
		$givemoney = 0;
		$givescore = 0;
		$moneyduan = 0;
		if($givedata){
			foreach($givedata as $give){
				if($order['money']*1 >= $give['money']*1 && $give['money']*1 > $moneyduan){
					$moneyduan = $give['money']*1;
					$givemoney = $give['give']*1;
                    $givescore = $give['give_score']*1;
				}
			}
		}
		if($givemoney > 0){
			\app\common\Member::addmoney($aid,$mid,$givemoney,'充值赠送');
		}
        if($givescore > 0){
            \app\common\Member::addscore($aid,$mid,$givescore,'充值赠送');
        }



		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'recharge',$order['money']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}

		//升级
		\app\common\Member::uplv($aid,$mid);

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'recharge');
        }

		$tmplcontent = array();
		$tmplcontent['first'] = '有新充值订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['accountType'] = t('会员').'昵称';
		$tmplcontent['account'] = $member['nickname'];
		$tmplcontent['amount'] = $order['money'].'元' . ($givemoney>0?'+赠送'.$givemoney.'元':'');
		$tmplcontent['result'] = '充值成功';
		\app\common\Wechat::sendhttmpl($aid,0,'tmpl_recharge',$tmplcontent,m_url('admin/finance/rechargelog', $aid),$order['mdid']);

		//充值通知
        if(getcustom('zhaopin')){
            \app\model\Zhaopin::sendhtsms('tmpl_recharge',[],$order['aid'],$order['bid'],$order['mdid']);
        }

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}


// 月结充值订单
public static function arrears_recharge_pay($orderid){
   // var_dump($orderid);die;
    // 获取订单信息
    $order = Db::name('arrears_recharge_order')->where('id', $orderid)->find();
    if (!$order) {
        return; // 订单不存在，直接返回
    }

    $aid = $order['aid'];
    $mid = $order['mid'];

    // 获取会员信息
    $member = Db::name('member')->where('id', $mid)->find();
    if (!$member) {
        return; // 会员不存在，直接返回
    }

    // 更新欠费金额
    $arrears = Db::name('member')->where('id', $mid)->value('arrears');
    $new_arrears = $arrears + $order['money']; // 增加欠费金额
    Db::name('member')->where('id', $mid)->update(['arrears' => $new_arrears]);

  
    // 月结充值赠送（如果有）
    $giveset = Db::name('recharge_giveset')->where('aid', $aid)->find();
    if ($giveset && $giveset['status'] == 1) {
        $givedata = json_decode($giveset['givedata'], true);
    } else {
        $givedata = [];
    }

    $givemoney = 0;
    $givescore = 0;
    $moneyduan = 0;
    if ($givedata) {
        foreach ($givedata as $give) {
            if ($order['money'] * 1 >= $give['money'] * 1 && $give['money'] * 1 > $moneyduan) {
                $moneyduan = $give['money'] * 1;
                $givemoney = $give['give'] * 1;
                $givescore = $give['give_score'] * 1;
            }
        }
    }

    // 赠送金额处理
    if ($givemoney > 0) {
        \app\common\Member::addmoney($aid, $mid, $givemoney, '月结充值赠送');
    }

    // 赠送积分处理
    if ($givescore > 0) {
        \app\common\Member::addscore($aid, $mid, $givescore, '月结充值赠送');
    }

    // 发放优惠券
    $couponlist = \app\common\Coupon::getpaygive($aid, $mid, 'arrears_recharge', $order['money']);
    if ($couponlist) {
        foreach ($couponlist as $coupon) {
            \app\common\Coupon::send($aid, $mid, $coupon['id']);
        }
    }

    // 升级会员等级
    \app\common\Member::uplv($aid, $mid);

    // 如果是微信支付，发货信息录入
    if ($order['platform'] == 'wx' && $order['paytypeid'] == 2) {
        \app\common\Order::wxShipping($order['aid'], $order, 'arrears_recharge');
    }

    // 发送充值成功通知
    $tmplcontent = [];
    $tmplcontent['first'] = '有新月结充值订单支付成功';
    $tmplcontent['remark'] = '点击进入查看~';
    $tmplcontent['accountType'] = t('会员') . '昵称';
    $tmplcontent['account'] = $member['nickname'];
    $tmplcontent['amount'] = $order['money'] . '元' . ($givemoney > 0 ? '+赠送' . $givemoney . '元' : '');
    $tmplcontent['result'] = '月结充值成功';
    \app\common\Wechat::sendhttmpl($aid, 0, 'tmpl_recharge', $tmplcontent, m_url('admin/finance/arrearslog', $aid), $order['mdid']);

    // 月结充值通知
    if (getcustom('zhaopin')) {
        \app\model\Zhaopin::sendhtsms('tmpl_recharge', [], $order['aid'], $order['bid'], $order['mdid']);
    }

    // 发送新订单通知给管理员
    \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
        'consignee' => $order['consignee'],
        'phone' => $order['tel'],
        'ordernum' => $order['ordernum'],
        'amount' => $order['money']
    ]);
}

	//会员升级订单
	public static function member_levelup_pay($orderid){
		$order = Db::name('member_levelup_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];

		//成为分销商
		$leveldata = Db::name('member_level')->where('aid',$aid)->where('id',$order['levelid'])->find();
		if($leveldata['apply_check']){
			//$return = array('status'=>2,'msg'=>'付款成功请等待审核');

			$tmplcontent = [];
			$tmplcontent['first'] = '有新用户申请升级,请及时处理';
			$tmplcontent['remark'] = '请进入电脑端后台进行审核~';
			$tmplcontent['keyword1'] = $leveldata['name']; //会员等级
			$tmplcontent['keyword2'] = '待审核';//审核状态
			\app\common\Wechat::sendhttmpl($aid,0,'tmpl_uplv',$tmplcontent);

		}else{
			if($leveldata['yxqdate'] > 0){
				$levelendtime = strtotime(date('Y-m-d')) + 86400 + 86400 * $leveldata['yxqdate'];
			}else{
				$levelendtime = 0;
			}
			Db::name('member_levelup_order')->where('id',$orderid)->update(['status'=>2,'paytime'=>time()]);
            //判断是否默认分组
			if($leveldata['cid'] > 0)
            $is_default = Db::name('member_level_category')->where('id', $leveldata['cid'])->value('isdefault');
            if($is_default || $leveldata['cid'] == 0) {
                Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['levelid'=>$leveldata['id'],'levelendtime'=>$levelendtime]);
                if(getcustom('level_comwithdraw')){
                    if($member['levelstarttime']<=0 && $leveldata['fenhong'] > 0){
                        Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['levelstarttime'=>time()]);
                    }
                }else{
                    Db::name('member')->where('aid',$aid)->where('id',$mid)->update(['levelstarttime'=>time()]);
                }

                //更新代理区域
                if($leveldata['areafenhong']==1){
                    Db::name('member')->where('aid',$aid)->where('id',$order['mid'])->update(['areafenhong_province'=>$order['areafenhong_province']]);
                }elseif($leveldata['areafenhong']==2){
                    Db::name('member')->where('aid',$aid)->where('id',$order['mid'])->update(['areafenhong_province'=>$order['areafenhong_province'],'areafenhong_city'=>$order['areafenhong_city']]);
                }elseif($leveldata['areafenhong']==3){
                    Db::name('member')->where('aid',$aid)->where('id',$order['mid'])->update(['areafenhong_province'=>$order['areafenhong_province'],'areafenhong_city'=>$order['areafenhong_city'],'areafenhong_area'=>$order['areafenhong_area']]);
                }elseif($leveldata['areafenhong']==10){
                    Db::name('member')->where('aid',$aid)->where('id',$order['mid'])->update(['areafenhong_largearea'=>$order['areafenhong_largearea']]);
                }
            } else {
                if(getcustom('plug_sanyang')) {
                    $count = Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->count();
                    if($count) Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->update(['levelid' => $leveldata['id'], 'levelendtime' => $levelendtime]);
                    else {
                        $record_data = ['levelid' => $leveldata['id'], 'levelendtime' => $levelendtime];
                        $record_data['aid'] = $aid;
                        $record_data['mid'] = $mid;
                        $record_data['createtime'] = time();
                        $record_data['cid'] = $leveldata['cid'];
                        Db::name('member_level_record')->insertGetId($record_data);
                    }
                    if(getcustom('level_comwithdraw')){
                        if($member['levelstarttime']<=0 && $leveldata['fenhong'] > 0){
                            Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->update(['levelstarttime'=>time()]);
                        }
                    }else{
                        Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->update(['levelstarttime'=>time()]);
                    }

                    //更新代理区域
                    if($leveldata['areafenhong']==1){
                        Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->update(['areafenhong_province'=>$order['areafenhong_province']]);
                    }elseif($leveldata['areafenhong']==2){
                        Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->update(['areafenhong_province'=>$order['areafenhong_province'],'areafenhong_city'=>$order['areafenhong_city']]);
                    }elseif($leveldata['areafenhong']==3){
                        Db::name('member_level_record')->where('aid', $aid)->where('mid', $mid)->where('cid', $leveldata['cid'])->update(['areafenhong_province'=>$order['areafenhong_province'],'areafenhong_city'=>$order['areafenhong_city'],'areafenhong_area'=>$order['areafenhong_area']]);
                    }
                }
            }

            //0702给上级更新拉新时间
            if(getcustom('seckill2') && in_array($order['levelid'],explode(',',Db::name('seckill2_sysset')->where('aid',$order['aid'])->value('seckill_level')))){
                Db::name('member')->where(['aid'=>$aid,'id'=>$member['pid']])->update(['laxin_time'=>time()]);
            }

            if($leveldata['apply_payfenxiao'] == 1){ //升级费用参与分销及分红
                \app\common\Common::applypayfenxiao($aid,$order['id']);
            }
            if($leveldata['apply_payfenxiao'] == 1){ //升级费用参与分销及分红
                Db::name('member_levelup_order')->where('id',$orderid)->update(['isfenhong'=>1]);
            }
            //赠送积分
            if($leveldata['up_give_score'] > 0) {
                \app\common\Member::addscore($aid,$mid,$leveldata['up_give_score'],'升级奖励');
            }
            //赠送现金券
            //升级赠送现金券
            if($leveldata['up_give_xianjinquan'] >0)
            {
                 \app\common\Member::addhei($aid, $mid, $leveldata['up_give_xianjinquan'], '升级奖励');
            }

            //奖励佣金
            if($leveldata['up_give_commission'] > 0) {
                \app\common\Member::addcommission($aid,$mid,0,$leveldata['up_give_commission'],'升级奖励');
            }

            //奖励余额
            if($leveldata['up_give_money'] > 0) {
                \app\common\Member::addmoney($aid,$mid,$leveldata['up_give_money'],'升级奖励');
            }

            //赠送上级佣金
            if ($leveldata['up_give_parent_money'] > 0) {
                $pid = Db::name('member')->where('aid', $aid)->where('id', $mid)->value('pid');
                if($pid > 0) \app\common\Member::addcommission($aid, $pid, $mid, $leveldata['up_give_parent_money'], '直推奖');
            }

			$tmplcontent = [];
			$tmplcontent['first'] = '恭喜您成功升级为'.$leveldata['name'];
			$tmplcontent['remark'] = '点击进入查看~';
			$tmplcontent['keyword1'] = $leveldata['name']; //会员等级
			$tmplcontent['keyword2'] = '已生效';//审核状态
			$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_uplv',$tmplcontent,m_url('pages/my/usercenter', $aid));

		}
	}
	//表单支付
	// 表单支付
public static function form_pay($orderid){
    // 获取订单信息
    $order = Db::name('form_order')->where('id', $orderid)->find();
    if (!$order) {
        throw new \Exception('订单不存在');
    }

    // 获取用户信息
    $member = Db::name('member')->where('id', $order['mid'])->find();
    if (!$member) {
        throw new \Exception('用户不存在');
    }

    $aid = $order['aid'];
    $mid = $order['mid'];

    // 获取表单信息
    $form = Db::name('form')->where('id', $order['formid'])->find();
    if (!$form) {
        throw new \Exception('表单不存在');
    }

    $totalcommission = 0;
    $ogdata = [];
    $agleveldata1 = $agleveldata2 = $agleveldata3 = null;

    if ($form && $form['commissionset'] != -1) {
        // 分销提成逻辑
        if ($member['pid']) {
            $parent1 = Db::name('member')->where('aid', $aid)->where('id', $member['pid'])->find();
            if ($parent1) {
                $agleveldata1 = Db::name('member_level')->where('aid', $aid)->where('id', $parent1['levelid'])->find();
                if ($agleveldata1 && $agleveldata1['can_agent'] != 0) {
                    $ogdata['parent1'] = $parent1['id'];
                }
            }
        }

        if (isset($parent1) && $parent1['pid']) {
            $parent2 = Db::name('member')->where('aid', $aid)->where('id', $parent1['pid'])->find();
            if ($parent2) {
                $agleveldata2 = Db::name('member_level')->where('aid', $aid)->where('id', $parent2['levelid'])->find();
                if ($agleveldata2 && $agleveldata2['can_agent'] > 1) {
                    $ogdata['parent2'] = $parent2['id'];
                }
            }
        }

        if (isset($parent2) && $parent2['pid']) {
            $parent3 = Db::name('member')->where('aid', $aid)->where('id', $parent2['pid'])->find();
            if ($parent3) {
                $agleveldata3 = Db::name('member_level')->where('aid', $aid)->where('id', $parent3['levelid'])->find();
                if ($agleveldata3 && $agleveldata3['can_agent'] > 2) {
                    $ogdata['parent3'] = $parent3['id'];
                }
            }
        }

        // 根据佣金设置计算佣金
        if ($form['commissionset'] == 1) { // 按比例
            $commissiondata = json_decode($form['commissiondata1'], true);
            if ($commissiondata && $agleveldata1 && $agleveldata2 && $agleveldata3) {
                $ogdata['parent1commission'] = isset($commissiondata[$agleveldata1['id']]['commission1']) 
                    ? $commissiondata[$agleveldata1['id']]['commission1'] * $order['money'] * 0.01 
                    : 0;
                $ogdata['parent2commission'] = isset($commissiondata[$agleveldata2['id']]['commission2']) 
                    ? $commissiondata[$agleveldata2['id']]['commission2'] * $order['money'] * 0.01 
                    : 0;
                $ogdata['parent3commission'] = isset($commissiondata[$agleveldata3['id']]['commission3']) 
                    ? $commissiondata[$agleveldata3['id']]['commission3'] * $order['money'] * 0.01 
                    : 0;
            }
        } elseif ($form['commissionset'] == 2) { // 按固定金额
            $commissiondata = json_decode($form['commissiondata2'], true);
            if ($commissiondata && $agleveldata1 && $agleveldata2 && $agleveldata3) {
                $ogdata['parent1commission'] = isset($commissiondata[$agleveldata1['id']]['commission1']) 
                    ? $commissiondata[$agleveldata1['id']]['commission1'] 
                    : 0;
                $ogdata['parent2commission'] = isset($commissiondata[$agleveldata2['id']]['commission2']) 
                    ? $commissiondata[$agleveldata2['id']]['commission2'] 
                    : 0;
                $ogdata['parent3commission'] = isset($commissiondata[$agleveldata3['id']]['commission3']) 
                    ? $commissiondata[$agleveldata3['id']]['commission3'] 
                    : 0;
            }
        } else { // 默认按比例
            if ($agleveldata1 && $agleveldata2 && $agleveldata3) {
                $ogdata['parent1commission'] = $agleveldata1['commission1'] * $order['money'] * 0.01;
                $ogdata['parent2commission'] = $agleveldata2['commission2'] * $order['money'] * 0.01;
                $ogdata['parent3commission'] = $agleveldata3['commission3'] * $order['money'] * 0.01;
            }
        }

        // 分发佣金
        if (!empty($ogdata['parent1']) && $ogdata['parent1commission'] > 0) {
            $totalcommission += $ogdata['parent1commission'];
            \app\common\Member::addcommission($aid, $ogdata['parent1'], $mid, $ogdata['parent1commission'], '下级购买奖励2');
        }
        if (!empty($ogdata['parent2']) && $ogdata['parent2commission'] > 0) {
            $totalcommission += $ogdata['parent2commission'];
            \app\common\Member::addcommission($aid, $ogdata['parent2'], $mid, $ogdata['parent2commission'], '���二级购买奖���');
        }
        if (!empty($ogdata['parent3']) && $ogdata['parent3commission'] > 0) {
            $totalcommission += $ogdata['parent3commission'];
            \app\common\Member::addcommission($aid, $ogdata['parent3'], $mid, $ogdata['parent3commission'], '下三级购买奖励');
        }

        // 提升等级
        if (!empty($ogdata['parent1'])) {
            \app\common\Member::uplv($aid, $ogdata['parent1']);
        }
    }

    // 处理商家货款
    if ($order['bid'] != 0) { // 入驻商家的货款
        $binfo = Db::name('business')->where('aid', $aid)->where('id', $order['bid'])->find();
        if ($binfo) {
            $totalmoney = $order['money'] - $totalcommission;
            if ($totalmoney > 0) {
                $totalmoney = $totalmoney * (100 - $binfo['feepercent']) * 0.01;
            }
            \app\common\Business::addmoney($aid, $order['bid'], $totalmoney, '表单支付 订单号：' . $order['ordernum']);
        }
    }

    // 处理发货信息录入（微信小程序+微信支付）
    if ($order['platform'] == 'wx' && $order['paytypeid'] == 2) {
        \app\common\Order::wxShipping($order['aid'], $order, 'form');
    }

    // 检查并处理余额充值逻辑
    if ($form['jinruyue'] == 1) { // 判断是否开启进入余额
        // 确认支付是否成功
        if ($order['paytypeid'] == 2) { // 假设 status=1 表示支付成功
            // 添加到用户余额
            
              \app\common\Member::addmoney($order['aid'],$mid,$order['money'],'表单充值');
              \app\common\Member::addscore($order['aid'],$mid,$order['money'],'表单充值');
            // 记录余额变动日志（假设有一个 balance_log 表）
     
        }
    }

    // 发送模板消息
    $tmplcontent = [];
    $tmplcontent['first'] = '有客户提交表单成功';
    $tmplcontent['remark'] = '点击查看详情~';
    $tmplcontent['keyword1'] = $order['title'];
    $tmplcontent['keyword2'] = date('Y-m-d H:i');
    \app\common\Wechat::sendhttmpl(
        $aid,
        $order['bid'],
        'tmpl_formsub',
        $tmplcontent,
        m_url('admin/form/formdetail?id=' . $orderid, $aid)
    );

    // 发送新订单通知给管理员
    \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
        'consignee' => $order['consignee'],
        'phone' => $order['tel'],
        'ordernum' => $order['ordernum'],
        'amount' => $order['money']
    ]);

    // 可选：返回或记录支付处理结果
}

	//工单支付
	public static function workorder_pay($orderid){
		$order = Db::name('workorder_order')->where('id',$orderid)->find();
		$aid = $order['aid'];

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'workorder');
        }

		$tmplcontent = [];
		$tmplcontent['first'] = '有客户提交工单成功';
		$tmplcontent['remark'] = '请进入电脑端后台进行查看~';
		$tmplcontent['keyword1'] = $order['title'];
		$tmplcontent['keyword2'] = date('Y-m-d H:i');
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_formsub',$tmplcontent);
	}

	//付费查看页面
	public static function designerpage_pay($orderid){
		$order = Db::name('designerpage_order')->where('id',$orderid)->find();
		\app\common\Order::collect($order,'designerpage');
        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'designerpage');
        }
	}
	//购买优惠券
	public static function coupon_pay($orderid){
		$order = Db::name('coupon_order')->where('id',$orderid)->find();
		\app\common\Coupon::send($order['aid'],$order['mid'],$order['cpid']);
		\app\common\Order::collect($order,'coupon');
//发货信息录入 微信小程序+微信支付
if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
	\app\common\Order::wxShipping($order['aid'],$order,'coupon');
}
	}
// 买单
public static function maidan_pay($orderid){
    // 开启事务
    Db::startTrans();
    try {
        // 获取买单订单信息
        $order = Db::name('maidan_order')->where('id', $orderid)->find();
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        // 获取会员信息
        $member = Db::name('member')->where('id', $order['mid'])->find();
        if (!$member) {
            throw new \Exception('会员信息不存在');
        }

        $aid = $order['aid'];
        $mid = $order['mid'];
        $bid = $order['bid'];

        // 修复：使用原始金额（包含抵扣部分）来计算奖励，而不是只用实际支付金额
        $originalAmount = $order['money'] ?? 0; // 原始金额
        $payAmount = $order['paymoney'] ?? 0; // 实际支付金额
        $disprice = $order['disprice'] ?? 0; // 会员折扣
        $scoredk = $order['scoredk'] ?? 0; // 积分抵扣
        $couponmoney = $order['couponmoney'] ?? 0; // 优惠券抵扣
        $moneydkmoney = $order['moneydkmoney'] ?? 0; // 余额抵扣

        // 计算总抵扣金额（包含所有抵扣方式）
        $totalDeduction = $disprice + $scoredk + $couponmoney + $moneydkmoney;

        // 验证：原始金额应该等于实际支付金额加上总抵扣金额
        $calculatedOriginal = $payAmount + $totalDeduction;

        // 获取商户系统设置
        $sysset = Db::name('business_sysset')->where('aid', $aid)->find();

        // 获取商户信息（用于获取抽佣费率）
        $business = Db::name('business')->where('aid', $aid)->where('id', $bid)->find();
        $businessFeepercent = $business ? $business['feepercent'] : 0; // 商户抽佣费率

        // 确定奖励计算基数 - 使用原始金额（包含抵扣部分）
        $rewardBase = $originalAmount; // 默认按流水百分比计算，使用原始金额

        // 如果设置为按抽佣百分比计算（值为2）
        if ($sysset && $sysset['maidan_reward_type'] == 2) {
            $rewardBase = $originalAmount * $businessFeepercent / 100; // 使用原始金额计算抽佣金额作为基数
        }
        
        // 记录奖励计算日志
        $rewardType = ($sysset && $sysset['maidan_reward_type'] == 2) ? '抽佣百分比' : '流水百分比';
        $logDetail = "买单奖励计算详情：原始金额={$originalAmount}，实际支付={$payAmount}，会员折扣={$disprice}，积分抵扣={$scoredk}，优惠券抵扣={$couponmoney}，余额抵扣={$moneydkmoney}，总抵扣={$totalDeduction}，计算方式={$rewardType}";
        if ($sysset && $sysset['maidan_reward_type'] == 2) {
            $logDetail .= "，商户抽佣费率={$businessFeepercent}%，奖励基数={$rewardBase}";
        } else {
            $logDetail .= "，奖励基数={$rewardBase}";
        }
        \think\facade\Log::write('买单奖励计算：' . $logDetail);
        
        // 给买单用户发放贡献值和黄积分
        if ($sysset && $sysset['maidan_user_contribution_percent'] > 0) {
            $userContribution = $rewardBase * $sysset['maidan_user_contribution_percent'] / 100;
            if ($userContribution > 0) {
                // 使用通用函数增加用户贡献值
                \app\common\Member::addgongxianzhi($aid, $mid, $userContribution, '买单'.t('贡献值').'奖励，订单号：' . $order['ordernum'] . '，计算方式：' . $rewardType, $orderid);
            }
        }
        
        if ($sysset && $sysset['maidan_user_yellow_score_percent'] > 0) {
            $userYellowScore = $rewardBase * $sysset['maidan_user_yellow_score_percent'] / 100;
            if ($userYellowScore > 0) {
                // 使用通用函数增加用户黄积分
                \app\common\Member::addscorehuang($aid, $mid, $userYellowScore, '买单'.t('黄积分').'奖励，订单号：' . $order['ordernum'] . '，计算方式：' . $rewardType, '买单奖励');
            }
        }
        
        // 给商家管理员发放贡献值和黄积分
        if ($bid > 0) {
            // 查找商家的管理员ID
            $business = Db::name('business')->where('aid', $aid)->where('id', $bid)->find();
            if ($business && $business['mid2'] > 0) {
                $adminMid = $business['mid2'];
                
                // 给商家管理员发放贡献值
                if ($sysset && $sysset['maidan_admin_contribution_percent'] > 0) {
                    $adminContribution = $rewardBase * $sysset['maidan_admin_contribution_percent'] / 100;
                    if ($adminContribution > 0) {
                        // 使用通用函数增加商家管理员贡献值
                        \app\common\Member::addgongxianzhi($aid, $adminMid, $adminContribution, '买单管理员'.t('贡献值').'奖励，订单号：' . $order['ordernum'] . '，计算方式：' . $rewardType, $orderid);
                    }
                }
                
                // 给商家管理员发放黄积分
                if ($sysset && $sysset['maidan_admin_yellow_score_percent'] > 0) {
                    $adminYellowScore = $rewardBase * $sysset['maidan_admin_yellow_score_percent'] / 100;
                    if ($adminYellowScore > 0) {
                        // 使用通用函数增加商家管理员黄积分
                        \app\common\Member::addscorehuang($aid, $adminMid, $adminYellowScore, '买单管理员'.t('黄积分').'奖励，订单号：' . $order['ordernum'] . '，计算方式：' . $rewardType, '买单管理员奖励');
                    }
                }
            }
        }

        // 处理优惠券
        if($order['couponrid']){
            Db::name('coupon_record')->where('id', $order['couponrid'])->update(['status' => 1, 'usetime' => time()]);
        }

        // 收集订单
        \app\common\Order::collect($order, 'maidan');

        // 去排队
        if($order['paidui_jiang'] > 0 || $order['paidui_give'] > 0){
            $buss = Db::name('paidui_maidan_set')->where('aid', $order['aid'])->where('bid', 0)->find();
            if($order['bid'] > 0){
                $buss = Db::name('paidui_maidan_set')->where('aid', $order['aid'])->where('bid', $order['bid'])->find();
            }

         

            if($buss['type_status'] == 1){
                $paiduidata = [
                    'aid' => intval($order['aid']),
                    'mid' => intval($order['mid']),
                    'bid' => intval($order['bid']),
                    'type_status' => intval($buss['type_status']),
                    'type' => intval($buss['type']),
                    'orderid' => intval($order['id']),
                    'ordergoodid' => 0,
                    'paidui_jiang' => number_format($order['paidui_jiang'], 5, '.', ''),
                    'paidui_give' => number_format($order['paidui_give'], 5, '.', ''),
                    'give_shengyu' => number_format($order['paidui_jiang'], 5, '.', ''),
                    'shiji_dedao' => 0.00000,
                    'createtime' => time(),
                    'status' => 0,
                    'qu' => 1,
                    'jifen' => 0.00000,
                    'qutime' => 0,
                    'yue' => 0.00000,
                    'ischoujiang' => 0
                ];

             
                
                try {
                    $paiduiId = Db::name('paidui_list')->insertGetId($paiduidata);

                    // 检查是否启用分期模式，如果启用则创建分期记录
                    if($paiduiId && \app\common\PaiduiPeriods::isPeriodsEnabled($order['aid'])) {
                        $periods_result = \app\common\PaiduiPeriods::createPeriods(
                            $order['aid'], 
                            $paiduiId, 
                            $order['mid'], 
                            $order['bid'], 
                            $order['paidui_jiang']
                        );
                        
                        if($periods_result['status'] == 1) {
                            \think\facade\Log::write("排队分期创建成功: 排队ID={$paiduiId}, 分期数={$periods_result['periods']}, 总金额={$periods_result['total_amount']}");
                        } else {
                            \think\facade\Log::error("排队分期创建失败: 排队ID={$paiduiId}, 错误信息={$periods_result['msg']}");
                        }
                    }

                    if($buss['model'] == 1){
                      
                        \app\common\Member::givepaiduijiangModel($order['aid'], $order['mid'], $paiduiId, $order['ordernum'], $buss);
                    }
                    else{
                       
                        // 排队给奖励
                        \app\common\Member::givepaiduijiang($order['aid'], $order['mid'], $order['id'], 0, $order['ordernum'], 1);
                    }
                } catch (\Exception $e) {
                   
                    throw $e;
                }
            }
        }

        // 提交事务前再次验证数据
        if(isset($paiduiId)) {
            $finalCheck = Db::name('paidui_list')->where('id', $paiduiId)->find();
          
        }

        // 处理城市代理佣金（买单支付成功后）
        try {
            \app\common\CityAgent::onOrderPaid($order, 'maidan');
        } catch (\Exception $e) {
            \think\facade\Log::write('买单城市代理佣金处理失败:' . $e->getMessage());
            // 不影响主流程，只记录日志
        }

        // 提交事务
        Db::commit();

        // 事务提交成功后，发送云喇叭播报通知
        try {
            \app\model\CloudSpeaker::sendMaidanPayNotification($order);
        } catch (\Exception $e) {
            // 云喇叭播报失败不影响主流程，只记录日志
            \think\facade\Log::write('云喇叭播报失败：' . $e->getMessage());
        }

    } catch(\Exception $e) {
        // 回滚事务
        Db::rollback();

        // 记录完整的错误堆栈

        throw $e;
    }
}

	//餐饮订单合并支付
	public static function restaurant_takeaway_hb_pay($orderid,$ordernum){
		$orderlist = Db::name('restaurant_takeaway_order')->where('ordernum','like',$ordernum.'%')->select()->toArray();
		foreach($orderlist as $order){
			self::restaurant_takeaway_pay($order['id']);
		}
	}
	//餐饮订单
	public static function restaurant_takeaway_pay($orderid){
		$order = Db::name('restaurant_takeaway_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		Db::name('restaurant_takeaway_order_goods')->where('orderid',$orderid)->update(['status'=>1]);
		$takeaway_set = Db::name('restaurant_takeaway_sysset')->where('aid',$aid)->where('bid',$order['bid'])->find();
		if($takeaway_set['confirm_auto']==1){ //自动接单
			Db::name('restaurant_takeaway_order')->where('id',$orderid)->update(['status'=>12]);
			Db::name('restaurant_takeaway_order_goods')->where('orderid',$orderid)->update(['status'=>12]);
		}

		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'restaurant',$order['totalprice'],$order['id']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}

		\app\common\Member::uplv($aid,$mid);
        \app\custom\Restaurant::print('restaurant_takeaway', $order);

        if($takeaway_set['confirm_auto']==1){ //自动接单
            //判断是否自动派单
            if($order['freight_type'] == 2){
                $peisong_set = \db('peisong_set')->where('aid',$aid)->find();
                if($peisong_set['express_wx_status'] == 1 && $peisong_set['express_wx_paidan'] == 1){
                    Db::name('restaurant_takeaway_order')->where('id',$orderid)->update(['express_type'=>'express_wx']);
                    \app\custom\ExpressWx::addOrder('restaurant_takeaway_order',$order);
                }
            }
        }

		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/restaurant/takeawayorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('restaurant/takeaway/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/restaurant/takeawayorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}
    //餐饮订单
    public static function restaurant_booking_pay($orderid){
        $order = Db::name('restaurant_booking_order')->where('id',$orderid)->find();
        $member = Db::name('member')->where('id',$order['mid'])->find();
        $aid = $order['aid'];
        $mid = $order['mid'];
        Db::name('restaurant_booking_order_goods')->where('orderid',$orderid)->update(['status'=>1]);

        //支付后送券
        $couponlist = \app\common\Coupon::getpaygive($aid,$mid,'restaurant',$order['totalprice'],$order['id']);
        if($couponlist){
            foreach($couponlist as $coupon){
                \app\common\Coupon::send($aid,$mid,$coupon['id']);
            }
        }

        \app\common\Member::uplv($aid,$mid);
        \app\custom\Restaurant::print('restaurant_booking', $order,'','',1);

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'restaurant_booking');
        }

        //公众号通知 订单支付成功
        $tmplcontent = [];
        $tmplcontent['first'] = '有新预定支付成功';
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $member['nickname']; //用户名
        $tmplcontent['keyword2'] = $order['ordernum'];//订单号
        $tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
        $tmplcontent['keyword4'] = $order['title'];//商品信息
        \app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/restaurant/bookingorder', $aid),$order['mdid']);
        $tmplcontent['first'] = '恭喜您的预定订单已支付成功';
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('restaurant/booking/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/restaurant/bookingorder',$order['mdid']);

        //短信通知
        $rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
    }

    //餐饮订单
    public static function restaurant_shop_pay($orderid){
        $order = Db::name('restaurant_shop_order')->where('id',$orderid)->find();
        $member = Db::name('member')->where('id',$order['mid'])->find();
        $aid = $order['aid'];
        $bid = $order['bid'];
        $mid = $order['mid'];
        Db::name('restaurant_shop_order_goods')->where('orderid',$orderid)->update(['status'=>1]);

        //支付后送券
        $couponlist = \app\common\Coupon::getpaygive($aid,$mid,'restaurant',$order['totalprice'],$order['id']);
        if($couponlist){
            foreach($couponlist as $coupon){
                \app\common\Coupon::send($aid,$mid,$coupon['id']);
            }
        }

        \app\common\Member::uplv($aid,$mid);
        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'restaurant_shop');
        }

        //根据餐后付款设置，开启时下单后打印小票，关闭时付款后打印小票
        $restaurant_shop_sysset = Db::name('restaurant_shop_sysset')->where('aid', $aid)->where('bid', $bid)->find();
        if($restaurant_shop_sysset['pay_after'] == 0)
            \app\custom\Restaurant::print('restaurant_shop',$order);

        //公众号通知 订单支付成功
        $tmplcontent = [];
        if($order['paytypeid'] != 4) {
            $tmplcontent['first'] = '有新点餐订单支付成功';
        } else {
            $tmplcontent['first'] = '有新点餐订单下单成功（线下支付），';
        }
        $tmplcontent['remark'] = '点击进入查看~';
        $tmplcontent['keyword1'] = $member['nickname']; //用户名
        $tmplcontent['keyword2'] = $order['ordernum'];//订单号
        $tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
        $tmplcontent['keyword4'] = $order['title'];//商品信息
        \app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/restaurant/shoporder', $aid),$order['mdid']);
        if($order['paytypeid'] != 4) {
            $tmplcontent['first'] = '恭喜您的点餐订单已支付成功';
        } else {
            $tmplcontent['first'] = '恭喜您的点餐订单下单成功';
        }
        $rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('restaurant/shop/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/restaurant/shoporder',$order['mdid']);

        //短信通知
        $rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
    }



    	/**
	 * 活动报名订单支付处理
	 * @param int $orderid 订单ID
	 * @return array
	 */
	public static function huodong_baoming_pay($orderid){
		$order = Db::name('huodong_baoming_order')->where('id',$orderid)->find();
		if(!$order) return ['status'=>0,'msg'=>'订单不存在'];
		if($order['status'] != 0) return ['status'=>0,'msg'=>'订单状态不符合'];
		
		$aid = $order['aid'];
		
		// 更新销量
		Db::name('huodong_baoming_product')->where('aid',$aid)->where('id',$order['proid'])->update(['sales'=>Db::raw("sales+".$order['num'])]);
		
		// 赠送积分
		if($order['givescore'] > 0){
			\app\common\Member::addscore($aid,$order['mid'],$order['givescore'],'参与活动赠送'.t('积分'));
		}
		
		// 发货信息录入 微信小程序+微信支付
		if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
			\app\common\Order::wxShipping($aid,$order,'huodong_baoming');
		}
		
		return ['status'=>1,'msg'=>''];
	}

// 订票
public static function theater_pay($orderid){
    return true;
}
    // 订场
    public static function venues_pay($orderid){
    	return true;
    }

     // 约场
    public static function venues_appointment_pay($orderid){
    	$appointmentInfo = Db::name('venues_appointment')->alias('a')->field('a.*')
    		->join('venues_appointment_order o', 'o.appointment_id=a.id')
    		->where('o.id', $orderid)->find();
    	if(!empty($appointmentInfo)){
    		// 是否已够人成团
    		$joinCount = Db::name('venues_appointment_order')->where('aid', aid)->where('appointment_id', $appointmentInfo['id'])->where('status', 1)->count();
    		if($joinCount == $appointmentInfo['application']){
    			Db::name('venues_appointment')->where('id', $appointmentInfo['id'])->update(['status' => 1]);
    		}
    	}
    	return true;
    }
    
	public static function plug_businessqr_pay_pay($orderid){
        $order = Db::name('plug_businessqr_pay_order')->where('id',$orderid)->find();
        $member = Db::name('member')->where('id',$order['mid'])->find();
        $aid = $order['aid'];
		if($order['bid']!=0){//入驻商家的货款
			$binfo = Db::name('business')->where('aid',$aid)->where('id',$order['bid'])->find();
			$totalmoney = $order['cost_price'];
			\app\common\Business::addmoney($aid,$order['bid'],$totalmoney,'会员支付，订单号：'.$order['ordernum']);
			//店铺加销量
			Db::name('business')->where('aid',$aid)->where('id',$order['bid'])->inc('sales',1)->update();

			//公众号通知 订单支付成功
			$tmplcontent = [];
			$tmplcontent['first'] = '有新会员支付订单支付成功';
			$tmplcontent['remark'] = '点击进入查看~';
			$tmplcontent['keyword1'] = $member['nickname']; //用户名
			$tmplcontent['keyword2'] = $order['ordernum'];//订单号
			$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
			$tmplcontent['keyword4'] = $order['title'];//商品信息
			\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/finance/bmoneylog', $aid));
			$tmplcontent['first'] = '恭喜您已支付成功';
			$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('pages/money/moneylog?st=1', $aid));

			$tmplcontent = [];
			$tmplcontent['thing11'] = $order['title'];
			$tmplcontent['character_string2'] = $order['ordernum'];
			$tmplcontent['phrase10'] = '已支付';
			$tmplcontent['amount13'] = $order['totalprice'].'元';
			$tmplcontent['thing27'] = $member['nickname'];
			\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/finance/bmoneylog');

            // 发送新订单通知给管理员
            \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
                'consignee' => $binfo['name'],
                'phone' => $binfo['tel'],
                'ordernum' => $order['ordernum'],
                'amount' => $order['totalprice']
            ]);
		}
	}
	//预约服务支付
	public static function yuyue_pay($orderid){
			//var_dump($orderid);
		   $order = Db::name('yuyue_order')->where('id',$orderid)->find();
		   $aid = $order['aid'];
		   $member = Db::name('member')->where('id',$order['mid'])->find();
			if(!$order) return json(['status'=>0,'msg'=>'订单不存在']);
			if($order['status']!=1 && $order['status']!=12) return json(['status'=>0,'msg'=>'订单状态不符合']);
			if(getcustom('hmy_yuyue') && in_array($order['paidan_type'],[2,3])){ //定制 将订单同步到师傅app端
				\app\custom\Yuyue::apiyuyue($order);
			}else{
				$yyset = Db::name('yuyue_set')->where('aid',$aid)->find();
				if($yyset['paidantype']==0 && $yyset['isautopd']==1){   //自动派单到大厅
					$rs = \app\model\YuyueWorkerOrder::create($order,0,'');
				}
				if($order['worker_id']){
					//如果用户已经选择服务人员则支付后直接进行派单
					$rs = \app\model\YuyueWorkerOrder::create($order,$order['worker_id'],'');
					if($rs['status']==0) return json($rs);
					//\app\common\System::plog('预约派单'.$orderid);
				}
			}
			 //发货信息录入 微信小程序+微信支付
			 if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
				\app\common\Order::wxShipping($order['aid'],$order,'yuyue');
			}
			//公众号通知 订单支付成功
			$tmplcontent = [];
			$tmplcontent['first'] = '有新预约订单支付成功';
			$tmplcontent['remark'] = '点击进入查看~';
			$tmplcontent['keyword1'] = $member['nickname']; //用户名
			$tmplcontent['keyword2'] = $order['ordernum'];//订单号
			$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
			$tmplcontent['keyword4'] = $order['title'];//商品信息
			\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/yuyueorder', $aid),$order['mdid']);
			$tmplcontent['first'] = '恭喜您的订单已支付成功';
			$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('activity/yuyue/orderlist', $aid));

			$tmplcontent = [];
			$tmplcontent['thing11'] = $order['title'];
			$tmplcontent['character_string2'] = $order['ordernum'];
			$tmplcontent['phrase10'] = '已支付';
			$tmplcontent['amount13'] = $order['totalprice'].'元';
			$tmplcontent['thing27'] = $member['nickname'];
			\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/yuyueorder',$order['mdid']);

			//短信通知
			$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

            // 发送新订单通知给管理员
            \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
                'consignee' => $order['consignee'],
                'phone' => $order['tel'],
                'ordernum' => $order['ordernum'],
                'amount' => $order['totalprice']
            ]);
	}

//酒店订单
public static function hotel_pay($orderid){
    $order = Db::name('hotel_order')->where('id',$orderid)->find();
    $member = Db::name('member')->where('id',$order['mid'])->find();
    $aid = $order['aid'];
    $mid = $order['mid'];
    if($order['use_money']>0){
        \app\common\Member::addmoney($aid,$mid,-$order['use_money'],t('余额').'抵扣，订单号: '.$order['ordernum']);
    }
    $room = Db::name('hotel_room')->where('id',$order['roomid'])->find();
    //增加押金记录
    if($order['yajin_money']>0){
        $yjdata = [];
        $yjdata['aid'] = $order['aid'];
        $yjdata['bid'] = $order['bid'];
        $yjdata['mid'] = $order['mid'];
        $yjdata['orderid'] = $order['id'];
        $yjdata['ordernum'] = $order['ordernum'];
        $yjdata['yajin_money'] = $order['yajin_money'];
        $yjdata['yajin_type'] = $order['yajin_type'];
        $yjdata['refund_money'] = $order['yajin_money'];
        $yjdata['refund_status'] = 0;
        $yjdata['refund_ordernum'] = '' . date('ymdHis') . rand(100000, 999999);
        //$yjdata['apply_time'] = time();
        $yjdata['yd_num'] = $order['totalnum']; //预定人数
        $yajinid = Db::name('hotel_order_yajin')->insertGetId($yjdata);
        //修改关联的押金订单
        Db::name('hotel_order')->where('id',$orderid)->update(['yajin_orderid'=>$yajinid]);
    }
    $couponlist = \app\common\Coupon::getpaygive($aid,$mid,'hotel',$order['totalprice']);
   
    if($couponlist){
        foreach($couponlist as $coupon){
            \app\common\Coupon::send($aid,$mid,$coupon['id']);
        }
    }
    //发货信息录入 微信小程序+微信支付
    if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
        \app\common\Order::wxShipping($order['aid'],$order,'hotel');
    }
    //是否为即时确认
    if($room['qrtype']==1){
        Db::name('hotel_order')->where('id',$orderid)->update(['status'=>2,'confirm_time'=>time()]);
        //发送消息通知
        \app\model\Hotel::sendNotice($aid,$order);
    }
    //加销量
    \app\model\Hotel::addroomsales($order,$order['totalnum']);
    if(getcustom('yx_queue_free_hotel',$aid)){
        \app\custom\QueueFree::join($order,'hotel');
    }
    //短信通知
    $rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

    
}

	//预约尾款支付
	public static function yuyue_balance_pay($orderid){
		//var_dump($orderid);
	    $order = Db::name('yuyue_order')->where('id',$orderid)->find();
		$aid = $order['aid'];
        $member = Db::name('member')->where('id',$order['mid'])->find();
		if(!$order) return json(['status'=>0,'msg'=>'订单不存在']);
		if($order['balance_pay_status']!=1) return json(['status'=>0,'msg'=>'订单状态不符合']);
		if(getcustom('hmy_yuyue') && $order['sysOrderNo']){ //将订单同步到师傅app端
			//直接完成订单
			db('yuyue_order')->where(['aid'=>$order['aid'],'id'=>$orderid])->update(['status'=>3,'collect_time'=>time()]);
			$rs = \app\common\Order::collect($order,'yuyue');
			Db::name('yuyue_worker_order')->where('id',$order['worker_orderid'])->update(['status'=>3,'endtime'=>time()]);
			\app\custom\Yuyue::payoff($order);
		}
		
        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'yuyue');
        }
		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '尾款订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/yuyueorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('activity/yuyue/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/yuyueorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}


 /**
     * 套餐订单支付成功回调
     * @param int $orderid 套餐订单ID (yuyue_package_order表的ID)
     */
    public static function yuyue_package_pay($orderid){
        $order = Db::name('yuyue_package_order')->where('id', $orderid)->find();
        
        if(!$order){
            Log::error('套餐支付回调失败：订单不存在，ID: ' . $orderid);
            return;
        }
        
        // 检查订单状态是否为待支付
        if($order['status'] != 0){
            Log::warning('套餐支付回调：订单状态不正确或已处理，ID: ' . $orderid . ', Status: ' . $order['status']);
            return;
        }
        
        // 计算过期时间
        $expires_time = time() + $order['valid_days'] * 86400; 
        
        // 更新订单状态
        $update_data = [
            'status' => 1, // 已支付
            'pay_time' => time(),
            'expires_time' => $expires_time,
            'updatetime' => time()
        ];
        $res = Db::name('yuyue_package_order')->where('id', $orderid)->update($update_data);
        
        if($res !== false) {
            // 增加套餐销量
            Db::name('yuyue_package')
                ->where('id', $order['package_id'])
                ->inc('sales')
                ->update();
                
            // 发送购买成功通知给用户
            try {
                $tmplcontent = [];
                $tmplcontent['first'] = '您的服务套餐[' . $order['package_name'] . ']购买成功';
                $tmplcontent['keyword1'] = $order['package_name']; // 套餐名称
                $tmplcontent['keyword2'] = '￥'.$order['total_price']; // 支付金额
                $tmplcontent['keyword3'] = date('Y-m-d H:i:s', $update_data['pay_time']); // 支付时间
                $tmplcontent['keyword4'] = $order['ordernum']; // 订单编号
                $tmplcontent['remark'] = '套餐已生效，有效期至：'.date('Y-m-d', $expires_time).'。请在有效期内使用。';
                
                \app\common\Wechat::sendtmpl($order['aid'], $order['mid'], 'tmpl_ordersucc', $tmplcontent, m_url('pages/my/packageorderlist')); // 跳转到套餐订单列表
            } catch (\Exception $e) {
                Log::error('发送套餐购买成功通知失败：orderid=' . $orderid . ', error=' . $e->getMessage());
            }

            // 可在此处添加分销、积分等逻辑（如果套餐参与）
            // ...

        } else {
            Log::error('更新套餐订单状态失败，ID: ' . $orderid);
        }
    }
    public static function periodicservice_pay($orderid){ // $orderid 是 periodicservice_order 表的 id
        // 日志记录：开始处理周期服务支付回调的特定逻辑
        $logPrefixBase = '[Payorder]-[periodic_service_pay_'; // 日志基础前缀
        $log = function($level, $seq, $message) use ($logPrefixBase, $orderid) {
            $timestamp = date('Y-m-d H:i:s') . ',' . round(microtime(true) * 1000) % 1000;
            Log::$level("{$timestamp}-{$level}-{$logPrefixBase}{$seq}] {$message}, periodicservice_order_id: {$orderid}");
        };

        $log('INFO', '001', "开始处理周期服务特定逻辑");

        // 直接查询周期服务订单
        $order = Db::name('periodicservice_order')->where('id', $orderid)->find();
        if(!$order){
            $log('WARN', '002', "周期服务订单未找到");
            return;
        }

        // 检查订单支付状态
        if($order['pay_status'] != 1){
             $log('INFO', '003', "周期服务订单未支付或支付状态异常, pay_status: " . $order['pay_status']);
             return;
        }
    
        $log('INFO', '004', "周期服务订单状态检查通过");
    
        // --- 生成服务工单 (periodic_service_stage) ---
        $stagesData = [];
        $startDate = $order['start_date'];
        $totalPeriod = $order['total_period'] ?? 0; // 总期数
        $psCycle = $order['ps_cycle'] ?? 0; // 周期类型：1=周，2=月，5=按天间隔
        $psFrequency = $order['ps_frequency'] ?? 0; // 频率（周几/每月几号）
        $psDays = $order['ps_days'] ?? 0; // 天数间隔
        
        $log('INFO', '005', "开始计算工单日期, start_date: {$startDate}, total_period: {$totalPeriod}, ps_cycle: {$psCycle}, ps_frequency: {$psFrequency}, ps_days: {$psDays}");
        
        if (empty($startDate) || $totalPeriod <= 0) {
            $log('ERROR', '006', "开始日期为空或总期数无效");
            return;
        }

        $startDateTimestamp = strtotime($startDate);
        if ($startDateTimestamp === false) {
            $log('ERROR', '007', "无法解析开始日期: {$startDate}");
             return;
        }
    
        // 计算每期的服务日期
        for ($i = 1; $i <= $totalPeriod; $i++) {
            $currentDate = null;
            
            // 第一期的日期计算
            if ($i == 1) {
                $log('INFO', '008_1', "计算第1期日期");
                
                // 根据周期类型计算第一期日期
                if ($psCycle == 5) { // 按天数间隔
                    // 第一期就是开始日期
                    $currentDate = date('Y-m-d', $startDateTimestamp);
                    $log('INFO', '009_1', "按天数间隔, 第1期日期: {$currentDate}");
                } 
                else if ($psCycle == 1) { // 按周
                    // 确定开始日期是周几
                    $startDayOfWeek = date('N', $startDateTimestamp); // 1(周一)到7(周日)
                    
                    // 目标是周几
                    $targetDayOfWeek = $psFrequency;
                    
                    if ($startDayOfWeek == $targetDayOfWeek) {
                        // 如果开始日期刚好是目标周几，直接使用
                        $currentDate = date('Y-m-d', $startDateTimestamp);
        } else {
                        // 计算距离目标周几的天数
                        $daysUntilTarget = ($targetDayOfWeek - $startDayOfWeek + 7) % 7;
                        if ($daysUntilTarget == 0) $daysUntilTarget = 7; // 如果计算结果为0，则为下周同一天
                        $timestamp = strtotime("+{$daysUntilTarget} days", $startDateTimestamp);
                        $currentDate = date('Y-m-d', $timestamp);
                    }
                    $log('INFO', '009_1', "按周, 开始日期周{$startDayOfWeek}, 目标周{$targetDayOfWeek}, 第1期日期: {$currentDate}");
                } 
                else if ($psCycle == 2) { // 按月
                    // 获取开始日期的月和日
                    $startMonth = date('n', $startDateTimestamp);
                    $startDay = date('j', $startDateTimestamp);
                    $startYear = date('Y', $startDateTimestamp);
                    
                    // 目标日是每月几号
                    $targetDay = $psFrequency;
                    
                    // 检查当月是否还有这一天
                    $daysInMonth = date('t', $startDateTimestamp);
                    if ($targetDay > $daysInMonth) {
                        $targetDay = $daysInMonth; // 如果目标日大于当月天数，则使用月底
                    }
                    
                    // 如果开始日期已过目标日，则使用下个月的目标日
                    if ($startDay > $targetDay) {
                        $nextMonth = $startMonth == 12 ? 1 : $startMonth + 1;
                        $nextYear = $startMonth == 12 ? $startYear + 1 : $startYear;
                        $timestamp = strtotime("{$nextYear}-{$nextMonth}-{$targetDay}");
                        // 检查下个月是否有目标日
                        $daysInNextMonth = date('t', $timestamp);
                        if ($targetDay > $daysInNextMonth) {
                            $targetDay = $daysInNextMonth;
                            $timestamp = strtotime("{$nextYear}-{$nextMonth}-{$targetDay}");
                        }
                    } else {
                        // 使用当月目标日
                        $timestamp = strtotime("{$startYear}-{$startMonth}-{$targetDay}");
                    }
                    
                    $currentDate = date('Y-m-d', $timestamp);
                    $log('INFO', '009_1', "按月, 开始日期{$startDay}号, 目标{$targetDay}号, 第1期日期: {$currentDate}");
                }
            } 
            // 后续期数的日期计算
            else {
                $log('INFO', "008_{$i}", "计算第{$i}期日期");
                
                // 获取上一期的日期作为基准
                $prevDate = $stagesData[$i - 2]['scheduled_date'];
                $prevTimestamp = strtotime($prevDate);
                
                if ($psCycle == 5) { // 按天数间隔
                    $timestamp = strtotime("+{$psDays} days", $prevTimestamp);
                    $currentDate = date('Y-m-d', $timestamp);
                    $log('INFO', "009_{$i}", "按天数间隔{$psDays}天, 上期日期: {$prevDate}, 第{$i}期日期: {$currentDate}");
                } 
                else if ($psCycle == 1) { // 按周
                    $timestamp = strtotime("+7 days", $prevTimestamp);
                    $currentDate = date('Y-m-d', $timestamp);
                    $log('INFO', "009_{$i}", "按周, 上期日期: {$prevDate}, 第{$i}期日期: {$currentDate}");
                } 
                else if ($psCycle == 2) { // 按月
                    $prevDay = date('j', $prevTimestamp);
                    $prevMonth = date('n', $prevTimestamp);
                    $prevYear = date('Y', $prevTimestamp);
                    
                    // 确定下个月的月份和年份
                    $nextMonth = $prevMonth == 12 ? 1 : $prevMonth + 1;
                    $nextYear = $prevMonth == 12 ? $prevYear + 1 : $prevYear;
                    
                    // 目标日是每月几号
                    $targetDay = $psFrequency;
                    
                    // 检查下个月是否有目标日
                    $daysInNextMonth = date('t', strtotime("{$nextYear}-{$nextMonth}-01"));
                    if ($targetDay > $daysInNextMonth) {
                        $targetDay = $daysInNextMonth;
                    }
                    
                    $timestamp = strtotime("{$nextYear}-{$nextMonth}-{$targetDay}");
                    $currentDate = date('Y-m-d', $timestamp);
                    $log('INFO', "009_{$i}", "按月, 上期日期: {$prevDate}, 第{$i}期日期: {$currentDate}");
                }
            }
            
            // 如果无法计算出有效日期，跳过
            if (empty($currentDate)) {
                $log('ERROR', "010_{$i}", "无法计算第{$i}期日期, 跳过此期");
                continue;
            }
            
            // 添加工单数据
            $stagesData[] = [
                'aid' => $order['aid'],
                'bid' => $order['bid'],
                'order_id' => $order['id'],
                'ordernum' => $order['ordernum'],
                'mid' => $order['mid'],
                'product_id' => $order['product_id'],
                'product_name' => $order['product_name'],
                'period_num' => $i,
                'scheduled_date' => $currentDate,
                'status' => 0, // 待服务
                'createtime' => time(),
                'updatetime' => time(),
            ];
        }
        
        if(empty($stagesData)) {
            $log('ERROR', '011', "未生成任何工单数据");
            return;
        }
        
        $log('INFO', '012', "成功生成 " . count($stagesData) . " 条工单数据");
        
        // 检查是否已经生成过工单
        $existingStages = Db::name('periodic_service_stage')->where('order_id', $order['id'])->count();
        if ($existingStages > 0) {
            $log('INFO', '013', "该订单已生成工单，共 {$existingStages} 条，跳过生成步骤");
            return;
        }
        
        // 使用事务确保数据一致性
        Db::startTrans();
        try {
            // 批量插入工单数据
            $result = Db::name('periodic_service_stage')->insertAll($stagesData);
            if (!$result) {
                throw new \Exception("工单插入失败");
            }
            $log('INFO', '014', "批量插入工单成功, 共 " . count($stagesData) . " 条");
            
            // 更新主订单状态为服务中
            $updateData = [
                'status' => 1, // 服务中
                'remain_period' => $totalPeriod, // 剩余期数
                'served_period' => 0, // 已服务期数
                'next_service_date' => $stagesData[0]['scheduled_date'], // 下次服务日期
                'updatetime' => time()
            ];
            
            $updateResult = Db::name('periodicservice_order')->where('id', $order['id'])->update($updateData);
            if ($updateResult === false) {
                throw new \Exception("更新订单状态失败");
            }
            $log('INFO', '015', "更新订单状态为服务中成功, next_service_date: {$stagesData[0]['scheduled_date']}");
            
            Db::commit();
            $log('INFO', '016', "事务提交成功");
        } catch (\Exception $e) {
            Db::rollback();
            $log('ERROR', '017', "处理失败, 事务回滚: " . $e->getMessage());
            return;
        }
        
        // --- 后续操作 (积分、优惠券、分销、通知等) ---
        // 发送通知给客户
        try {
            // 获取会员信息
            $member = Db::name('member')->where('id', $order['mid'])->find();
            if ($member) {
                // 构建通知内容
                $noticeData = [
                    'type' => 'periodic_service_order',
                    'template' => 'order_paid',
                    'aid' => $order['aid'],
                    'bid' => $order['bid'],
                    'mid' => $order['mid'],
                    'orderid' => $order['id'],
                    'ordernum' => $order['ordernum'],
                    'createtime' => time(),
                    'data' => [
                        'nickname' => $member['nickname'],
                        'order_id' => $order['id'],
                        'order_sn' => $order['ordernum'],
                        'order_amount' => $order['total'],
                        'product_name' => $order['product_name'],
                        'service_time' => $stagesData[0]['scheduled_date'],
                        'total_period' => $totalPeriod
                    ]
                ];
                
                // 这里调用您系统中的通知发送方法
                // \app\common\service\Notice::send($noticeData);
                $log('INFO', '018', "发送订单支付成功通知");
            }
        } catch (\Exception $e) {
            $log('WARN', '019', "发送通知失败: " . $e->getMessage());
            // 通知失败不影响主流程
        }
        
        $log('INFO', '020', "周期服务特定逻辑处理完成");
    }
	//课程支付
	public static function kecheng_pay($orderid){
		//var_dump($orderid);
	    $order = Db::name('kecheng_order')->where('id',$orderid)->find();
        $aid = $order['aid'];
		//增加学习人数
		Db::name('kecheng_list')->where('aid',$order['aid'])->where('id',$order['kcid'])->inc('join_num')->update();
		if(!$order) return json(['status'=>0,'msg'=>'订单不存在']);
		if($order['status']!=1 && $order['status']!=12) return json(['status'=>0,'msg'=>'订单状态不符合']);
        $member = Db::name('member')->where('aid',$order['aid'])->where('id',$order['mid'])->find();

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'kecheng');
        }
		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新课程订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['price'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/kechengorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('activity/kecheng/orderlist?bid='.$order['bid'], $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/kechengorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);
	    \app\common\Order::collect($order,'kecheng');

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['price']
        ]);
	}

	//课程支付
	public static function exam_pay($orderid){
		//var_dump($orderid);
	    $order = Db::name('exam_order')->where('id',$orderid)->find();
        $aid = $order['aid'];
		//增加学习人数
		Db::name('exam_list')->where('aid',$order['aid'])->where('id',$order['kcid'])->inc('join_num')->update();
		if(!$order) return json(['status'=>0,'msg'=>'订单不存在']);
		if($order['status']!=1 && $order['status']!=12) return json(['status'=>0,'msg'=>'订单状态不符合']);
        $member = Db::name('member')->where('aid',$order['aid'])->where('id',$order['mid'])->find();

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'exam');
        }
		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新课程订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['price'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/kechengorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('activity/kecheng/orderlist?bid='.$order['bid'], $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/kechengorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);
	    \app\common\Order::collect($order,'kecheng');

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['price']
        ]);
	}


	//团购
	public static function tuangou_pay($orderid){
		$order = Db::name('tuangou_order')->where('id',$orderid)->find();
		$member = Db::name('member')->where('id',$order['mid'])->find();
		$aid = $order['aid'];
		$mid = $order['mid'];
		//自动发货
		if($order['freight_type']==3){
			Db::name('tuangou_order')->where('id',$order['id'])->update(['status'=>2,'send_time'=>time()]);
		}
		//在线卡密
		if($order['freight_type']==4){
			$codelist = Db::name('tuangou_codelist')->where('proid',$order['proid'])->where('status',0)->order('id')->limit($order['num'])->select()->toArray();
			if($codelist && count($codelist) >= $order['num']){
				$pscontent = [];
				foreach($codelist as $codeinfo){
					$pscontent[] = $codeinfo['content'];
					Db::name('tuangou_codelist')->where('id',$codeinfo['id'])->update(['orderid'=>$order['id'],'ordernum'=>$order['ordernum'],'headimg'=>$member['headimg'],'nickname'=>$member['nickname'],'buytime'=>time(),'status'=>1]);
				}
				$pscontent = implode("\r\n",$pscontent);
				Db::name('tuangou_order')->where('id',$order['id'])->update(['freight_content'=>$pscontent,'status'=>2,'send_time'=>time()]);
			}
			
            //发货信息录入 微信小程序+微信支付
            if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
                \app\common\Order::wxShipping($order['aid'],$order,'tuangou');
            }
		}
		//支付后送券
		$couponlist = \app\common\Coupon::getpaygive($aid,$mid,'tuangou',$order['totalprice']);
		if($couponlist){
			foreach($couponlist as $coupon){
				\app\common\Coupon::send($aid,$mid,$coupon['id']);
			}
		}
		\app\common\Wifiprint::print($aid,'tuangou',$order['id']);
		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新团购订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/tuangouorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$mid,'tmpl_orderpay',$tmplcontent,m_url('activity/tuangou/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/tuangouorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

		$set = Db::name('admin_set')->where('aid',$aid)->find();
		if($set['fxjiesuantime'] == 1 && $set['fxjiesuantime_delaydays'] == '0'){
			\app\common\Order::giveCommission($order,'tuangou');
		}

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}

	//约课服务支付
	public static function yueke_pay($orderid){
	   $order = Db::name('yueke_order')->where('id',$orderid)->find();
	   $aid = $order['aid'];
	   $member = Db::name('member')->where('id',$order['mid'])->find();
		if(!$order) return json(['status'=>0,'msg'=>'订单不存在']);
		if($order['status']!=1 && $order['status']!=12) return json(['status'=>0,'msg'=>'订单状态不符合']);
		$workerinfo = Db::name('yuyue_worker')->where('aid',$aid)->where('workerid',$order['workerid'])->find();

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'yueke');
        }

		//公众号通知 订单支付成功
		$tmplcontent = [];
		$tmplcontent['first'] = '有新预约订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['totalprice'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/yuekeorder', $aid),$order['mdid']);
		if($workerinfo && $workerinfo['mid']){
			$rs = \app\common\Wechat::sendtmpl($aid,$workerinfo['mid'],'tmpl_orderpay',$tmplcontent,m_url('pagesExt/yueke/workerorderlist', $aid));
		}
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('pagesExt/yueke/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['totalprice'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/yuekeorder',$order['mdid']);

		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['totalprice']
        ]);
	}

	public function business_recharge_pay($orderid){
		$order = Db::name('business_recharge_order')->where('id',$orderid)->find();
		$info = Db::name('business')->where('id',$order['bid'])->find();
		\app\common\Business::addmoney($order['aid'],$order['bid'],$order['money'],t('余额').'充值');

        //发货信息录入 微信小程序+微信支付
        if($order['platform'] == 'wx' && $order['paytypeid'] == 2){
            \app\common\Order::wxShipping($order['aid'],$order,'business_recharge');
        }
		//\app\common\System::plog('给商户充值'.$order['bid']);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $info['name'],
            'phone' => $info['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['money']
        ]);
	}
	public function yuyue_workerapply_pay($orderid){
		$order = Db::name('yuyue_workerapply_order')->where('id',$orderid)->find();
		$info = Db::name('yuyue_worker')->where('id',$order['bid'])->find();
		//入驻成功给管理员发通知
		$tmplcontent = [];
		$tmplcontent['first'] = '有师傅申请成功';
		$tmplcontent['remark'] = '请登录后台，查看申请详情~';
		$tmplcontent['keyword1'] = '预约师傅申请';
		$tmplcontent['keyword2'] = date('Y-m-d H:i');
		\app\common\Wechat::sendhttmpl(aid,$info['bid'],'tmpl_formsub',$tmplcontent,'');
	}


	//预约补余款支付
	public static function yuyue_addmoney_pay($orderid){
		//var_dump($orderid);
	    $order = Db::name('yuyue_order')->where('id',$orderid)->find();
		$aid = $order['aid'];
        $member = Db::name('member')->where('id',$order['mid'])->find();
		if(!$order) return json(['status'=>0,'msg'=>'订单不存在']);
		if($order['addmoneyStatus']!=1) return json(['status'=>0,'msg'=>'订单状态不符合']);


		//公众号通知 订单支付成功
		/*$tmplcontent = [];
		$tmplcontent['first'] = '补余款订单支付成功';
		$tmplcontent['remark'] = '点击进入查看~';
		$tmplcontent['keyword1'] = $member['nickname']; //用户名
		$tmplcontent['keyword2'] = $order['ordernum'];//订单号
		$tmplcontent['keyword3'] = $order['addmoney'].'元';//订单金额
		$tmplcontent['keyword4'] = $order['title'];//商品信息
		\app\common\Wechat::sendhttmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,m_url('admin/order/yuyueorder', $aid),$order['mdid']);
		$tmplcontent['first'] = '恭喜您的订单已支付成功';
		$rs = \app\common\Wechat::sendtmpl($aid,$order['mid'],'tmpl_orderpay',$tmplcontent,m_url('activity/yuyue/orderlist', $aid));

		$tmplcontent = [];
		$tmplcontent['thing11'] = $order['title'];
		$tmplcontent['character_string2'] = $order['ordernum'];
		$tmplcontent['phrase10'] = '已支付';
		$tmplcontent['amount13'] = $order['addmoney'].'元';
		$tmplcontent['thing27'] = $member['nickname'];
		\app\common\Wechat::sendhtwxtmpl($aid,$order['bid'],'tmpl_orderpay',$tmplcontent,'admin/order/yuyueorder',$order['mdid']);
		*/
		//短信通知
		$rs = \app\common\Sms::send($aid,$member['tel']?$member['tel']:$order['tel'],'tmpl_orderpay',['ordernum'=>$order['ordernum']]);

        // 发送新订单通知给管理员
        \app\common\Sms::sendToAdmin($aid, 'tmpl_neworder', [
            'consignee' => $order['consignee'],
            'phone' => $order['tel'],
            'ordernum' => $order['ordernum'],
            'amount' => $order['addmoney']
        ]);
	}
	//元宝 更新shop_order和payorder
    static private function yuanbao_up($type,$payorder){

        //元宝支付
        if(getcustom('pay_yuanbao') && $type == 'shop'){
            //如果是元宝支付，则需要重置支付金额
            if($payorder['is_yuanbao_pay'] == 1){
                //更新pay_order
                $up_pay = Db::name('payorder')->where('id',$payorder['id'])->update(['money'=>$payorder['yuanbao_money']]);

                //查询订单
                $order = Db::name('shop_order')->where('id',$payorder['orderid'])->field('aid,ordernum,mid,total_yuanbao')->find();
                if($order){
                    //更新shop_order
                    $up_order = Db::name('shop_order')->where('id',$payorder['orderid'])->update(['is_yuanbao_pay'=>1,'yuanbao_money'=>$payorder['yuanbao_money'],'totalprice'=>$payorder['yuanbao_money']]);
                    //更新商品id
                   	$sel_goods = Db::name('shop_order_goods')
                   		->where('orderid',$payorder['orderid'])
                   		->select()
                   		->toArray();
                   	if($sel_goods){
                   		//查询系统设置
                   		$sysset = Db::name('admin_set')->where('aid',$order['aid'])->find();
                   		//查询商城设置
                   		$shopset = Db::name('shop_sysset')->where('aid',$order['aid'])->field('showjd,comment,showcommission,hide_sales,hide_stock,show_lvupsavemoney')->find();
                   		//查询下单者
                   		$member  = Db::name('member')->where('id',$order['mid'])->find();
				        self::deal_commission($order['aid'],$order,$sel_goods,$sysset,$shopset,$member);
                   	}
                }
                //扣除元宝
                  \app\common\Member::addyuanbao($order['aid'],$order['mid'],-$order['total_yuanbao'],'支付订单：'.$order['ordernum']);
            }
        }
    }
    //元宝转账
    static private function member_yuanbao_transfer_pay($id){

        //元宝支付
        if(getcustom('pay_yuanbao')){

            //查询订单
            $order = Db::name('member_yuanbao_transfer_order')->where('id',$id)->field('aid,mid,to_mid,money,yuanbao,parent1,parent2,parent3,parent1commission,parent2commission,parent3commission,iscommission')->find();
            $aid = $order['aid'];
            if($order){
                $member     = Db::name('member')->where('id',$order['mid'])->field('nickname')->find();
                $to_member  = Db::name('member')->where('id',$order['to_mid'])->field('nickname')->find();
                if($member){
                    //直接转账
                    $rs = \app\common\Member::addyuanbao($aid,$order['to_mid'],$order['yuanbao'],sprintf("来自%s的".t('元宝')."转赠", $member["nickname"]));
                    if ($rs['status'] == 1) {
                        \app\common\Member::addyuanbao($aid,$order['mid'],$order['yuanbao'] * -1, sprintf(t('元宝')."转赠给：%s",$to_member['nickname']));
                    }
                }

                if($order['iscommission'] != 1){
                    $totalcommission = 0;
                    //发奖
                    if($order['parent1'] && $order['parent1commission'] > 0){
                        $totalcommission+=$order['parent1commission'];
                        \app\common\Member::addcommission($aid,$order['parent1'],$order['mid'],$order['parent1commission'],'下级'.t('元宝').'转账奖励');
                    }
                    if($order['parent2'] && $order['parent2commission'] > 0){
                        $totalcommission+=$order['parent2commission'];
                        \app\common\Member::addcommission($aid,$order['parent2'],$order['mid'],$order['parent2commission'],'下二级'.t('元宝').'转账奖励');
                    }
                    if($order['parent3'] && $order['parent3commission'] > 0){
                        $totalcommission+=$order['parent3commission'];
                        \app\common\Member::addcommission($aid,$order['parent3'],$order['mid'],$order['parent3commission'],'下三级'.t('元宝').'转账奖励');
                    }

                    //更新发佣金状态
                    $up = Db::name('member_yuanbao_transfer_order')->where('id',$id)->update(['iscommission'=>1]);
                }
            }
        }
    }
    //重新计算佣金
    static private function deal_commission($aid,$order,$sel_goods,$sysset,$shopset,$member){
        //元宝支付
        if(getcustom('pay_yuanbao')){
            if($sysset){
                $yuanbao_money_ratio = $sysset['yuanbao_money_ratio']/100;
            }else{
                $yuanbao_money_ratio  = 0;
            }
            foreach($sel_goods as $ogdata){

                //计算商品元宝现金价格
                $yuanbao_money = $ogdata['total_yuanbao']*$yuanbao_money_ratio;
                $yuanbao_money = round($yuanbao_money,2);
                //更新商品金额
                $up_goods = Db::name('shop_order_goods')->where('id',$ogdata['id'])->update(['yuanbao_money'=>$yuanbao_money,'totalprice'=>$yuanbao_money,'real_totalprice'=>$yuanbao_money]);


                //删除之前的会员佣金记录
                Db::name('member_commission_record')->where('orderid',$ogdata['orderid'])->where('ogid',$ogdata['id'])->delete();

                //查询规格
                $guige = Db::name('shop_guige')->where('aid',$aid)->where('id',$ogdata['ggid'])->find();

                //实际支付价格
                $og_totalprice = $yuanbao_money;

                //数量
                $num = $ogdata['num'];
                //佣金总价格
                $commission_totalprice   = 0;

                if($sysset['fxjiesuantype']==1){ //按成交价格
					$commission_totalprice = $yuanbao_money;
					if($commission_totalprice < 0){
						$commission_totalprice = 0;
					}
				}
				if($sysset['fxjiesuantype']==2){ //按销售利润
					$commission_totalprice = $yuanbao_money - $guige['cost_price'] * $num;
					if($commission_totalprice < 0) {
						$commission_totalprice = 0;
					}
				}
                $commission_totalpriceCache = $commission_totalprice;

                $ogupdate = [];
                $ogupdate['parent1'] = 0;
            	$ogupdate['parent2'] = 0;
            	$ogupdate['parent3'] = 0;
            	$ogupdate['parent4'] = 0;

            	$ogupdate['parent1commission'] = 0;
            	$ogupdate['parent2commission'] = 0;
            	$ogupdate['parent3commission'] = 0;
            	$ogupdate['parent4commission'] = 0;
                $ogupdate['fhparent2commission'] = 0;
                $ogupdate['fhparent3commission'] = 0;
                $ogupdate['fhparent4commission'] = 0;
                        if($parent1['pid']>0){
                            $parent2 = Db::name('member')->where('id',$parent1['pid'])->find();
                            if($parent2&&$ogupdate['parent1commission']>0){
                                $ogupdate['fhparent2commission'] = $ogupdate['parent1commission']*$agleveldata2['fh_commission2']%10;
                                if($parent2['pid']>0){
                                    $parent3 = Db::name('member')->where('id',$parent2['pid'])->find();
                                    if($parent3&&$ogupdate['parent1commission']>0){
                                        $ogupdate['fhparent3commission'] = $ogupdate['parent1commission']*$agleveldata3['fh_commission3']%10;
                                    }
                                    if($parent3['pid']>0){
                                        $parent4 = Db::name('member')->where('id',$parent3['pid'])->find();
                                        if($parent4&&$ogupdate['parent1commission']>0){
                                            $ogupdate['fhparent4commission'] = $ogupdate['parent1commission']*$agleveldata4['fh_commission4']%10;
                                        }
                                    }
                                }
                            }
                        }
            	$ogupdate['parent1score'] = 0;
            	$ogupdate['parent2score'] = 0;
            	$ogupdate['parent3score'] = 0;

            	$ogupdate['hongbaoEdu'] = 0;
            	$ogupdate['business_total_money'] = 0;
            	//自己是否拿一级分成
                $agleveldata = Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->find();
                if($agleveldata['can_agent'] > 0 && $agleveldata['commission1own']==1){
                    $member['pid'] = $ogdata['mid'];
                }

                //查询商品
                $product = Db::name('shop_product')->where('id',$ogdata['proid'])->find();

                if($product['bid'] > 0) {

                	$store_info = Db::name('business')->where('aid',$aid)->where('id',$product['bid'])->find();

                    $totalprice_business = $og_totalprice;
                    //商品独立费率
                    if($product['feepercent'] != '' && $product['feepercent'] != null && $product['feepercent'] >= 0) {
                        $ogupdate['business_total_money'] = $totalprice_business * (100-$product['feepercent']) * 0.01;
                    } else {
                        //商户费率
                        $ogupdate['business_total_money'] = $totalprice_business * (100-$store_info['feepercent']) * 0.01;
                    }

                }

                if($product['commissionset']!=-1){

                    if($member['pid']){
                        $parent1 = Db::name('member')->where('aid',$aid)->where('id',$member['pid'])->find();
                        if($parent1){
                            $agleveldata1 = Db::name('member_level')->where('aid',$aid)->where('id',$parent1['levelid'])->find();
                            if($agleveldata1['can_agent']!=0){
                                $ogupdate['parent1'] = $parent1['id'];
                            }
                        }
                    }
                    if($parent1['pid']>0){
                        $parent2 = Db::name('member')->where('aid',$aid)->where('id',$parent1['pid'])->find();
                        if($parent2){
                            $agleveldata2 = Db::name('member_level')->where('aid',$aid)->where('id',$parent2['levelid'])->find();
                            if($agleveldata2['can_agent']>1){
                                $ogupdate['parent2'] = $parent2['id'];
                            }
                        }
                    }
                    if($parent2['pid']>0){
                        $parent3 = Db::name('member')->where('aid',$aid)->where('id',$parent2['pid'])->find();
                        if($parent3){
                            $agleveldata3 = Db::name('member_level')->where('aid',$aid)->where('id',$parent3['levelid'])->find();
                            if($agleveldata3['can_agent']>2){
                                $ogupdate['parent3'] = $parent3['id'];
                            }
                        }
                    }
                    if($parent3['pid']>0){
                        $parent4 = Db::name('member')->where('aid',$aid)->where('id',$parent3['pid'])->find();
                        if($parent4){
                            $agleveldata4 = Db::name('member_level')->where('aid',$aid)->where('id',$parent4['levelid'])->find();
                            //持续推荐奖励
                            if($agleveldata4['can_agent'] > 0 && ($agleveldata4['commission_parent'] > 0 || ($parent4['levelid']==$parent3['levelid'] && $agleveldata4['commission_parent_pj'] > 0))){
                                $ogupdate['parent4'] = $parent4['id'];
                            }
                        }
                    }
                    if($product['commissionset']==1){//按商品设置的分销比例
                        $commissiondata = json_decode($product['commissiondata1'],true);
                        if($commissiondata){
                            if($agleveldata1) $ogupdate['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $commission_totalprice * 0.01;
                            if($agleveldata2) $ogupdate['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $commission_totalprice * 0.01;
                            if($agleveldata3) $ogupdate['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $commission_totalprice * 0.01;
                        }
                    }elseif($product['commissionset']==2){//按固定金额
                        $commissiondata = json_decode($product['commissiondata2'],true);
                        if($commissiondata){
                            if(getcustom('fengdanjiangli') && $product['fengdanjiangli']){

                            }else{
                                if($agleveldata1) $ogupdate['parent1commission'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                                if($agleveldata2) $ogupdate['parent2commission'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                                if($agleveldata3) $ogupdate['parent3commission'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                            }
                        }
                    }elseif($product['commissionset']==3){//提成是积分
                        $commissiondata = json_decode($product['commissiondata3'],true);
                        if($commissiondata){
                            if($agleveldata1) $ogupdate['parent1score'] = $commissiondata[$agleveldata1['id']]['commission1'] * $num;
                            if($agleveldata2) $ogupdate['parent2score'] = $commissiondata[$agleveldata2['id']]['commission2'] * $num;
                            if($agleveldata3) $ogupdate['parent3score'] = $commissiondata[$agleveldata3['id']]['commission3'] * $num;
                        }
                    }else{ //按会员等级设置的分销比例
                        if($agleveldata1){
                            if(getcustom('plug_ttdz') && $ogdata['isfg'] == 1){
                                $agleveldata1['commission1'] = $agleveldata1['commission4'];
                            }
                            if($agleveldata1['commissiontype']==1){ //固定金额按单
                                if($istc1==0){
                                    $ogupdate['parent1commission'] = $agleveldata1['commission1'];
                                    $istc1 = 1;
                                }
                            }else{
                                $ogupdate['parent1commission'] = $agleveldata1['commission1'] * $commission_totalprice * 0.01;
                            }
                        }
                        if($agleveldata2){
                            if(getcustom('plug_ttdz') && $ogdata['isfg'] == 1){
                                $agleveldata2['commission2'] = $agleveldata2['commission5'];
                            }
                            $ogupdate['fhparent2commission'] = $ogupdate['parent1commission']*$agleveldata2['fh_commission2']%10;
                            if($agleveldata2['commissiontype']==1){
                                if($istc2==0){
                                    $ogupdate['parent2commission'] = $agleveldata2['commission2'];
                                    $istc2 = 1;
                                    //持续推荐奖励
                                    if($agleveldata2['commission_parent'] > 0) {
                                        $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $agleveldata2['commission_parent'];
                                    }
                                    if($agleveldata1['id'] == $agleveldata2['id'] && $agleveldata2['commission_parent_pj'] > 0) {
                                        $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $agleveldata2['commission_parent_pj'];
                                    }
                                }
                            }else{
                                $ogupdate['parent2commission'] = $agleveldata2['commission2'] * $commission_totalprice * 0.01;
                                //持续推荐奖励
                                if($agleveldata2['commission_parent'] > 0 && $ogupdate['parent1commission'] > 0) {
                                    $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $ogupdate['parent1commission'] * $agleveldata2['commission_parent'] * 0.01;
                                }
                                if($agleveldata1['id'] == $agleveldata2['id'] && $agleveldata2['commission_parent_pj'] > 0 && $ogupdate['parent1commission'] > 0) {
                                    $ogupdate['parent2commission'] = $ogupdate['parent2commission'] + $ogupdate['parent1commission'] * $agleveldata2['commission_parent_pj'] * 0.01;
                                }
                            }
                        }
                        if($agleveldata3){
                            if(getcustom('plug_ttdz') && $ogdata['isfg'] == 1){
                                $agleveldata3['commission3'] = $agleveldata3['commission6'];
                            }
                            $ogupdate['fhparent3commission'] = $ogupdate['parent1commission']*$agleveldata2['fh_commission3']%10;
                            if($agleveldata3['commissiontype']==1){
                                if($istc3==0){
                                    $ogupdate['parent3commission'] = $agleveldata3['commission3'];
                                    $istc3 = 1;
                                    //持续推荐奖励
                                    if($agleveldata3['commission_parent'] > 0) {
                                        $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $agleveldata3['commission_parent'];
                                    }
                                    if($agleveldata2['id'] == $agleveldata3['id'] && $agleveldata3['commission_parent_pj'] > 0) {
                                        $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $agleveldata3['commission_parent_pj'];
                                    }
                                }
                            }else{
                                $ogupdate['parent3commission'] = $agleveldata3['commission3'] * $commission_totalprice * 0.01;
                                //持续推荐奖励
                                if($agleveldata3['commission_parent'] > 0 && $ogupdate['parent2commission'] > 0) {
                                    $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $ogupdate['parent2commission'] * $agleveldata3['commission_parent'] * 0.01;
                                }
                                if($agleveldata2['id'] == $agleveldata3['id'] && $agleveldata3['commission_parent_pj'] > 0 && $ogupdate['parent2commission'] > 0) {
                                    $ogupdate['parent3commission'] = $ogupdate['parent3commission'] + $ogupdate['parent2commission'] * $agleveldata3['commission_parent_pj'] * 0.01;
                                }
                            }
                        }
                        if($agleveldata4){
                            $ogupdate['fhparent4commission'] = $ogupdate['parent1commission']*$agleveldata2['fh_commission4']%10;
                        }
                        //持续推荐奖励
                        if($agleveldata4['commission_parent'] > 0) {
                            if($agleveldata3['commissiontype']==1){
                                $ogupdate['parent4commission'] = $agleveldata4['commission_parent'];
                            } else {
                                $ogupdate['parent4commission'] = $ogupdate['parent3commission'] * $agleveldata4['commission_parent'] * 0.01;
                            }
                        }
                        if($agleveldata3['id'] == $agleveldata4['id'] && $agleveldata4['commission_parent_pj'] > 0) {
                            if($agleveldata3['commissiontype']==1){
                                $ogupdate['parent4commission'] = $agleveldata4['commission_parent_pj'];
                            } else {
                                $ogupdate['parent4commission'] = $ogupdate['parent3commission'] * $agleveldata4['commission_parent_pj'] * 0.01;
                            }
                        }
                    }
                }
                
                if($ogupdate){
                    Db::name('shop_order_goods')->where('id',$ogdata['id'])->update($ogupdate);
                }

                if($product['commissionset4']==1 && $product['lvprice']==1){ //极差分销

                    if($member['path']){
                        $parentList = Db::name('member')->where('id','in',$member['path'])->order(Db::raw('field(id,'.$member['path'].')'))->select()->toArray();
                        if($parentList){
                            $parentList   = array_reverse($parentList);
                            $lvprice_data = json_decode($guige['lvprice_data'],true);
                            $nowprice     = $commission_totalpriceCache;
                            $giveidx      = 0;
                            foreach($parentList as $k=>$parent){
                                if($parent['levelid'] && $lvprice_data[$parent['levelid']]){
                                    $thisprice = floatval($lvprice_data[$parent['levelid']]) * $num;
                                    if($nowprice > $thisprice){
                                        $commission = $nowprice - $thisprice;
                                        $nowprice = $thisprice;
                                        $giveidx++;
                                        //添加新的
                                        Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$parent['id'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$commission,'score'=>0,'remark'=>'下级购买商品差价','createtime'=>time()]);
                                    }
                                }
                            }
                        }
                    }
                }
                
                if($parent2 && $ogupdate['fhparent2commission']>0){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'jl_type'=>2,'mid'=>$parent2['id'],'frommid'=>$og['mid'],'orderid'=>$orderid,'ogid'=>$og['id'],'type'=>'shop','commission'=>$ogupdate['fhparent2commission'],'remark'=>'下一级收入分红','createtime'=>time()]);
                        }
                        if($parent3 && $ogupdate['fhparent3commission']>0){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'jl_type'=>2,'mid'=>$parent3['id'],'frommid'=>$og['mid'],'orderid'=>$orderid,'ogid'=>$og['id'],'type'=>'shop','commission'=>$ogupdate['fhparent3commission'],'remark'=>'下二级收入分红','createtime'=>time()]);
                        }
                        if($parent4 && $ogupdate['fhparent4commission']>0){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'jl_type'=>2,'mid'=>$parent4['id'],'frommid'=>$og['mid'],'orderid'=>$orderid,'ogid'=>$og['id'],'type'=>'shop','commission'=>$ogupdate['fhparent4commission'],'remark'=>'下三级收入分红','createtime'=>time()]);
                        }
                
                if($product['commissionset']!=4){
                    if(getcustom('plug_ttdz') && $ogdata['isfg'] == 1){
                        if($ogupdate['parent1'] && ($ogupdate['parent1commission'] || $ogupdate['parent1score'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent1'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent1commission'],'score'=>$ogupdate['parent1score'],'remark'=>'下级复购奖励','createtime'=>time()]);
                        }
                        if($ogupdate['parent2'] && ($ogupdate['parent2commission'] || $ogupdate['parent2score'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent2'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent2commission'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级复购奖励','createtime'=>time()]);
                        }
                        if($ogupdate['parent3'] && ($ogupdate['parent3commission'] || $ogupdate['parent3score'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent3'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent3commission'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级复购奖励','createtime'=>time()]);
                        }
                    }else{
                        if($ogupdate['parent1'] && ($ogupdate['parent1commission'] || $ogupdate['parent1score'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent1'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent1commission'],'score'=>$ogupdate['parent1score'],'remark'=>'下级购买商品奖励','createtime'=>time()]);
                        }
                        if($ogupdate['parent2'] && ($ogupdate['parent2commission'] || $ogupdate['parent2score'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent2'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent2commission'],'score'=>$ogupdate['parent2score'],'remark'=>'下二级购买商品奖励','createtime'=>time()]);
                        }
                        if($ogupdate['parent3'] && ($ogupdate['parent3commission'] || $ogupdate['parent3score'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent3'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent3commission'],'score'=>$ogupdate['parent3score'],'remark'=>'下三级购买商品奖励','createtime'=>time()]);
                        }
                        if($ogupdate['parent4'] && ($ogupdate['parent4commission'])){
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$ogupdate['parent4'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$ogupdate['parent4commission'],'score'=>0,'remark'=>'持续推荐奖励','createtime'=>time()]);
                        }
                    }
                    if($order['checkmemid'] && $commission_totalprice > 0){
                        $checkmember = Db::name('member')->where('aid',$aid)->where('id',$order['checkmemid'])->find();
                        if($checkmember){
                            $buyselect_commission = Db::name('member_level')->where('id',$checkmember['levelid'])->value('buyselect_commission');
                            $checkmemcommission = $buyselect_commission * $commission_totalprice * 0.01;
                            Db::name('member_commission_record')->insert(['aid'=>$aid,'mid'=>$checkmember['id'],'frommid'=>$ogdata['mid'],'orderid'=>$ogdata['orderid'],'ogid'=>$ogdata['id'],'type'=>'shop','commission'=>$checkmemcommission,'score'=>0,'remark'=>'购买商品时指定奖励','createtime'=>time()]);
                        }
                    }
                }

                if(getcustom('everyday_hongbao')) {

                    $hd = Db::name('hongbao_everyday')->where('aid', $aid)->find();
                    $hongbaoEdu = 0;
                    if($product['everyday_hongbao_bl'] === null) {
                        $hongbaoEdu = $og_totalprice * $hd['shop_product_hongbao_bl'] / 100;
                    } elseif($product['everyday_hongbao_bl'] > 0 ) {
                        $hongbaoEdu = $og_totalprice * $product['everyday_hongbao_bl'] / 100;
                    }
                    $hongbaoEdu = round($hongbaoEdu,2);
                    if($hongbaoEdu > 0){
                        Db::name('shop_order_goods')->where('id',$ogupdate['id'])->update(['hongbaoEdu' => $hongbaoEdu]);
                    }
                }
            }
            unset($sg_v);
        }

    }
    public static function cashier_pay($orderid){
	    //如果是多商家且余额支付，则结算商家费用
        $order = Db::name('cashier_order')->where('id',$orderid)->find();
        $totalmoney = $order['totalprice'];
        if($order['paytypeid']==1 && $order['bid']>0 && $totalmoney>0){
            //商家费率
            $feepercent = Db::name('business')->where('aid',$order['aid'])->where('id',$order['bid'])->value('feepercent');
            if($feepercent){
                $totalmoney = $totalmoney * (100-$feepercent) * 0.01;
            }
            //结算给商家
            $res = \app\common\Business::addmoney($order['aid'],$order['bid'],$totalmoney,'收银台收款');
            if(!$res || $res['status']!=1){
              
            }
        }
	    return true;
    }
    public static function dscj_pay($orderid){
        $order = Db::name('dscj_order')->where('id',$orderid)->find();
        //其他订单关闭
        $where = [];
        $where[] = ['status','=',0];
        $where[] = ['bid','=',$order['bid']];
        $where[] = ['aid','=',$order['aid']];
        $where[] = ['hid','=',$order['hid']];
        $where[] = ['mid','=',$order['mid']];
        Db::name('dscj_order')->where($where)->update(['status'=>4]);
        Db::name('dscj')->where('id',$order['hid'])->inc('joinnum',1)->update();
        return true;
    }
    //招聘置顶
    public static function zhaopin_top_pay($orderid){
	    if(getcustom('zhaopin')){
            $order = Db::name('zhaopin_top_order')->where('id',$orderid)->find();
            $zhaopinupdate  = [];
            if($order['status']==1 && $order['related_id']){
                $starttime = $order['paytime'];
                $endtimeS = $order['paytime'];
                $duration = $order['top_duration'];
                $durationTotal = 86400 * $duration;
                $relateinfo = Db::name('zhaopin')->where('aid',$order['aid'])->where('id',$order['related_id'])->find();
                if($relateinfo){
                    if($relateinfo['top_endtime'] && $relateinfo['top_endtime']>time()){
                        $starttime = $relateinfo['top_starttime'];
                        $endtimeS = $relateinfo['top_endtime'];
                    }
                    $zhaopinupdate['top_starttime'] = $starttime;
                    $zhaopinupdate['top_endtime'] = $endtimeS + $durationTotal;
                    $zhaopinupdate['top_feetype'] = $order['top_feetype'];
                    $zhaopinupdate['top_area'] = $order['top_area'];
//                    Db::name('zhaopin')->where('id',$order['related_id'])->update($zhaopinupdate);
                }
            }
            if($order['assurance_total']>0){
                //同步保证金订单
                $ordera = [];
                $ordera['ordernum'] = $order['ordernum'];
                $ordera['top_orderid'] = $order['id'];
                $ordera['totalprice'] = $order['assurance_total'];
                $ordera['createtime'] = time();
                $ordera['title'] = '担保招聘保证金';
                $ordera['aid'] = $order['aid'];
                $ordera['bid'] = $order['bid'];
                $ordera['mid'] = $order['mid'];
                $ordera['apply_id'] = $order['apply_id'];
                $ordera['status'] = 1;
                $ordera['paynum'] = $order['paynum'];
                $ordera['paytype'] = $order['paytype'];
                $ordera['paytime'] = $order['paytime'];
                Db::name('zhaopin_assurancefee_order')->insertGetId($ordera);

                //保证金累加
                Db::name('zhaopin_apply')->where('aid',$order['aid'])->where('id',$order['apply_id'])->update(['assurance_fee'=>Db::raw("assurance_fee+{$order['assurance_total']}")]);

                $data = [];
                $data['createtime'] = time();
                $data['aid'] = $order['aid'];
                $data['bid'] = $order['bid'];
                $data['mid'] = $order['mid'];
                $data['apply_id'] = $order['apply_id'];
                $data['zhaopin_id'] = $order['related_id'];
                $data['status'] = 1;//担保中
                $data['fee'] =  $order['assurance_total'];
                $data['remark'] =  '置顶担保招聘';
                $assurance_id = Db::name('zhaopin_assurance')->insertGetId($data);
                $zhaopinupdate['assurance_id'] = $assurance_id;
            }
            if($order['totalprice']>0){
                //发放奖励
                $givescore = \app\model\Zhaopin::getSetValue($order['aid'],'zhaopin','top_give_score',0);
                if($givescore>0){
                    \app\common\Member::addscore($order['aid'],$order['mid'],$givescore,'招聘置顶奖励');
                }
            }
            if($zhaopinupdate){
                Db::name('zhaopin')->where('id',$order['related_id'])->update($zhaopinupdate);
            }
        }
        return true;
    }

    //vip
    public static function zhaopin_vip_pay($orderid){
        if(getcustom('zhaopin')){
            //其他未支付的订单删除
            $order = Db::name('zhaopin_vip_order')->where('id',$orderid)->find();
            Db::name('zhaopin_vip_order')->where('status',0)->where('mid',$order['mid'])->where('zhaopin_id',$order['zhaopin_id'])->delete();
            Db::name('zhaopin')->where('aid',$order['aid'])->where('id',$order['zhaopin_id'])->update(['vip_orderid'=>$orderid]);
            //vip_order
            Db::name('zhaopin_apply')->where('aid',$order['aid'])->where('mid',$order['mid'])->update(['vip_orderid'=>$orderid,'zhaopin_id'=>$order['zhaopin_id']]);
            return;
        }
        return true;
    }

    //求职置顶
    public static function zhaopin_qiuzhi_top_pay($orderid){
        if(getcustom('zhaopin')) {
            $order = Db::name('zhaopin_qiuzhi_top_order')->where('id', $orderid)->find();
            if ($order['status'] == 1 && $order['related_id']) {
                $starttime = $order['paytime'];
                $endtimeS = $order['paytime'];
                $duration = $order['top_duration'];
                $durationTotal = 86400 * ($duration + 1);
                $relateinfo = Db::name('zhaopin_qiuzhi')->where('aid', $order['aid'])->where('id', $order['related_id'])->find();
                if ($relateinfo) {
                    if ($relateinfo['top_endtime'] && $relateinfo['top_endtime']>time()) {
                        $starttime = $relateinfo['top_starttime'];
                        $endtimeS = $relateinfo['top_endtime'];
                    }
                    $update['top_starttime'] = $starttime;
                    $update['top_endtime'] = $endtimeS + $durationTotal;
                    $update['top_feetype'] = $order['top_feetype'];
                    $update['top_area'] = $order['top_area'];
                    Db::name('zhaopin_qiuzhi')->where('id', $order['related_id'])->update($update);
                }
            }
            //发放奖励
            $givescore = \app\model\Zhaopin::getSetValue($order['aid'],'qiuzhi','top_give_score',0);
            if($givescore>0){
                \app\common\Member::addscore($order['aid'],$order['mid'],$givescore,'求职置顶奖励');
            }
        }
        return true;
    }
    //招聘置顶
    public static function zhaopin_assurancefee_pay($orderid){
        if(getcustom('zhaopin')){
            $order = Db::name('zhaopin_assurancefee_order')->where('id',$orderid)->find();
            if($order['apply_id']){
                //更新商家缴纳保证金的费用
                Db::name('zhaopin_apply')->where('aid',$order['aid'])->where('id',$order['apply_id'])->inc('assurance_fee',$order['totalprice'])->update();
            }
        }
        return true;
    }

    /**
     * @param $orderid
     * @param $type
     * @param $usecoupon_type 1付款后，2确认收货
     * @param $ordernum
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function afterusecoupon($orderid,$type,$usecoupon_type=1,$ordernum='')
    {
        //所有使用优惠券的订单类型maidan = couponrid
        if(getcustom('usecoupon_give_score') || getcustom('usecoupon_give_coupon') || getcustom('usecoupon_give_money')){
            if(in_array($type,[
                'shop','shop_hb','yuyue','tuangou','seckill2','seckill',
                'restaurant_takeaway','restaurant_shop','restaurant_booking',
                'maidan','lucky_collage','dscj','collage','cashier'
            ])){
                $couponOrders = [];//优惠券字段 全部转为coupon_rid
                //特殊处理的表
                if($type=='shop_hb'){
                    $couponOrders = Db::name('shop_order')->where('ordernum','like',$ordernum.'%')->select()->toArray();
                }elseif($type=='maidan'){
                    $couponOrders[] = Db::name('maidan_order')->where('id',$orderid)->field('*,couponrid coupon_rid')->find();
                }else{
                    $couponOrders[] = Db::name($type.'_order')->where('id',$orderid)->find();
                }
                if(empty($couponOrders)){
                    return true;
                }
                foreach ($couponOrders as $key=>$order){
                    if(!isset($order['coupon_rid']) || empty($order['coupon_rid'])){
                        continue;
                    }
                    \app\common\Coupon::useCoupon($order['aid'],$order['coupon_rid'],$usecoupon_type);
                }
            }
        }

        return true;
    }

    public static function send_free_notice($payorder){

    	if(getcustom('invite_free')){
    		//查询免单设置
            $set = Db::name('invite_free')->where('aid',$payorder['aid'])->find();
    		if($set && $set['status'] ==1 && $set['start_time']<=time() && $set['end_time']>=time() && $payorder['aid'] && $payorder['aid'] == 10){
    			//查询订单
		        $order = Db::name('shop_order')->where('id',$payorder['orderid'])->field('id,ordernum,mid,title,totalprice,mdid')->find();
		        if($order){
		        	//查询他下单次数
		        	$count_num = Db::name('shop_order')
		        		->where('mid',$order['mid'])
		        		->where('status','>=',1)
		        		->where('status','<=',3)
		        		->count();
		        	if($count_num == 1){
		        		//查询他上级
		        		$member = Db::name('member')
		        			->where('id',$order['mid'])
		        			->field('pid')
		        			->find();
		        		if($member && $member['pid']>0){

							// $tmplcontent['keyword1'] = Db::name('admin_set')->where('aid',aid)->value('name'); //店铺
							// $tmplcontent['thing27'] = $this->member['nickname'];

		        			//查询通知设置
				        	$mp_tmplset = Db::name('mp_tmplset')->where('aid',$payorder['aid'])->field('tmpl_orderconfirm')->find();
				        	if($mp_tmplset && $mp_tmplset['tmpl_orderconfirm']){
				        		//公众号通知 发送有新伙伴下单
								$tmplcontent = [];
								$tmplcontent['first']    = '有新伙伴下单';
								$tmplcontent['remark']   = '点击进入查看~';
								$tmplcontent['keyword1'] = ''; //店铺
								$tmplcontent['keyword2'] = date('Y-m-d H:i:s',$payorder['paytime']);//下单时间
								$tmplcontent['keyword3'] = $order['title']?$order['title']:'';//商品
								$tmplcontent['keyword4'] = $order['totalprice']?$order['totalprice']:'';//金额
								\app\common\Wechat::sendtmpl($payorder['aid'],$member['pid'],'tmpl_orderconfirm',$tmplcontent,m_url('pagesExt/invite_free/index'));
				        	}

				        	//查询通知设置
				        	$wx_tmplset = Db::name('wx_tmplset')->where('aid',$payorder['aid'])->field('tmpl_orderconfirm')->find();
				        	if($wx_tmplset && $wx_tmplset['tmpl_orderconfirm']){
								$tmplcontent = [];
								$tmplcontent['thing11']  = $order['title']?$order['title']:'';//商品
								$tmplcontent['character_string2'] = $order['ordernum']?$order['ordernum']:'';
								$tmplcontent['phrase10'] = '新伙伴下单';
								$tmplcontent['amount13'] =  $order['totalprice']?$order['totalprice']:'';//金额
								$tmplcontent['thing27']  = '';
								\app\common\Wechat::sendwxtmpl($payorder['aid'],$member['pid'],'tmpl_orderconfirm',$tmplcontent,m_url('pagesExt/invite_free/index'),$order['mdid']);
							}
		        		}
		        	}
		        }
    		}

	    }
    }

    public static function xixie_pay($orderid){
        if(getcustom('xixie')){
            $order = Db::name('shop_order')->where('id',$orderid)->find();
            $member = Db::name('member')->where('id',$order['mid'])->find();
            $aid = $order['aid'];
            $mid = $order['mid'];
            Db::name('shop_order_goods')->where('orderid',$orderid)->update(['status'=>1]);
        }
    }
    
 // 秒杀订单手续费
public static function miaoshasxf_pay($orderid)
{
    $order = Db::name('miaoshasxf_order')->where('id', $orderid)->find();
    if (!empty($order)) {
        $shop_order_id = $order['orderid'];
        $aid = $order['aid'];
        $mid = $order['mid'];
        $bid = $order['bid'];

        // 获取系统设置，包括 miaosha_zengsonggxz
        $admin_set = Db::name('miaosha_sysset')->where('aid', $aid)->find();

        // 获取商品信息
        $product = Db::name('miaosha_product')->where('aid', $aid)->where('id', $shop_order_id)->find();
        $insertdata = [];

        // 计算利润
        if ($admin_set['miaosha_lirun'] > 0) {
            $miaosha_lirun = $product['sell_price'] * $admin_set['miaosha_lirun'] * 0.01;
        } else {
            $miaosha_lirun = 0;
        }

        // 计算价格
        if ($admin_set['iszhang'] == 2) {
            // 虚拟支付价格 + 手续费 + 用户利润
            $price = $product['xuni_price'] + $miaosha_lirun + $order['money'];
        } else {
            $price = $product['sell_price'] + $product['sell_price'] * $admin_set['miaosha_zhangfu'] * 0.01;
        }
        $price = round($price, 2);

        // 准备插入数据
        $insertdata['ordernum'] = 'DB' . date('YmdHis') . uniqid();
        $insertdata['aid'] = $aid;
        $insertdata['status'] = 1;
        $insertdata['status2'] = 2;
        $insertdata['prostatus2'] = 2;
        $insertdata['sxf'] = $order['money'];
        $insertdata['price'] = $price;
        $insertdata['changci'] = 1;

        // 修改部分：使用商品的 changciid，如果没有则使用 admin_set 的
        if (!empty($product['changciid'])) {
            $insertdata['changciid'] = $product['changciid'];
        } else {
            $insertdata['changciid'] = $admin_set['miaosha_changciid'];
        }

        $insertdata['bid'] = $bid;
        $insertdata['outid'] = $mid;
        $insertdata['orderid'] = $shop_order_id;
        $insertdata['goodsid'] = $shop_order_id;
        $insertdata['goodcate'] = $product['cid'];
        $insertdata['zhangfu'] = $admin_set['miaosha_zhangfu'];
        $insertdata['addtime'] = date('Y-m-d H:i:s');
        $insertdata['updatetime'] = date('Y-m-d H:i:s');
        $insertdata['memberids'] = '';
        $insertdata['tiqian'] = 0;
        $insertdata['tiqiandetail'] = [];
        $insertdata['zhangprice'] = $product['sell_price'] * $admin_set['miaosha_zhangfu'] * 0.01;
        $id = Db::name('miaosha')->insertGetId($insertdata);

        // 更新商品信息
        Db::name('miaosha_product')->where('id', $insertdata['goodsid'])->update(['sell_price' => $price, 'status2' => 2]);

        // 添加利润记录
        $insertdata2 = [];
        $insertdata2['aid'] = $aid;
        $insertdata2['mid'] = $mid;
        $insertdata2['lirun'] = $miaosha_lirun;
        $insertdata2['sell_price'] = $product['sell_price'];
        $insertdata2['zhang_price'] = $product['sell_price'] * $admin_set['miaosha_zhangfu'] * 0.01;
        $insertdata2['sxf'] = $order['money'];

        // 修改部分：使用商品的 changciid，如果没有则使用 admin_set 的
        if (!empty($product['changciid'])) {
            $insertdata2['changciid'] = $product['changciid'];
        } else {
            $insertdata2['changciid'] = $admin_set['miaosha_changciid'];
        }

        $insertdata2['goodsid'] = $shop_order_id;
        $insertdata2['addtime'] = date('Y-m-d H:i:s');
        $id = Db::name('miaosha_lirun')->insertGetId($insertdata2);

        // 给上级佣金
        $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->field('pid')->find();
        if ($member['pid'] > 0) {
            $miaoshasysset = Db::name('miaosha_sysset')->where('aid', $aid)->field('miaosha_zhitui')->find();
            if ($miaoshasysset['miaosha_zhitui'] > 0) {
                $totalprice_daozhang = $product['sell_price'] * $miaoshasysset['miaosha_zhitui'] * 0.01;
                $totalprice_daozhang = round($totalprice_daozhang, 2);
                \app\common\Member::addcommission($aid, $member['pid'], $mid, $totalprice_daozhang, '我的下级委托上架佣金');
            }
        }

        // 扣除收益池金额
        if ($miaosha_lirun > 0) {
            \app\common\Member::addsyc($aid, $mid, -$miaosha_lirun, '扣除收益池金额' . $miaosha_lirun, 0);
        }

        // 添加贡献值
        if ($admin_set['miaosha_zengsonggxz'] > 0) {
            // 计算贡献值：手续费 * 比例
            $gongxianzhi = $order['money'] * $admin_set['miaosha_zengsonggxz'] * 0.01;
            $gongxianzhi = round($gongxianzhi, 2);

            // 调用添加贡献值的方法
            \app\common\Member::addgongxianzhi($aid, $mid, $gongxianzhi, '支付手续费赠送' . t('贡献值'));
        }
    }
}



    //充值订单
    public static function xixie_vip_pay($orderid){
        if(getcustom('xixie')){
            $order = Db::name('xixie_vip_order')->where('id',$orderid)->find();
            $member = Db::name('member')->where('id',$order['mid'])->update(['is_vip'=>1]);
        }
    }

    public static function article_reward_pay($orderid){
        if(getcustom('article_reward')){
            $order = Db::name('article_reward_order')->where('id',$orderid)->find();
            if($order){
                //增加打赏
                \app\common\Member::addmoney($order['aid'],$order['send_mid'],$order['num'],'文章打赏');
            }
        }
    }



    
	//更新商户销量
	public static function addSales($orderid,$type,$aid,$bid=0,$sale_num=0){
        $sales_type = [
            'sales' => 'sales',//虚拟销量
            'shop' => 'shop_sales',//普通商铺
            'collage' => 'collage_sales',//多人拼团
            'kanjia' => 'kanjia_sales',//砍价
            'seckill' => 'seckill_sales',//秒杀
            'tuangou' => 'tuangou_sales',//团购
            'scoreshop' => 'scoreshop_sales',//积分商城
            'lucky_collage' => 'lucky_collage_sales',//幸运拼团
            'yuyue' => 'yuyue_sales',//预约服务
            'kecheng' => 'kecheng_sales',//课程
            'cycle' => 'cycle_sales',//周期购
            'restaurant_takeaway' => 'restaurant_takeaway_sales',//餐饮外卖
            'restaurant_shop' => 'restaurant_shop_sales',//餐饮点餐
            'maidan' => 'maidan_sales'//买单
        ];
        if(!empty($sales_type[$type])){
            if($sale_num==0 && $orderid){
                switch ($type){
                    case 'shop':
                        $sale_num = Db::name('shop_order_goods')->where('orderid',$orderid)->sum('num');
                        break;
                    case 'collage':
                        $sale_num = Db::name('collage_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'kanjia':
                        $sale_num = Db::name('kanjia_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'seckill':
                        $sale_num = Db::name('seckill_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'tuangou':
                        $sale_num = Db::name('tuangou_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'scoreshop':
                        $sale_num = Db::name('scoreshop_order_goods')->where('orderid',$orderid)->sum('num');
                        break;
                    case 'lucky_collage':
                        $sale_num = Db::name('lucky_collage_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'yuyue':
                        $sale_num = Db::name('yuyue_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'kecheng':
                        $sale_num = 1;
                        break;
                    case 'cycle':
                        $sale_num = Db::name('cycle_order')->where('id',$orderid)->sum('num');
                        break;
                    case 'restaurant_takeaway':
                        $sale_num = Db::name('restaurant_takeaway_order_goods')->where('orderid',$orderid)->sum('num');
                        break;
                    case 'restaurant_shop':
                        $sale_num = Db::name('restaurant_shop_order_goods')->where('orderid',$orderid)->sum('num');
                        break;
                    case 'maidan':
                        $sale_num = 1;
                        break;
                }
            }

            $sales_field = $sales_type[$type];
            $business_sales = Db::name('business_sales')
                ->where('aid',$aid)
                ->where('bid',$bid)
                ->find();
            if(!$business_sales && $sale_num>0){
                $data_sales = [];
                $data_sales['aid'] = $aid;
                $data_sales['bid'] = $bid;
                $data_sales[$sales_field] = $sale_num;
                $data_sales['total_sales'] = $sale_num;
                Db::name('business_sales')->insert($data_sales);
            }else{
                $data_sales = [];
                $data_sales[$sales_field] = $business_sales[$sales_field]+$sale_num;
                $data_sales['total_sales'] = $business_sales['total_sales']+$sale_num;
                Db::name('business_sales')->where('id',$business_sales['id'])->update($data_sales);
            }

        }
    }



}
